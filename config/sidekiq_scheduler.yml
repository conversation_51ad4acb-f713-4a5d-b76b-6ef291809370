remove_old_feed_entries:
  description: "Invalidating and deleting posts of no current relevance in elastic search"
  cron: '0 3 * * * Asia/Kolkata'
  class: RemoveOldFeedEntries

searchkick_batch_indexing:
  description: "Enqueueing searchkick batch indexing every 1 minute"
  every: '1m'
  class: SearchkickBatchIndex

trigger_poster_slack_notification:
  description: "Sends a slack notification at 10PM about posters enabled for tomorrow"
  cron: '0 22 * * * Asia/Kolkata'
  class: TriggerPosterSlackNotification

process_post_views_batch:
  description: "Process post views batch"
  cron: '*/5 * * * * Asia/Kolkata'
  class: ProcessPostViewsBatch

process_poster_creative_views_batch:
  description: "Process poster creative views batch"
  cron: '*/5 * * * * Asia/Kolkata'
  class: ProcessPosterCreativeViewsBatch

process_frame_views_batch:
  description: "Process frame views batch"
  cron: '*/5 * * * * Asia/Kolkata'
  class: ProcessFrameViewsBatch

cron_to_follow_owner_after_owner_assigned_to_circle:
  description: "All members of circle to follow owner of the circle starts at 1:00 AM"
  cron: '0 1 * * * Asia/Kolkata'
  class: CronToFollowOwnerAfterOwnerAssignedToCircle

cron_for_pre_cache_post_ids_for_trending_feed:
  description: "To pre cache post ids for trending feed"
  cron: '*/30 * * * * Asia/Kolkata'
  class: CronForPreCachePostIdsForTrendingFeed

update_users_following_followers_count:
  description: "To update users' followers & following count every 10 minutes"
  cron: '*/10 * * * * Asia/Kolkata'
  class: UpdateUsersFollowingFollowersCount

update_circles_members_count:
  description: "To update circles' members count every 30 minutes"
  cron: '26,56 * * * * Asia/Kolkata'
  class: UpdateCirclesMembersCount

update_hashtag_likes_and_opinions_count:
  description: "To update hashtags' likes & opinions count at 1:30 AM"
  cron: '30 1,13 * * * Asia/Kolkata'
  class: UpdateHashtagLikesAndOpinionsCount

cron_for_old_user_cache_flush:
  description: "Clearing old user cache for 10000 users every 5 minutes"
  cron: '*/5 * * * * Asia/Kolkata'
  class: CronForOldUserCacheFlush

create_consecutive_circle_monthly_usages:
  description: "Cron to create consecutive circle monhtly usages for the existing usages"
  cron: '0 0 * * * Asia/Kolkata'
  class: CreateConsecutiveCircleMonthlyUsages

create_new_circle_monthly_usages:
  description: "Cron to create new circle monthly usages for the mappings became active on the day"
  cron: '0 0 * * * Asia/Kolkata'
  class: CreateNewCircleMonthlyUsages

update_user_trial_end_dates:
  description: "Cron to update user trial end dates"
  cron: '0 0 * * * Asia/Kolkata'
  class: UpdateUserTrialEndDates

cron_for_verifying_charges_created_and_sent:
  description: "Cron for verifying charges created and sent"
  cron: '0 17 * * * Asia/Kolkata'
  class: CronForVerifyingChargesCreatedAndSent

cron_for_charges_recon:
  description: "Cron for charges recon"
  cron: '0 4 * * * Asia/Kolkata'
  class: CronForChargesRecon

#create_renewal_order_cron:
#  description: "Cron to create renewal order for the users whose trial is ended at 12AM"
#  cron: '0 0 * * * Asia/Kolkata'
#  class: CreateRenewalOrderCron

poster_trial_extension_started_event_cron:
  description: "Cron to send poster trial extension started event to mixpanel at 12AM"
  cron: '0 0 * * * Asia/Kolkata'
  class: PosterTrialExtensionStartedEventCron

#poster_premium_extension_started_event_cron:
#  description: "Cron to send poster premium extension started event to mixpanel at 12AM"
#  cron: '0 0 * * * Asia/Kolkata'
#  class: PosterPremiumExtensionStartedEventCron

#poster_premium_extension_ended_event_cron:
#  description: "Cron to send poster premium extension ended event to mixpanel at 12AM"
#  cron: '0 0 * * * Asia/Kolkata'
#  class: PosterPremiumExtensionEndedEventCron

cron_for_qualified_leads:
  description: "Cron for qualified leads at 12AM"
  cron: '0 0 * * * Asia/Kolkata'
  class: CronForQualifiedLeads

cron_for_creating_subscription_charges:
  description: "Cron for charging subscription at 2:30PM"
  cron: '30 14 * * * Asia/Kolkata'
  class: CronForCreatingSubscriptionCharges

#Should change the Subscription::TIME_FOR_RAISING_CHARGES_IN_HOURS value if cron_for_sending_charges_to_pg
#cron execution time is changing as
#retry charges creation logic is based on this value(cron_for_sending_charges_to_pg time hours),
cron_for_sending_charges_to_pg:
  description: "Cron for sending charges to payment gateway at 3PM"
  cron: '0 15 * * * Asia/Kolkata'
  class: CronForSendingChargesToPg

user_mixpanel_event_after_plan_expires:
  description: "Cron to send user mixpanel event after plan expires at 2 30AM"
  cron: '30 2 * * * Asia/Kolkata'
  class: UserMixpanelEventAfterPlanExpires

user_mixpanel_event_after_grace_period_expires:
  description: "Cron to send user mixpanel event after grace period expires at 2 30AM"
  cron: '30 2 * * * Asia/Kolkata'
  class: UserMixpanelEventAfterGracePeriodExpires

update_lead_score_cron_based_on_last_online:
  description: "Cron to update lead score based on last online for every 5 minutes"
  cron: '*/5 * * * * Asia/Kolkata'
  class: UpdateLeadScoreCronBasedOnLastOnline

update_lead_score_cron_based_on_last_trial_attempt:
  description: "Cron to update lead score based on last trial attempt for every 5 minutes"
  cron: '*/5 * * * * Asia/Kolkata'
  class: UpdateLeadScoreCronBasedOnLastTrialAttempt

remove_users_from_special_offer_event:
  description: "Cron to run everyday at 2AM for removing the users from special offer"
  cron: '0 2 * * * Asia/Kolkata'
  class: RemoveUsersFromSpecialOfferEvent

cron_to_verify_crm_leads:
  description: "Cron to run everyday at 12 30AM to send the user ids to slack(#api-alerts) channel, whose status is interested and crm_stage is empty"
  cron: '30 0 * * * Asia/Kolkata'
  class: CronToVerifyCrmLeads

generate_poster_images_for_campaign_users:
  description: "Cron to generate wati campaign poster photos at 2AM"
  cron: '0 2 * * * Asia/Kolkata'
  class: GeneratePosterImagesForCampaignUsers

send_missed_sunday_leads_to_floww_cron:
  description: "Cron to send missed Sunday leads to Floww CRM at 6AM daily"
  cron: '0 6 * * * Asia/Kolkata'
  class: SendMissedSundayLeadsToFlowwCron

generate_poster_creative_for_trail_activation_campaign:
  description: "Cron to generate wati campaign poster photos at 2AM"
  cron: '0 2 * * * Asia/Kolkata'
  class: GenerateCreativeForTrialActivationCampaign

#cron_for_sending_profession_ids_to_mixpanel:
#  description: "cron for sending profession ids to mixpanel"
#  cron: '*/5 * * * * Asia/Kolkata'
#  class: CronForSendingProfessionIdsToMixpanel

#cron_for_removing_invalid_truecaller_urls_in_photos:
#  description: "Cron for removing the invalid truecaller urls in photo model"
#  cron: '*/2 * * * * Asia/Kolkata'
#  class: CronForRemovingInvalidTruecallerUrlsInPhotos

#cron_for_versions_table_data_deletion:
#  description: "Cron for versions table data deletion before November 1 2024"
#  cron: '* * * * * Asia/Kolkata'
#  class: CronForVersionsTableDataDeletion

#cron_for_populate_path_and_bucket_cols_in_photos:
#  description: "Cron for populating path and bucket columns data in the photos table"
#  cron: '* * * * * Asia/Kolkata'
#  class: CronForPopulatePathAndBucketColsInPhotos

#cron_for_updating_cdn_urls_in_videos_model:
#  description: "Cron for updating video cdn urls with thecircleapp.in"
#  cron: '* * * * * Asia/Kolkata'
#  class: CronForUpdatingCdnUrlsInVideosModel

#cron_for_create_user_roles:
#  description: "check singular data to create user roles"
#  cron: '*/10 * * * * Asia/Kolkata'
#  class: CronForCreateUserRoles

#users_indexing:
#  description: "Indexing 10000 users for every 30 minutes"
#  cron: '*/30 * * * * Asia/Kolkata'
#  class: UsersIndexing

#circles_indexing:
#  description: "Indexing 5000 circles for every 30 minutes "
#  cron: '*/30 * * * * Asia/Kolkata'
#  class: CirclesIndexing

#mixpanel_update_cron:
#  description: "mix panel update 10000 users for every 30 minutes"
#  cron: '*/30 * * * * Asia/Kolkata'
#  class: MixpanelUpdateCron

#index_and_sync_to_mixpanel_users_cron:
#  description: "To index and sync to mixpanel users every 20 minutes"
#  cron: '0/20 * * * * Asia/Kolkata'
#  class: IndexAndSyncToMixpanelUsersCron

#user_invites_indexing:
#  description: "index new user invites cron daily at 2AM"
#  cron: '0 2 * * * Asia/Kolkata'
#  class: UserInvitesIndexing

