FactoryBot.define do
  factory :order do
    user
    status { 0 }
    total_amount { 1 }
    discount_amount { 0 }
    payable_amount { 1 }

    transient do
      duration_in_months { 12 } # Default duration
    end

    trait :with_order_items do
      after(:create) do |order, evaluator|
        frame1 = FactoryBot.create(:frame, identifier: :party_curved_identity, frame_type: :premium, gold_border: false,
                                   has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                   identity_type: :curved)
        frame2 = FactoryBot.create(:frame, identifier: :party_neon_flat_identity, frame_type: :premium,
                                   gold_border: false, has_shadow_color: true, is_neutral_frame: false,
                                   has_footer_party_icon: true)
        frame3 = FactoryBot.create(:frame, identifier: :gold_white_flat_identity, frame_type: :status,
                                   gold_border: true, has_shadow_color: false, is_neutral_frame: false,
                                   has_footer_party_icon: true)
        # dont create item price for product if it is already created
        item_price = ItemPrice.where(item_type: 'Product', item_id: Constants.get_poster_product_id).first
        if item_price.blank?
          item_price = FactoryBot.create(
            :item_price,
            item_type: 'Product',
            item_id: Constants.get_poster_product_id,
            price: 1,
            maintenance_price: 0,
            duration_in_months: 1)
          FactoryBot.create(
            :item_price,
            item_type: 'Product',
            item_id: Constants.get_poster_product_id,
            price: 3,
            maintenance_price: 0,
            duration_in_months: evaluator.duration_in_months)
        end
        FactoryBot.create(
          :item_price,
          item_id: frame1.id,
          item_type: 'Frame',
          price: 1,
          maintenance_price: 0,
          duration_in_months: evaluator.duration_in_months)
        item_price1 = FactoryBot.create(
          :item_price,
          item_id: frame1.id,
          item_type: 'Frame',
          price: 1,
          maintenance_price: 0,
          duration_in_months: 1)
        FactoryBot.create(
          :item_price,
          item_id: frame2.id,
          item_type: 'Frame',
          price: 1,
          maintenance_price: 0,
          duration_in_months: evaluator.duration_in_months)
        item_price2 = FactoryBot.create(
          :item_price,
          item_id: frame2.id,
          item_type: 'Frame',
          price: 1,
          maintenance_price: 0,
          duration_in_months: 1)
        FactoryBot.create(
          :item_price,
          item_id: frame3.id,
          item_type: 'Frame',
          price: 2,
          maintenance_price: 0,
          duration_in_months: evaluator.duration_in_months)
        item_price3 = FactoryBot.create(
          :item_price,
          item_id: frame3.id,
          item_type: 'Frame',
          price: 1,
          maintenance_price: 0,
          duration_in_months: 1)

        product = FactoryBot.create(:order_item, item_type: "Product", item_id: Constants.get_poster_product_id,
                                    item_price_id: item_price.id, duration_in_months: evaluator.duration_in_months,
                                    order_id: order.id)

        FactoryBot.create(
          :order_item,
          order_id: order.id,
          item_id: frame1.id,
          item_type: 'Frame',
          item_price_id: item_price1.id,
          duration_in_months: evaluator.duration_in_months,
          total_item_price: 1,
          parent_order_item_id: product.id
        )
        FactoryBot.create(
          :order_item,
          order_id: order.id,
          item_id: frame2.id,
          item_type: 'Frame',
          item_price_id: item_price2.id,
          duration_in_months: evaluator.duration_in_months,
          total_item_price: 1,
          parent_order_item_id: product.id
        )

        FactoryBot.create(
          :order_item,
          order_id: order.id,
          item_id: frame3.id,
          item_type: 'Frame',
          item_price_id: item_price3.id,
          duration_in_months: evaluator.duration_in_months,
          total_item_price: 1,
          parent_order_item_id: product.id
        )
      end
    end

    trait :with_addon_order_items do
      after(:create) do |order|
        frame1 = FactoryBot.create(:frame, identifier: :party_curved_identity, frame_type: :premium, gold_border: false,
                                   has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                   identity_type: :curved)
        frame2 = FactoryBot.create(:frame, identifier: :party_neon_flat_identity, frame_type: :premium,
                                   gold_border: false, has_shadow_color: true, is_neutral_frame: false,
                                   has_footer_party_icon: true)
        item_price1 = FactoryBot.create(
          :item_price,
          item_id: frame1.id,
          item_type: 'Frame',
          price: 1,
          maintenance_price: 0,
          duration_in_months: 1)
        item_price2 = FactoryBot.create(
          :item_price,
          item_id: frame2.id,
          item_type: 'Frame',
          price: 1,
          maintenance_price: 0,
          duration_in_months: 1)

        FactoryBot.create(
          :order_item,
          order_id: order.id,
          item_id: frame1.id,
          item_type: 'Frame',
          item_price_id: item_price1.id,
          duration_in_months: 1,
          total_item_price: 1
        )
        FactoryBot.create(
          :order_item,
          order_id: order.id,
          item_id: frame2.id,
          item_type: 'Frame',
          item_price_id: item_price2.id,
          duration_in_months: 1,
          total_item_price: 1
        )
      end
    end
  end
end
