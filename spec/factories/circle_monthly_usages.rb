FactoryBot.define do
  factory :circle_monthly_usage do
    circle { association(:circle) }
    month_start { Time.zone.today }
    month_end { Time.zone.tomorrow }
    fan_posters_limit { Faker::Number.number(digits: 2) }
    fan_posters_usage { 0 }
    channel_message_limit { Faker::Number.number(digits: 2) }
    channel_message_usage { 0 }
    channel_notification_limit { Faker::Number.number(digits: 2) }
    channel_notification_usage { 0 }
    active { true }
  end
end
