FactoryBot.define do
  factory :subscription do
    user
    plan
    status { 'active' }
    max_amount { 100 }
    initial_charge { 1 }
    payment_gateway { 'cashfree' }
    pg_id { 'pg_123' }
    pg_reference_id { '1234' }
    auth_link { 'http://example.com' }
    pg_json { { key: 'value' } }

    after(:create) do |subscription|
      if subscription.status == 'active'
        FactoryBot.create(:metadatum,
                          entity: subscription,
                          key: Constants.subscription_activated_date_key,
                          value: Time.zone.today.strftime("%Y-%m-%d"))
      end
    end
  end
end
