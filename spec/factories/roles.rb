FactoryBot.define do
  factory :role do
    name { Faker::Name.unique.name }
    name_en { Faker::Name.unique.name }
    has_badge { true }
    badge_ring { true }
    badge_color { :GOLD }
    quota_type { :absolute }
    quota_value { 2 }
    grade_level { :grade_2 }
    parent_circle_level { :political_party }
    has_purview { false }
    purview_level {}
    active { true }
    permission_group_id { Constants.owner_permission_group_id }
  end
end
