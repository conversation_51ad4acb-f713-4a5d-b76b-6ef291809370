FactoryBot.define do
  factory :campaign do
    name { Faker::Name.name }
    start_time { Time.zone.now }
    end_time { Time.zone.now + 1.day }
    active { true }
    admin_medium do
      image_600x750 = Rack::Test::UploadedFile.new(Rails.root.join("app/assets/images/poster_creative_600x750.png"), "image/png")
      create(:admin_medium, blob_data: image_600x750)
    end
    cohort_details_json {
      { cohort_2: [{ plan_duration_in_months: 12, discount_percentage: 50.01 }],
        cohort_3: [{ plan_duration_in_months: 12, discount_percentage: 66.66 }],
        cohort_4: [{ plan_duration_in_months: 12, discount_percentage: 50.01 }] }
    }
  end
end
