FactoryBot.define do
  factory :subscription_charge_refund do
    association :subscription_charge
    association :user
    amount { 50 }
    status { :initiated }
    pg_id { "refund_#{SecureRandom.hex(8)}" }
    reason { :manual }

    trait :automatic do
      reason { :automatic }
    end

    trait :downgrade do
      reason { :downgrade }
    end

    trait :extension do
      reason { :extension }
    end

    trait :success do
      status { :success }
    end

    trait :failed do
      status { :failed }
    end
  end
end
