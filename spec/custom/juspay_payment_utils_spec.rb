require 'rails_helper'
require 'webmock/rspec'

RSpec.describe JuspayPaymentUtils do
  it 'has distinct error code constants' do
    intersection = JuspayPaymentUtils::SDK_FAILURE_ERROR_CODES & JuspayPaymentUtils::SDK_PROBABLE_SUCCESS_ERROR_CODES
    expect(intersection).to be_empty
  end

  describe '#generate_pg_id' do
    it 'returns a string' do
      expect(JuspayPaymentUtils.generate_pg_id).to be_a(String)
    end
  end

  describe '#create_customer' do
    it 'save in metadata & returns a juspay customer id' do
      allow(JuspayPaymentUtils).to receive(:post).and_return({ 'id' => '123' })
      user = create(:user)

      expect(JuspayPaymentUtils.create_customer(user)).to eq('123')
      expect(UserMetadatum.find_by(user: user, key: Constants.juspay_customer_id_key).value).to eq('123')
    end
  end

  describe '#get_customer' do
    it 'returns a customer' do
      allow(JuspayPaymentUtils).to receive(:get).and_return({ 'id' => '123' })
      expect(JuspayPaymentUtils.get_customer(create(:user))).to eq({ 'id' => '123' })
    end
  end

  describe '#get_order_status' do
    it 'returns an order' do
      allow(JuspayPaymentUtils).to receive(:get).and_return({ 'id' => '123' })
      expect(JuspayPaymentUtils.get_order_status('123')).to eq({ 'id' => '123' })
    end
  end

  describe '#get_customer_id' do
    it 'returns a string' do
      user = create(:user)
      expect(JuspayPaymentUtils.get_customer_id(user)).to eq('praja_user_' + user.id.to_s)
    end
  end

  describe '#post' do
    it 'returns a response' do
      allow(JuspayPaymentUtils).to receive(:send_request).and_return(OpenStruct.new(body: '{}'))
      expect(JuspayPaymentUtils.post('/customers', {})).to eq({})
    end
  end

  describe '#get' do
    it 'returns a response' do
      allow(JuspayPaymentUtils).to receive(:send_request).and_return(OpenStruct.new(body: '{}'))
      expect(JuspayPaymentUtils.get('/customers/123', { 'options.get_client_auth_token': true })).to eq({})
    end
  end

  describe '#put' do
    it 'returns a response' do
      allow(JuspayPaymentUtils).to receive(:send_request).and_return(OpenStruct.new(body: '{}'))
      expect(JuspayPaymentUtils.put('/customers/123', {})).to eq({})
    end
  end

  describe '#send_request' do
    context 'when method is post' do
      it 'returns a response' do
        url = URI('https://api.juspay.in/customers')
        http = Net::HTTP.new(url.host, url.port)
        http.use_ssl = true
        request = Net::HTTP::Post.new(url)
        response = nil
        http.start do |http|
          response = http.request(request)
          response.body = '{hello: "world"}'
          response.instance_variable_set(:@code, '200')
          response.instance_variable_set(:@message, 'OK')
        end
        allow(Net::HTTP).to receive(:new).and_return(http)
        allow(Net::HTTP::Post).to receive(:new).and_return(request)
        allow(http).to receive(:request).with(request).and_return(response)
        expect(JuspayPaymentUtils.send_request(url, {}, :post)).to eq(response)
      end

      it 'raises error if response if not 200' do
        url = URI('https://api.juspay.in/customers')
        http = Net::HTTP.new(url.host, url.port)
        http.use_ssl = true
        request = Net::HTTP::Put.new(url)
        response = nil
        http.start do |http|
          response = http.request(request)
          response.body = '{hello: "world"}'
          response.instance_variable_set(:@code, '400')
          response.instance_variable_set(:@message, 'Bad Request')
        end
        allow(Net::HTTP).to receive(:new).and_return(http)
        allow(Net::HTTP::Post).to receive(:new).and_return(request)
        allow(http).to receive(:request).with(request).and_return(response)
        expect { JuspayPaymentUtils.send_request(url, {}, :post) }.to raise_error(RuntimeError)
      end
    end

    context 'when method is get' do
      it 'returns a response' do
        url = URI('https://api.juspay.in/customers')
        http = Net::HTTP.new(url.host, url.port)
        http.use_ssl = true
        request = Net::HTTP::Get.new(url)
        response = nil
        http.start do |http|
          response = http.request(request)
          response.body = '{hello: "world"}'
          response.instance_variable_set(:@code, '200')
          response.instance_variable_set(:@message, 'OK')
        end
        allow(Net::HTTP).to receive(:new).and_return(http)
        allow(Net::HTTP::Get).to receive(:new).and_return(request)
        allow(http).to receive(:request).with(request).and_return(response)
        expect(JuspayPaymentUtils.send_request(url, nil, :get)).to eq(response)
      end
    end

    context 'when method is put' do
      it 'returns a response' do
        url = URI('https://api.juspay.in/customers')
        http = Net::HTTP.new(url.host, url.port)
        http.use_ssl = true
        request = Net::HTTP::Put.new(url)
        response = nil
        http.start do |http|
          response = http.request(request)
          response.body = '{hello: "world"}'
          response.instance_variable_set(:@code, '200')
          response.instance_variable_set(:@message, 'OK')
        end
        allow(Net::HTTP).to receive(:new).and_return(http)
        allow(Net::HTTP::Put).to receive(:new).and_return(request)
        allow(http).to receive(:request).with(request).and_return(response)
        expect(JuspayPaymentUtils.send_request(url, {}, :put)).to eq(response)
      end
    end

    context 'when method is invalid' do
      it 'returns nil' do
        url = URI('https://api.juspay.in/customers')
        expect { JuspayPaymentUtils.send_request(url, nil, :delete) }.to raise_error(RuntimeError, 'Invalid method: delete')
      end
    end
  end
end
