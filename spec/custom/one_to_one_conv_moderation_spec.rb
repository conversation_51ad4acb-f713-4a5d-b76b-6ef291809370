require 'rails_helper'
require 'webmock/rspec'

RSpec.describe OneToOneConvModeration do

  describe "moderation" do

    context "when sender is blocked by 2 multiple users and sender followers count is grater than 25 and sender id is not in USER_IDS_TO_SKIP_BLOCK_CHECK" do
      it "should set is_banned to false" do
        one_to_one_conv_moderation = OneToOneConvModeration.new(sender_id: 1,  is_sender_blocked_for_commenting: false, sender_blocked_by_count: 2, sender_followers_count: 30, count_of_last_7_days_active_other_conversations_created: 0, count_of_last_24_hrs_active_other_conversations_created: 0, count_of_last_7_days_active_conversations_created: 0, count_of_last_24_hrs_active_conversations_created: 0)
        expect(one_to_one_conv_moderation.banned?).to eq(false)
        expect(one_to_one_conv_moderation.reason).to eq("")
      end
    end

    context "when sender is blocked by 3 multiple users and sender followers count is less than or equal to 25 and sender id is not in USER_IDS_TO_SKIP_BLOCK_CHECK" do
      it "should set is_banned to true" do
        one_to_one_conv_moderation = OneToOneConvModeration.new(sender_id: 1,  is_sender_blocked_for_commenting: false, sender_blocked_by_count: 3, sender_followers_count: 24, count_of_last_7_days_active_other_conversations_created: 0, count_of_last_24_hrs_active_other_conversations_created: 0, count_of_last_7_days_active_conversations_created: 0, count_of_last_24_hrs_active_conversations_created: 0)
        expect(one_to_one_conv_moderation.banned?).to eq(true)
        expect(one_to_one_conv_moderation.reason).to eq(DmUtil::BANNING_REASONS[:is_blocked_by_multiple_users])
      end
    end

    context "when sender is blocked by 3 multiple users and sender followers count is less than or equal to 25 and sender id is in USER_IDS_TO_SKIP_BLOCK_CHECK" do
      it "should set is_banned to false" do
        one_to_one_conv_moderation = OneToOneConvModeration.new(sender_id: 41,  is_sender_blocked_for_commenting: false, sender_blocked_by_count: 3, sender_followers_count: 24, count_of_last_7_days_active_other_conversations_created: 0, count_of_last_24_hrs_active_other_conversations_created: 0, count_of_last_7_days_active_conversations_created: 0, count_of_last_24_hrs_active_conversations_created: 0)
        expect(one_to_one_conv_moderation.banned?).to eq(false)
        expect(one_to_one_conv_moderation.reason).to eq("")
      end
    end

    context "sender is blocked by 5 multiple users and sender followers count is less than or equal to 25 and sender id is not in USER_IDS_TO_SKIP_BLOCK_CHECK" do
      it "should set is_banned to true" do
        one_to_one_conv_moderation = OneToOneConvModeration.new(sender_id: 1,  is_sender_blocked_for_commenting: false, sender_blocked_by_count: 5, sender_followers_count: 24, count_of_last_7_days_active_other_conversations_created: 0, count_of_last_24_hrs_active_other_conversations_created: 0, count_of_last_7_days_active_conversations_created: 0, count_of_last_24_hrs_active_conversations_created: 0)
        expect(one_to_one_conv_moderation.banned?).to eq(true)
        expect(one_to_one_conv_moderation.reason).to eq(DmUtil::BANNING_REASONS[:is_blocked_by_multiple_users])
      end
    end

    context "when a user is blocked by 4 multiple users and sender followers count is greater than 25 and sender id is not in USER_IDS_TO_SKIP_BLOCK_CHECK" do
      it "should set is_banned to false" do
        one_to_one_conv_moderation = OneToOneConvModeration.new(sender_id: 1,  is_sender_blocked_for_commenting: false, sender_blocked_by_count: 0, sender_followers_count: 30, count_of_last_7_days_active_other_conversations_created: 0, count_of_last_24_hrs_active_other_conversations_created: 0, count_of_last_7_days_active_conversations_created: 0, count_of_last_24_hrs_active_conversations_created: 0)
        expect(one_to_one_conv_moderation.banned?).to eq(false)
        expect(one_to_one_conv_moderation.reason).to eq("")
      end
    end

    context "when a sender followers count is greater than 25 and sender blocked by count is less than 5 and sender id is in USER_IDS_TO_SKIP_BLOCK_CHECK" do
      it "should set is_banned to false" do
        one_to_one_conv_moderation = OneToOneConvModeration.new(sender_id: 41,  is_sender_blocked_for_commenting: false, sender_blocked_by_count: 0, sender_followers_count: 30, count_of_last_7_days_active_other_conversations_created: 0, count_of_last_24_hrs_active_other_conversations_created: 0, count_of_last_7_days_active_conversations_created: 0, count_of_last_24_hrs_active_conversations_created: 0)
        expect(one_to_one_conv_moderation.banned?).to eq(false)
        expect(one_to_one_conv_moderation.reason).to eq("")
      end
    end

    context "when user created less then 3 other conversations with in 24 hours and less than 10 other conversations with in 7 days" do
      it "should set is_banned to false" do
        one_to_one_conv_moderation = OneToOneConvModeration.new(sender_id: 1,  is_sender_blocked_for_commenting: false, sender_blocked_by_count: 0, sender_followers_count: 0, count_of_last_7_days_active_other_conversations_created: 9, count_of_last_24_hrs_active_other_conversations_created: 4, count_of_last_7_days_active_conversations_created: 0, count_of_last_24_hrs_active_conversations_created: 0)
        expect(one_to_one_conv_moderation.banned?).to eq(false)
        expect(one_to_one_conv_moderation.reason).to eq("")
      end
    end

    context "when user created more than 3 other conversations with in 24 hours and less than 10 other conversations with in 7 days" do
      it "should set is_banned to true" do
        one_to_one_conv_moderation = OneToOneConvModeration.new(sender_id: 1,  is_sender_blocked_for_commenting: false, sender_blocked_by_count: 0, sender_followers_count: 0, count_of_last_7_days_active_other_conversations_created: 9, count_of_last_24_hrs_active_other_conversations_created: 6, count_of_last_7_days_active_conversations_created: 0, count_of_last_24_hrs_active_conversations_created: 0)
        expect(one_to_one_conv_moderation.banned?).to eq(true)
        expect(one_to_one_conv_moderation.reason).to eq(DmUtil::BANNING_REASONS[:exceeded_max_other_conversations_with_in_24_hrs])
      end
    end

    context "when user created more than 3 other conversations with in 24 hours and more than 10 other conversations with in 7 days" do
      it "should set is_banned to true" do
        one_to_one_conv_moderation = OneToOneConvModeration.new(sender_id: 1,  is_sender_blocked_for_commenting: false, sender_blocked_by_count: 0, sender_followers_count: 0, count_of_last_7_days_active_other_conversations_created: 11, count_of_last_24_hrs_active_other_conversations_created: 6, count_of_last_7_days_active_conversations_created: 0, count_of_last_24_hrs_active_conversations_created: 0)
        expect(one_to_one_conv_moderation.banned?).to eq(true)
        expect(one_to_one_conv_moderation.reason).to eq(DmUtil::BANNING_REASONS[:exceeded_max_other_conversations_with_in_7_days])
      end
    end

    context "when user created less than 3 other conversations with in 24 hours and more than 10 other conversations with in 7 days" do
      it "should set is_banned to true" do
        one_to_one_conv_moderation = OneToOneConvModeration.new(sender_id: 1,  is_sender_blocked_for_commenting: false, sender_blocked_by_count: 0, sender_followers_count: 0, count_of_last_7_days_active_other_conversations_created: 11, count_of_last_24_hrs_active_other_conversations_created: 3, count_of_last_7_days_active_conversations_created: 0, count_of_last_24_hrs_active_conversations_created: 0)
        expect(one_to_one_conv_moderation.banned?).to eq(true)
        expect(one_to_one_conv_moderation.reason).to eq(DmUtil::BANNING_REASONS[:exceeded_max_other_conversations_with_in_7_days])
      end
    end

    context "when sender is blocked for commenting" do
      it "should set is_requested to true" do
        one_to_one_conv_moderation = OneToOneConvModeration.new(sender_id: 1,  is_sender_blocked_for_commenting: true, sender_blocked_by_count: 0, sender_followers_count: 0, count_of_last_7_days_active_other_conversations_created: 0, count_of_last_24_hrs_active_other_conversations_created: 0, count_of_last_7_days_active_conversations_created: 0, count_of_last_24_hrs_active_conversations_created: 0)
        expect(one_to_one_conv_moderation.requested?).to eq(true)
        expect(one_to_one_conv_moderation.reason).to eq(DmUtil::REQUESTING_REASONS[:is_comment_blocked_user])
      end
    end

    context "when user created more than 25 conversations within 7 days and less than 10 conversations with in 24 hours" do
      it "should set is_requested to true" do
        one_to_one_conv_moderation = OneToOneConvModeration.new(sender_id: 1,  is_sender_blocked_for_commenting: false, sender_blocked_by_count: 0, sender_followers_count: 0, count_of_last_7_days_active_other_conversations_created: 0, count_of_last_24_hrs_active_other_conversations_created: 0, count_of_last_7_days_active_conversations_created: 26, count_of_last_24_hrs_active_conversations_created: 0)
        expect(one_to_one_conv_moderation.requested?).to eq(true)
        expect(one_to_one_conv_moderation.reason).to eq(DmUtil::REQUESTING_REASONS[:exceeded_max_primary_conversations_with_in_7_days])
      end
    end

    context "when user created more than 10 conversations within 24 hours and less than 25 conversations with in 7 days" do
      it "should set is_requested to true" do
        one_to_one_conv_moderation = OneToOneConvModeration.new(sender_id: 1,  is_sender_blocked_for_commenting: false, sender_blocked_by_count: 0, sender_followers_count: 0, count_of_last_7_days_active_other_conversations_created: 0, count_of_last_24_hrs_active_other_conversations_created: 0, count_of_last_7_days_active_conversations_created: 0, count_of_last_24_hrs_active_conversations_created: 11)
        expect(one_to_one_conv_moderation.requested?).to eq(true)
        expect(one_to_one_conv_moderation.reason).to eq(DmUtil::REQUESTING_REASONS[:exceeded_max_primary_conversations_with_in_24_hrs])
      end
    end

    context "when user created less than 25 conversations within 7 days and less than 10 conversation with in 24 hours" do
      it "should set is_requested to false" do
        one_to_one_conv_moderation = OneToOneConvModeration.new(sender_id: 1,  is_sender_blocked_for_commenting: false, sender_blocked_by_count: 0, sender_followers_count: 0, count_of_last_7_days_active_other_conversations_created: 0, count_of_last_24_hrs_active_other_conversations_created: 0, count_of_last_7_days_active_conversations_created: 24, count_of_last_24_hrs_active_conversations_created: 9)
        expect(one_to_one_conv_moderation.requested?).to eq(false)
        expect(one_to_one_conv_moderation.reason).to eq("")
      end
    end

    context "when user created more than 10 conversations within 24 hours and more than 25 conversation with in 7 days" do
      it "should set is_requested to true" do
        one_to_one_conv_moderation = OneToOneConvModeration.new(sender_id: 1,  is_sender_blocked_for_commenting: false, sender_blocked_by_count: 0, sender_followers_count: 0, count_of_last_7_days_active_other_conversations_created: 0, count_of_last_24_hrs_active_other_conversations_created: 0, count_of_last_7_days_active_conversations_created: 26, count_of_last_24_hrs_active_conversations_created: 20)
        expect(one_to_one_conv_moderation.requested?).to eq(true)
        expect(one_to_one_conv_moderation.reason).to eq(DmUtil::REQUESTING_REASONS[:exceeded_max_primary_conversations_with_in_7_days])
      end
    end

  end

end
