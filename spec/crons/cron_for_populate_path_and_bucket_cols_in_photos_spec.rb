require 'rails_helper'

RSpec.describe CronForPopulatePathAndBucketColsInPhotos, type: :worker do
  describe '#perform' do
    let(:photo) { { id: 32, service: "aws" } }
    let(:bucket) { 'circle-app-photos' }

    context 'when there are records to update' do
      before do
        allow(Photo).to receive(:where).and_return(Photo)
        allow(Photo).to receive(:limit).and_return(Photo)
        allow(Photo).to receive(:pluck).and_return([[photo[:id], photo[:service], nil]])
        allow(Photo).to receive(:update_all)
      end

      it 'updates the path and bucket for the respective photo' do
        described_class.new.perform

        expect(Photo).to have_received(:update_all).with(Arel.sql(
           "bucket = CASE service
                      WHEN 'azure' THEN 'prodprajarup'
                      WHEN 'aws' THEN 'circle-app-photos'
                      WHEN 'gcp' THEN 'praja-raw-user-photos'
                    END"))
      end
    end

    context 'when there are records to update' do
      before do
        create(:photo, id: photo[:id], service: photo[:service], bucket: nil)
      end

      it 'updates the path and bucket for the respective photo' do
        described_class.new.perform

        updated_photo = Photo.find(photo[:id])
        expect(updated_photo.bucket).to eq('circle-app-photos')
      end
    end

    context 'when there are no records to process' do
      before do
        allow(Photo).to receive(:where).and_return(Photo)
        allow(Photo).to receive(:pluck).and_return([])
        allow(Photo).to receive(:update_all)
      end

      it 'does not update any records' do
        described_class.new.perform
        expect(Photo).not_to have_received(:update_all)
      end
    end
  end
end
