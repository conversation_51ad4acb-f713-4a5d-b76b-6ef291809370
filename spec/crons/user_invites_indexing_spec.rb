require 'rails_helper'

RSpec.describe UserInvitesIndexing, type: :worker do
  let(:time) { (1.minute) }
  let(:scheduled_job) { described_class.perform_at(time) }
  describe 'calling perfom' do
    context 'enqueues the workers' do
      it 'it enqueues a job' do
        expect do
          described_class.perform_async
        end.to change(described_class.jobs, :size).by(1)
      end

      it 'args are empty' do
        described_class.perform_async
        expect(described_class.jobs.last['args']).to eq([])
      end
    end
    context 'process the cron' do
      before :each do
        @user_invite = FactoryBot.create(:user_invite)
      end
      it 'calls the method' do
        allow(UserInviteIndexPhoneWorker).to receive(:perform_async).and_return(nil)
        described_class.new.perform
      end
    end
  end
end
