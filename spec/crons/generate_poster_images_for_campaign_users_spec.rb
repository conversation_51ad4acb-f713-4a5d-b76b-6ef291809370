require 'rails_helper'
require 'webmock/rspec'

RSpec.describe GeneratePosterImagesForCampaignUsers, type: :worker do
  let(:user) { create(:user) }
  let(:plan) { create(:plan, duration_in_months: 1) }
  let(:user_plan) { create(:user_plan, user: user, plan: plan, end_date: Time.zone.now - 2.days) }
  let(:event) { create(:event, priority: "high", active: true) }
  let(:free_creative) { create(:poster_creative, event: event, active: true, primary: true, paid: false) }
  let(:primary_creative) { create(:poster_creative, event: event, active: true, primary: true, paid: true) }
  let(:paid_creative) { create(:poster_creative, event: event, active: true, primary: false, paid: true) }
  let(:campaign_name) { "wati_re_activation_campaign_#{Time.zone.now.strftime('%Y_%m_%d')}" }

  before do
    user_plan
    free_creative
    primary_creative
    paid_creative
    create(:user_metadatum, user: user, key: Constants.premium_reactivation_wati_campaign_count, value: 1)
    allow(Event).to receive(:upcoming_events_including_current).and_return([event])
    allow(event).to receive(:get_creative_for_wati_campaign).and_return(primary_creative)
    allow(GeneratePosterCampaignImage).to receive(:perform_async).and_return(true)
    allow_any_instance_of(User).to receive(:get_user_creative_data) do |user, campaign|
      UserMetadatum.new(user_id: user.id, key: "#{campaign}_creative_id", value: primary_creative.id.to_s)
    end
    allow(UserMetadatum).to receive(:import) do |records, options|
      records.each do |record|
        UserMetadatum.create(user_id: record.user_id, key: record.key, value: record.value)
      end
    end
  end

  describe "#perform" do
    let(:worker) { GeneratePosterImagesForCampaignUsers.new }

    context "when eligible users exists" do
      it "should generate poster creative and insert user metadata" do
        expect(GeneratePosterCampaignImage).to receive(:perform_async).with(user.id, primary_creative.id.to_s, campaign_name)
        expect { worker.perform }.to change { UserMetadatum.where(user: user, key: "#{campaign_name}_creative_id", value: primary_creative.id) }
        worker.perform
        metadata = UserMetadatum.find_by(user: user, key: "#{campaign_name}_creative_id")
        expect(metadata.value).to eq(primary_creative.id.to_s)
      end
    end

    context "when user does not have a one month plan" do
      before { user_plan.update!(end_date: Time.zone.now + 1.day) }

      it "should not generate a poster creative" do
        expect(GeneratePosterCampaignImage).not_to receive(:perform_async)
        worker.perform
      end
    end

    context "when user trigger count is 11" do
      before { UserMetadatum.find_by(user: user, key: Constants.premium_reactivation_wati_campaign_count)&.update(value: 11) }

      it "should not generate a poster creative" do
        expect(GeneratePosterCampaignImage).not_to receive(:perform_async)
        worker.perform
      end
    end

    context "when there are no high priority events" do
      before { allow(Event).to receive(:upcoming_events_including_current).and_return([]) }
      it "should not generate a poster creative" do
        expect(GeneratePosterCampaignImage).not_to receive(:perform_async)
      end
    end

    context "when event does not have a valid creative" do
      before { allow(event).to receive(:get_creative_for_wati_campaign).and_return(nil) }

      it "Should not generate a poster creative" do
        expect(GeneratePosterCampaignImage).not_to receive(:perform_async)
      end
    end

    context "when there are multiple users" do
      let(:user2) { create(:user) }
      let(:user_plan2) { create(:user_plan, user: user2, plan: plan, end_date: Time.zone.now - 3.days) }

      before do
        user_plan2
        create(:user_metadatum, user: user2, key: Constants.premium_reactivation_wati_campaign_count, value: 2)
      end

      it "should generate poster creatives for all eligible users" do
        expect(GeneratePosterCampaignImage).to receive(:perform_async).with(user.id, primary_creative.id.to_s, campaign_name)
        expect(GeneratePosterCampaignImage).to receive(:perform_async).with(user2.id, primary_creative.id.to_s, campaign_name)
        worker.perform
        expect(UserMetadatum.find_by(user: user, key: "#{campaign_name}_creative_id").value).to eq(primary_creative.id.to_s)
        expect(UserMetadatum.find_by(user: user2, key: "#{campaign_name}_creative_id").value).to eq(primary_creative.id.to_s)
      end
    end
  end
end
