require 'rails_helper'

RSpec.describe CreateRenewalOrderCron, type: :worker do
  let(:time) { (1.minute) }
  let(:scheduled_job) { described_class.perform_at(time) }
  describe 'calling perfom' do
    context 'enqueues the workers' do
      it 'it enqueues a job' do
        expect do
          described_class.perform_async
        end.to change(described_class.jobs, :size).by(1)
      end

      it 'args are empty' do
        described_class.perform_async
        expect(described_class.jobs.last['args']).to eq([])
      end
    end
    context 'process the cron' do
      before :each do
        # user with product subscription expires in 15 days
        @user = FactoryBot.create(:user)
        @order = FactoryBot.create(:order, :with_order_items, user: @user)
        @order.update(status: :successful)
        # user product subscription will be created after order is successful
        @order.order_items.each do |order_item|
          if order_item.item_type == "Product"
            FactoryBot.create(:user_product_subscription, user_id: @user.id, item_type: "Product",
                              item_id: order_item.item_id, order_id: @order.id, start_date: Time.zone.now - 1.month,
                              end_date: Time.zone.now + 15.days)
          else
            FactoryBot.create(:user_product_subscription, user_id: @user.id, item_type: "Frame", item_id: order_item.item_id,
                              order_id: @order.id, start_date: Time.zone.now - 1.month, end_date: Time.zone.now + 15.days)
          end
        end

        # user with product subscription expires in 16 days
        @user1 = FactoryBot.create(:user)
        @order1 = FactoryBot.create(:order, :with_order_items, user: @user1)
        @order1.update(status: :successful)
        # user product subscription will be created after order is successful
        @order1.order_items.each do |order_item|
          if order_item.item_type == "Product"
            FactoryBot.create(:user_product_subscription, user_id: @user1.id, item_type: "Product",
                              item_id: order_item.item_id, order_id: @order1.id, start_date: Time.zone.now - 1.month,
                              end_date: Time.zone.now + 16.days)
          else
            FactoryBot.create(:user_product_subscription, user_id: @user1.id, item_type: "Frame", item_id: order_item.item_id,
                              order_id: @order1.id, start_date: Time.zone.now - 1.month, end_date: Time.zone.now + 16.days)
          end
        end

        # user with product subscription expires in 10 days
        @user2 = FactoryBot.create(:user)
        @order2 = FactoryBot.create(:order, :with_order_items, user: @user2)
        @order2.update(status: :successful)
        # user product subscription will be created after order is successful
        @order2.order_items.each do |order_item|
          if order_item.item_type == "Product"
            FactoryBot.create(:user_product_subscription, user_id: @user2.id, item_type: "Product",
                              item_id: order_item.item_id, order_id: @order2.id, start_date: Time.zone.now - 1.month,
                              end_date: Time.zone.now + 10.days)
          else
            FactoryBot.create(:user_product_subscription, user_id: @user2.id, item_type: "Frame", item_id: order_item.item_id,
                              order_id: @order2.id, start_date: Time.zone.now - 1.month, end_date: Time.zone.now + 10.days)
          end
        end
      end
      it 'calls the method' do
        described_class.new.perform
        expect(CreateRenewalOrderForUser.jobs.size).to eq(2)
      end
      it 'if one user has open order with product' do
        @order3 = FactoryBot.create(:order, :with_order_items, user: @user2)
        described_class.new.perform
        expect(CreateRenewalOrderForUser.jobs.size).to eq(1)
      end
      it 'if user has open order which is for add on' do
        @order4 = FactoryBot.create(:order, :with_addon_order_items, user: @user2)
        described_class.new.perform
        expect(CreateRenewalOrderForUser.jobs.size).to eq(2)
      end
      it 'if user has two successful premium orders' do
        @order5 = FactoryBot.create(:order, :with_order_items, user: @user2)
        @order5.update(status: :successful)
        @order5.order_items.each do |order_item|
          if order_item.item_type == "Product"
            FactoryBot.create(:user_product_subscription, user_id: @user2.id, item_type: "Product",
                              item_id: order_item.item_id, order_id: @order5.id,
                              start_date: Time.zone.now + 15.days, end_date: Time.zone.now + 45.days)
          else
            FactoryBot.create(:user_product_subscription, user_id: @user2.id, item_type: "Frame",
                              item_id: order_item.item_id, order_id: @order5.id, start_date: Time.zone.now + 15.days,
                              end_date: Time.zone.now + 45.days)
          end
        end
        described_class.new.perform
        expect(CreateRenewalOrderForUser.jobs.size).to eq(1)
      end
    end
  end
end

