# frozen_string_literal: true
require 'rails_helper'

RSpec.describe WebApp::Admin::WebAppAdminController, type: :controller do
  let(:controller) { WebApp::Admin::WebAppAdminController.new }
  describe '#set_circle' do
    before :each do
      @circle = FactoryBot.create(:circle)
    end

    context 'should return not found' do
      it 'when circle hash id is not found' do
        allow(controller).to receive(:render)
        allow(controller).to receive(:request).and_return(double(headers: {}))
        controller.send(:set_circle)
        expect(controller).to have_received(:render).with(hash_including(json: { success: false, message: 'సర్కిల్ కనుగొనబడలేదు' }, status: :bad_request))
      end

      it 'when circle is not found' do
        allow(controller).to receive(:render)
        allow(controller).to receive(:request).and_return(double(headers: { 'X-Circle-HashId' => 'abcdef' }))
        controller.send(:set_circle)
        expect(controller).to have_received(:render).with(hash_including(json: { success: false, message: 'సర్కిల్ కనుగొనబడలేదు' }, status: :not_found))
      end
    end

    context 'should return success' do
      it 'when circle is found' do
        allow(controller).to receive(:request).and_return(double(headers: { 'X-Circle-HashId' => @circle.hashid }))
        controller.send(:set_circle)
        expect(controller.instance_variable_get(:@circle)).to eq(@circle)
      end
    end
  end

  describe '#check_user_permission' do
    before :each do
      @circle = FactoryBot.create(:circle)
      @user = FactoryBot.create(:user)
      controller.instance_variable_set(:@circle, @circle)
      controller.instance_variable_set(:@user, @user)
      allow(controller).to receive(:render)
    end

    context 'should return bad request' do
      it 'when user does not have owner permission' do
        @permission_group = FactoryBot.build(:permission_group, name: 'creator')
        @permission_group.save(validate: false)
        FactoryBot.create(:permission_group_permission, permission_group: @permission_group)
        FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle, permission_group: @permission_group)

        controller.send(:check_user_permission)
        expect(controller).to have_received(:render).with(hash_including(json: { success: false, message: 'మీకు ఈ పర్మిషన్ లేదు' }, status: :forbidden))
      end
    end
  end

  describe '#get_initial_data' do
    before :each do
      @circle = FactoryBot.create(:circle)
      Metadatum.create!(entity: @circle, key: 'webapp_mixpanel_embed_link', value: 'https://link.com')
      @circle2 = FactoryBot.create(:circle)
      @user = FactoryBot.create(:user)
      @permission_group = PermissionGroup.find(Constants.owner_permission_group_id)
      FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle, permission_group: @permission_group)
      FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle2, permission_group: @permission_group)
      token, = @user.generate_web_app_jwt_token
      request.headers['Authorization'] = "Bearer #{token}"
      request.headers['X-Circle-HashId'] = @circle.hashid
      allow($redis).to receive(:sismember).and_return(true)
    end

    it 'should return success' do
      get :get_initial_data
      expect(response).to have_http_status(:ok)
      response_body = JSON.parse(response.body)
      expect(response_body['data']['user']['id']).to eq(@user.hashid)
      expect(response_body['data']['circle']['id']).to eq(@circle.hashid)
      expect(response_body['data']['circles'].count).to eq(2)
      expect(response_body['data']['circles'][0]['id']).to eq(@circle.hashid)
      expect(response_body['data']['circles'][1]['id']).to eq(@circle2.hashid)
      expect(response_body['data']['create_config']).to eq(['create_post'])
      expect(response_body['data']['support_config']).to eq(%w[whatsapp call_me_now])
      expect(response_body['data']['praja_support_number']).to eq('+************')
      expect(response_body['data']['mixpanel_embed_link']).to eq('https://link.com')
    end
  end

  describe '#update_owner_profile' do
    before :each do
      @circle = FactoryBot.create(:circle)
      @user = FactoryBot.create(:user)
      User.find_by_phone(Constants.praja_account_phone)
      @permission_group = PermissionGroup.find(Constants.owner_permission_group_id)
      FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle, permission_group: @permission_group)
      token, = @user.generate_web_app_jwt_token
      request.headers['Authorization'] = "Bearer #{token}"
      request.headers['X-Circle-HashId'] = @circle.hashid
      allow($redis).to receive(:sismember).and_return(true)
    end

    context 'should return bad request' do
      it 'when photo is not saved' do
        image = fixture_file_upload("app/assets/images/logo.png", "image/png")
        allow(Photo).to receive(:upload).and_return(nil)
        put :update_owner_profile, params: { photo: image }
        expect(response).to have_http_status(:bad_request)
        response_body = JSON.parse(response.body)
        expect(response_body['success']).to eq(false)
        expect(response_body['message']).to eq('ఫోటో అప్లోడ్ ఫెయిల్ అయినది. కాసేపు తర్వాత ప్రయత్నించండి')
      end

      it 'when profile is not updated' do
        allow_any_instance_of(User).to receive(:save).and_return(false)
        put :update_owner_profile, params: { name: 'owner name' }
        expect(response).to have_http_status(:bad_request)
        response_body = JSON.parse(response.body)
        expect(response_body['success']).to eq(false)
        expect(response_body['message']).to eq('ప్రొఫైల్ అప్డేట్ చెయ్యడం సాధ్యం కాలేదు, కాసేపు ఆగి ప్రయత్నించండి.')
      end
    end

    context 'should return success' do
      it 'when profile is updated' do
        image = fixture_file_upload("app/assets/images/logo.png", "image/png")
        put :update_owner_profile, params: { name: 'owner name', photo: image }
        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)
        expect(response_body['id']).to eq(@user.id)
        expect(response_body['name']).to eq('owner name')
        expect(response_body['photo']).to be_present
      end
    end
  end
end
