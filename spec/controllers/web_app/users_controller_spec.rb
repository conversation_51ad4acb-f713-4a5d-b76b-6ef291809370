# frozen_string_literal: true
require 'rails_helper'

RSpec.describe WebApp::UsersController, type: :controller do
  describe '#update_profile' do
    before :each do
      @user = FactoryBot.create(:user)
      User.find_by_phone(Constants.praja_account_phone)
      token, = @user.generate_web_app_jwt_token
      request.headers['Authorization'] = "Bearer #{token}"
      allow($redis).to receive(:sismember).and_return(true)
    end

    context 'returns success' do
      it 'if saved successfully' do
        photo = fixture_file_upload('app/assets/images/praja-full-logo.png', 'image/png')
        put :update_profile, params: { name: 'Test User', short_bio: 'Test Bio', photo: photo }
        expect(response).to have_http_status(:ok)
        @user.reload
        expect(@user.name).to eq('Test User')
        expect(@user.short_bio).to eq('Test Bio')
        expect(@user.photo_id).to be_present
      end
    end

    context 'returns bad request' do
      it 'if failed to update data on to user' do
        photo = fixture_file_upload('app/assets/images/praja-full-logo.png', 'image/png')
        allow_any_instance_of(User).to receive(:save).and_return(false)
        put :update_profile, params: { name: 'Test User', short_bio: 'Test Bio', photo: photo }
        expect(response).to have_http_status(:bad_request)
        @user.reload
        expect(@user.name).to eq(@user.name)
        expect(@user.short_bio).to eq(@user.short_bio)
        expect(@user.photo_id).to eq(@user.photo_id)
      end

      it 'if photo upload fails' do
        photo = fixture_file_upload('app/assets/images/praja-full-logo.png', 'image/png')
        allow(Photo).to receive(:upload).and_return(nil)
        put :update_profile, params: { name: 'Test User', short_bio: 'Test Bio', photo: photo }
        expect(response).to have_http_status(:bad_request)
        @user.reload
        expect(@user.name).to eq(@user.name)
        expect(@user.short_bio).to eq(@user.short_bio)
        expect(@user.photo_id).to eq(@user.photo_id)
      end
    end
  end
end
