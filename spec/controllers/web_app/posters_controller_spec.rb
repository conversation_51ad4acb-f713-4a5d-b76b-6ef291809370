require 'rails_helper'

RSpec.describe WebApp::<PERSON>ers<PERSON><PERSON>roller, type: :controller do
  describe "#get_metadata_for_poster_web_tool" do
    before :each do
      @circle = FactoryBot.create(:circle)
      @event = FactoryBot.create(:event)
    end

    context "should return not found" do
      it "when circle is not found" do
        allow(Circle).to receive(:find_by_hashid).and_return(nil)
        get :get_metadata_for_poster_web_tool, params: { circle_hash_id: 'invalid' }
        expect(response).to have_http_status(:not_found)
      end

      it "when creatives are not found" do
        allow(Circle).to receive(:find_by_hashid).and_return(@circle)
        allow(PosterCreative).to receive(:of_circle).and_return([])
        get :get_metadata_for_poster_web_tool, params: { circle_hash_id: @circle.hashid }
        expect(response).to have_http_status(:not_found)
      end
    end

    context "should return ok" do
      it "when circle and creatives are found" do
        allow(Circle).to receive(:find_by_hashid).and_return(@circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        allow(PosterCreative).to receive(:of_circle).and_return([@poster_creative])
        get :get_metadata_for_poster_web_tool, params: { circle_hash_id: @circle.hashid }
        expect(response).to have_http_status(:ok)
      end
    end

  end

  describe "#get_poster_web_tool_frames" do
    before :each do
      @circle = FactoryBot.create(:circle, level: :political_party)
      @leader_circle = FactoryBot.create(:circle, level: :political_leader,
                                         circle_photos:
                                           [FactoryBot.build(:circle_photo,
                                                             photo: FactoryBot.create(:photo),
                                                             photo_type: :poster,
                                                             photo_order: 1)])
      @frame = FactoryBot.create(:frame, frame_type: :basic, gold_border: false,
                                  has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                  identity_type: :flat_user)
    end

    context "should return not found" do
      before :each do
        @circle_package = FactoryBot.create(:circle_package, enable_fan_posters: true)
        FactoryBot.create(:circle_package_mapping, circle_package: @circle_package, circle: @circle)
        FactoryBot.create(:circle_monthly_usage, circle: @circle)
      end
      it "when circle is not found" do
        allow(Circle).to receive(:find_by_hashid).and_return(nil)
        post :get_poster_web_tool_frames, params: { circle_hash_id: 'invalid' }
        expect(response).to have_http_status(:not_found)
      end

      it "when basic frame is not found" do
        allow(Circle).to receive(:find_by_hashid).and_return(@circle)
        @circle_frames = CircleFrame.get_circle_frames
        allow(CircleFrame).to receive(:get_circle_frames).and_return([])
        post :get_poster_web_tool_frames, params: { circle_hash_id: @circle.hashid }
        expect(response).to have_http_status(:not_found)
      end

      it "when creatives are not found" do
        allow(Circle).to receive(:find_by_hashid).and_return(@circle)
        @circle.photo = FactoryBot.create(:photo)
        @circle_frames = CircleFrame.get_circle_frames
        allow(CircleFrame).to receive(:get_circle_frames).and_return([@frame])
        allow(PosterCreative).to receive(:of_circle).and_return([])
        post :get_poster_web_tool_frames, params: { circle_hash_id: @circle.hashid }
        expect(response).to have_http_status(:not_found)
      end

      it "when creatives are not found for leader circle" do
        allow(Circle).to receive(:find_by_hashid).and_return(@leader_circle)
        @leader_circle.photo = FactoryBot.create(:photo)
        @leader_circle_package = FactoryBot.create(:circle_package, enable_fan_posters: true)
        FactoryBot.create(:circle_package_mapping, circle_package: @leader_circle_package, circle: @leader_circle)
        FactoryBot.create(:circle_monthly_usage, circle: @leader_circle)
        @circle_frames = CircleFrame.get_circle_frames
        allow(CircleFrame).to receive(:get_circle_frames).and_return([@frame])
        allow(PosterCreative).to receive(:of_circle).and_return([])
        post :get_poster_web_tool_frames, params: { circle_hash_id: @leader_circle.hashid }
        expect(response).to have_http_status(:not_found)
      end
    end

    context "should return success" do
      before :each do
        @circle_package = FactoryBot.create(:circle_package, enable_fan_posters: true)
        FactoryBot.create(:circle_package_mapping, circle_package: @circle_package, circle: @circle)
        FactoryBot.create(:circle_monthly_usage, circle: @circle)
      end
      it "when circle and basic frame are found" do
        allow(Circle).to receive(:find_by_hashid).and_return(@circle)
        @circle.photo = FactoryBot.create(:photo)
        @circle_frames = CircleFrame.get_circle_frames
        allow(CircleFrame).to receive(:get_circle_frames).and_return([@frame])
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        allow(PosterCreative).to receive(:of_circle).and_return([@poster_creative])
        allow(Singular).to receive(:shorten_link).and_return("https://praja.app/circles/1")
        post :get_poster_web_tool_frames, params: { circle_hash_id: @circle.hashid }
        expect(response).to have_http_status(:ok)
      end

      it "when circle and basic frame are found with user" do
        allow(Circle).to receive(:find_by_hashid).and_return(@circle)
        @circle.photo = FactoryBot.create(:photo)
        @circle_frames = CircleFrame.get_circle_frames
        allow(CircleFrame).to receive(:get_circle_frames).and_return([@frame])
        @user = FactoryBot.create(:user)
        token = @user.generate_jwt_token
        request.headers['Authorization'] = "Bearer #{token}"
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        allow(PosterCreative).to receive(:of_circle).and_return([@poster_creative])
        allow(Singular).to receive(:shorten_link).and_return("https://praja.app/circles/1")
        post :get_poster_web_tool_frames, params: { circle_hash_id: @circle.hashid }
        expect(response).to have_http_status(:ok)
      end

      it "when leader circle and basic frame are found" do
        allow(Circle).to receive(:find_by_hashid).and_return(@leader_circle)
        @leader_circle.photo = FactoryBot.create(:photo)
        @circle_frames = CircleFrame.get_circle_frames
        allow(CircleFrame).to receive(:get_circle_frames).and_return([@frame])
        @leader_party_circle = FactoryBot.create(:circles_relation, first_circle: @leader_circle, second_circle: @circle, relation: 'Leader2Party', active: true)
        @leader_circle_package = FactoryBot.create(:circle_package, enable_fan_posters: true)
        FactoryBot.create(:circle_package_mapping, circle_package: @leader_circle_package, circle: @leader_circle)
        FactoryBot.create(:circle_monthly_usage, circle: @leader_circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        allow(PosterCreative).to receive(:of_circle).and_return([@poster_creative])
        allow(Singular).to receive(:shorten_link).and_return("https://praja.app/circles/1")
        post :get_poster_web_tool_frames, params: { circle_hash_id: @leader_circle.hashid }
        expect(response).to have_http_status(:ok)
      end
    end

    context "should return empty list if enable_fan_posters is false" do
      it "when circle has no package" do
        allow(Circle).to receive(:find_by_hashid).and_return(@circle)
        post :get_poster_web_tool_frames, params: { circle_hash_id: @circle.hashid }
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe "#get_download_link" do
    it "should return the download link" do
      expect(controller.send(:get_download_link)).to be_a(String)
    end
  end

  describe "#get_share_url" do
    it "should return the share url" do
      @circle = FactoryBot.create(:circle, level: :political_party)
      allow(Singular).to receive(:shorten_link).and_return("https://praja.app/circles/1")
      expect(controller.send(:get_share_url, circle: @circle)).to be_a(String)
    end
  end
end
