require 'rails_helper'
require 'webmock/rspec'

RSpec.describe PostsController, type: :controller do

  describe "#set_post" do
    before :each do
      @user = FactoryBot.create(:user)
      @post = FactoryBot.create(:post, user: @user)
      AppVersionSupport.new('2808.01.01')
      @token = @user.generate_jwt_token
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = "2808.01.01"
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end

    it "should set post hashid id if is sent as id" do
      get :index, params: { :id => @post.hashid }
      post = controller.instance_variable_get(:@post)
      expect(post).to eq(@post)
    end

    it "should set post hash id if is sent as post_id" do
      get :index, params: { :post_id => @post.hashid }
      post = controller.instance_variable_get(:@post)
      expect(post).to eq(@post)
    end

    it "should set post hash id ifis sent as hashid" do
      get :index, params: { :hashid => @post.hashid }
      post = controller.instance_variable_get(:@post)
      expect(post).to eq(@post)
    end

    it "should return not_found if id isn't found" do
      get :index, params: { :id => '123' }
      expect(response).to have_http_status(:not_found)
      post = controller.instance_variable_get(:@post)
      expect(post).to be_nil
    end
  end

  describe "GET #show" do
    context "add new attribute comments_type to post object" do
      before :each do
        @user = FactoryBot.create(:user)
        @post = FactoryBot.create(:post, user: @user)
        @circle = FactoryBot.create(:circle)
        @post_circle = FactoryBot.create(:post_circle, post: @post, circle: @circle)
        AppVersionSupport.new('1.17.3')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "renders a JSON response with comments_type in post object " do
        get :show, params: { :id => @post.id }
        body = JSON.parse(response.body)
        expect(body["comments_type"]).to be_present
      end
    end
  end

  describe "POST #post_create" do
    context "create" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        AppVersionSupport.new('0.3.6')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "0.3.6"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "creates and if no post circles tagged, has_tagged_circles is false" do
        post :create, params: { content: "Hi", circle_ids: [@circle.id], user_id: @user.id }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body['has_tagged_circles']).to eq(true)
      end

      it "creates and if no post circles tagged, has_tagged_circles is false" do
        post :create, params: { content: "Hi", user_id: @user.id }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body['has_tagged_circles']).to eq(false)
      end

      it "create post successfully if multiple variations of same hashtag come up" do
        post :create, params: { content: "Hi #ContentCreate #contentcreate #contentCreate", user_id: @user.id }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body['id'].present?).to eq(true)
        expect(body['id']).to be > 0

        post = Post.find body['id']
        expect(post.hashtags.count).to eq(3)
      end

      it "creates a post with photo through create" do
        AppVersionSupport.new('2302.28.11')
        @request.headers['X-App-Version'] = "2302.28.11"
        image = fixture_file_upload("app/assets/images/logo.png", "image/png")
        post :create, params: { content: "Hi", user_id: @user.id, photos: { "0" => image, "1" => image } }

        expect(response).to have_http_status(:ok)
      end

      it 'if post created with neither photo not video does not send post media to media service job calls' do
        AppVersionSupport.new('2302.28.11')
        @request.headers['X-App-Version'] = "2302.28.11"
        post :create, params: { content: "Hi", user_id: @user.id }
        expect(SendPostMediaToMediaService.jobs.size).to eq(0)
      end

      it 'if post created with photo send post media to media service job calls' do
        AppVersionSupport.new('2302.28.11')
        @request.headers['X-App-Version'] = "2302.28.11"
        image = fixture_file_upload("app/assets/images/logo.png", "image/png")
        post :create, params: { content: "Hi", user_id: @user.id, photos: { "0" => image } }
        expect(SendPostMediaToMediaService.jobs.size).to eq(1)
      end

      it 'if post created with video send post media to media service job calls' do
        AppVersionSupport.new('2302.28.11')
        @request.headers['X-App-Version'] = "2302.28.11"
        video = fixture_file_upload("app/assets/videos/sample_video.mp4", "video/mp4")
        thumbnail = fixture_file_upload("app/assets/images/logo.png", "image/png")
        post :create, params: { content: "Hi", user_id: @user.id, videos: { "0" => video }, video_thumbnails: { "0" => thumbnail } }
        expect(SendPostMediaToMediaService.jobs.size).to eq(1)
      end

      it 'if post created with video through create does not enqueue media service job' do
        AppVersionSupport.new('2302.28.11')
        @request.headers['X-App-Version'] = "2302.28.11"
        video = fixture_file_upload("app/assets/videos/sample_video.mp4", "video/mp4")
        thumbnail = fixture_file_upload("app/assets/images/logo.png", "image/png")
        post :create, params: { content: "Hi", user_id: @user.id, videos: { "0" => video }, video_thumbnails: { "0" => thumbnail } }
        body = JSON.parse(response.body)
        post = Post.find_by(id: body["post_id"])
        SendPostMediaToMediaService.new.perform(post.id)
        expect(SendDataToMediaService.jobs.size).to eq(0)
      end

      it "creates post with selection of comments type" do
        post :create, params: { content: "Hi", comments_type: "1" }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body["comments_type"]).to eq("none")

        post :create, params: { content: "Hello", comments_type: "0" }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body["comments_type"]).to eq("all_users")

        @user.circles << FactoryBot.create(:circle)
        post :create, params: { content: "Welcome", comments_type: "2" }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body["comments_type"]).to eq("exclusive_party_users")

        post :create, params: { content: "Welcome Back", comments_type: "3" }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body["comments_type"]).to eq("exclusive_party_and_neutral_users")
      end

      it "creates post with parent post" do
        @parent_post = FactoryBot.create(:post)
        post :create, params: { content: "Hi", parent_post_id: @parent_post.id }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body["parent_post_id"]).to eq(@parent_post.id)
      end

      it "creates post within circles" do
        @circle = FactoryBot.create(:circle)
        post :create, params: { content: "Hi", circle_id: @circle.id }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body["circle"]["id"]).to eq(@circle.id)

        AppVersionSupport.new('2808.01.01')
        @request.headers['X-App-Version'] = "2808.01.01"
        @circle2 = FactoryBot.create(:circle, circle_type: :location, level: :state)
        post :create, params: { content: "Hello", circle_ids: [@circle.id, @circle2.id] }
        expect(response).to have_http_status(:ok)

        post :create, params: { content: "Hello Hi", circle_ids: [@circle.id, @circle2.id] }
        expect(response).to have_http_status(:ok)
      end

      it "creates post with received hashtags in params" do ||
        post :create, params: { content: "Hi", hashtags: { 0 => { tag: "#hello" } } }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body["hashtags"][0]["tag"]).to eq("#hello")
      end

      it "creates and makes the has_poster_photo field true if the name of the file has poster" do
        AppVersionSupport.new('2302.28.11')
        @request.headers['X-App-Version'] = "2302.28.11"
        image = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")
        post :create, params: { content: "Hi", user_id: @user.id, photos: { "0" => image } }
        expect(response).to have_http_status(:ok)
      end

      it "returns unprocessible entity if post save is not successful" do
        allow_any_instance_of(Post).to receive(:save!).and_raise(StandardError)
        post :create, params: { content: "Hi" }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "POST #create_v2" do
    context "create v2 for gcp service" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        AppVersionSupport.new('2305.28.12')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "2305.28.12"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "creates a post with video through create_v2" do
        videos = [{
                    "file_id": "3bb2ae5b0476337bc6f2852f5825cbbf",
                    "file_name": "3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "original_file_name": "sample_video.mp4",
                    "path": "50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "thumbnail": {
                      "path": "3bb2ae5b0476337bc6f2852f5825cbbf.jpg",
                      "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.jpg"
                    },
                    "service": "gcp",
                    "bitrate_mbps": 10.234,
                    "duration_secs": 300,
                    "width": 1280,
                    "height": 720,
                    "mode": "landscape"
                  }]
        post :create_v2, params: { content: "Hi", user_id: @user.id, videos: videos }
        body = JSON.parse(response.body)
        post = Post.find_by(id: body["post_id"])
        expect(response).to have_http_status(:ok)
        expect(post.video_ids.size).to eq(1)
        expect(post.videos.first.service.to_sym).to eq(:gcp)
        expect(post.videos.first.bitrate).to eq(10.234)
        expect(post.videos.first.duration).to eq(300)
        expect(post.videos.first.width).to eq(1280)
        expect(post.videos.first.height).to eq(720)
        expect(post.videos.first.mode).to eq("landscape")
      end

      it "created post with video without bitrate and duration" do
        videos = [{
                    "file_id": "3bb2ae5b0476337bc6f2852f5825cbbf",
                    "file_name": "3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "original_file_name": "sample_video.mp4",
                    "path": "50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "thumbnail": {
                      "path": "3bb2ae5b0476337bc6f2852f5825cbbf.jpg",
                      "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.jpg"
                    },
                    "service": "gcp"
                  }]
        post :create_v2, params: { content: "Hi", user_id: @user.id, videos: videos }
        body = JSON.parse(response.body)
        post = Post.find_by(id: body["post_id"])
        expect(response).to have_http_status(:ok)
        expect(post.video_ids.size).to eq(1)
        expect(post.videos.first.service.to_sym).to eq(:gcp)
        expect(post.videos.first.bitrate).to eq(nil)
        expect(post.videos.first.duration).to eq(nil)
        expect(post.videos.first.width).to eq(nil)
        expect(post.videos.first.height).to eq(nil)
        expect(post.videos.first.mode).to eq(nil)
      end

      it 'if created send post media to media service job calls' do
        photos = [
          {
            "file_id": "0c8f194b1be9391a18d4da433e03acfa",
            "file_name": "0c8f194b1be9391a18d4da433e03acfa.jpg",
            "original_file_name": "1645707.jpg",
            "path": "50/0c8f194b1be9391a18d4da433e03acfa.jpg",
            "cdn_url": "https://grup-cdn.circleapp.in/50/0c8f194b1be9391a18d4da433e03acfa.jpg",
            "service": "gcp"
          }
        ]
        post :create_v2, params: { content: "Hi", user_id: @user.id, photos: photos }
        expect(SendPostMediaToMediaService.jobs.size).to eq(1)
      end

      it 'if created with video send post media to media service job calls' do
        videos = [{
                    "file_id": "3bb2ae5b0476337bc6f2852f5825cbbf",
                    "file_name": "3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "original_file_name": "sample_video.mp4",
                    "path": "50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "thumbnail": {
                      "path": "3bb2ae5b0476337bc6f2852f5825cbbf.jpg",
                      "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.jpg"
                    },
                    "service": "gcp"
                  }]
        post :create_v2, params: { content: "Hi", user_id: @user.id, videos: videos }
        expect(SendPostMediaToMediaService.jobs.size).to eq(1)
      end

      it 'if created with one photo, with service gcp enqueues one media service job' do
        photos = [{
                    "file_id": "98f3058178f10d4688374a9099f3342c",
                    "file_name": "98f3058178f10d4688374a9099f3342c.jpeg",
                    "original_file_name": "image.jpeg",
                    "path": "50/98f3058178f10d4688374a9099f3342c.jpeg",
                    "cdn_url": "https://grup-cdn.circleapp.in/50/98f3058178f10d4688374a9099f3342c.jpeg",
                    "service": "gcp"
                  }]
        post :create_v2, params: { content: "Hi", user_id: @user.id, photos: photos }
        body = JSON.parse(response.body)
        post = Post.find_by(id: body["post_id"])
        SendPostMediaToMediaService.new.perform(post.id)
        expect(SendDataToMediaService.jobs.size).to eq(1)
      end

      it 'if created with one video, with service gcp enqueues one media service job' do
        videos = [{
                    "file_id": "3bb2ae5b0476337bc6f2852f5825cbbf",
                    "file_name": "3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "original_file_name": "sample_video.mp4",
                    "path": "50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "thumbnail": {
                      "path": "3bb2ae5b0476337bc6f2852f5825cbbf.jpg",
                      "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.jpg"
                    },
                    "service": "gcp"
                  }]
        post :create_v2, params: { content: "Hi", user_id: @user.id, videos: videos }
        body = JSON.parse(response.body)
        post = Post.find_by(id: body["post_id"])
        SendPostMediaToMediaService.new.perform(post.id)
        expect(SendDataToMediaService.jobs.size).to eq(1)
      end

      it 'if created  with photo,  response of media service job to be 200' do
        photos = [{
                    "file_id": "98f3058178f10d4688374a9099f3342c",
                    "file_name": "98f3058178f10d4688374a9099f3342c.jpeg",
                    "original_file_name": "image.jpeg",
                    "path": "50/98f3058178f10d4688374a9099f3342c.jpeg",
                    "cdn_url": "https://grup-cdn.circleapp.in/50/98f3058178f10d4688374a9099f3342c.jpeg",
                    "service": 'gcp'
                  }]

        stub_request(:post, "#{Constants.media_service_callbacks_baseurl}/photos/post-created")
          .to_return(status: 201, body: '', headers: {})

        post :create_v2, params: { content: "Hi", user_id: @user.id, photos: photos }
        body = JSON.parse(response.body)
        post = Post.find_by(id: body["post_id"])

        url = "#{Constants.media_service_callbacks_baseurl}/photos/post-created"
        body =
          {
            "user_id": post.user_id,
            "photos": {
              "path": "50/98f3058178f10d4688374a9099f3342c.jpeg"
            },
            "metadata": {
              "photo_ids": post.photo_ids
            }
          }

        # Call your worker that makes the HTTPS request
        response = SendDataToMediaService.new.perform(url, body)

        # Assert that the HTTPS request returned a 200 status code
        expect(response.code.to_i).to eq(201)
      end

      it 'if created  with video, response of media service job to be 200' do
        videos = [{
                    "file_id": "3bb2ae5b0476337bc6f2852f5825cbbf",
                    "file_name": "3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "original_file_name": "sample_video.mp4",
                    "path": "50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "thumbnail": {
                      "path": "3bb2ae5b0476337bc6f2852f5825cbbf.jpg",
                      "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.jpg"
                    },
                    "service": 'gcp'
                  }]

        stub_request(:post, "#{Constants.media_service_callbacks_baseurl}/videos/post-created")
          .to_return(status: 201, body: '', headers: {})

        post :create_v2, params: { content: "Hi", user_id: @user.id, videos: videos }
        body = JSON.parse(response.body)
        post = Post.find_by(id: body["post_id"])

        url = "#{Constants.media_service_callbacks_baseurl}/videos/post-created"
        body =
          {
            "user_id": post.user_id,
            "videos": [{
                         "path": "50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4"
                       }],
            "metadata": {
              "video_ids": post.video_ids
            }
          }

        # Call your worker that makes the HTTPS request
        response = SendDataToMediaService.new.perform(url, body)

        # Assert that the HTTPS request returned a 200 status code
        expect(response.code.to_i).to eq(201)
      end

      it "not creates when service being null" do
        photos = [
          {
            "file_id": "0c8f194b1be9391a18d4da433e03acfa",
            "file_name": "0c8f194b1be9391a18d4da433e03acfa.jpg",
            "original_file_name": "1645707.jpg",
            "path": "50/0c8f194b1be9391a18d4da433e03acfa.jpg",
            "cdn_url": "https://grup-cdn.circleapp.in/50/0c8f194b1be9391a18d4da433e03acfa.jpg"
          }
        ]

        post :create_v2, params: { content: "Hi", user_id: @user.id, photos: photos }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:unprocessable_entity)
        expect(body["success"]).to be_falsey
        expect(body["message"]).to eq("Photo service is blank")
      end

      it "not creates when where photo url being null" do
        photos = [
          {
            "file_id": "0c8f194b1be9391a18d4da433e03acfa",
            "file_name": "0c8f194b1be9391a18d4da433e03acfa.jpg",
            "original_file_name": "1645707.jpg",
            "path": "50/0c8f194b1be9391a18d4da433e03acfa.jpg",
            "service": "gcp"
          }
        ]

        post :create_v2, params: { content: "Hi", user_id: @user.id, photos: photos }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:unprocessable_entity)
        expect(body["success"]).to be_falsey
        expect(body["message"]).to eq("Photo url is blank")
      end

      it "not creates when photo path being null" do
        photos = [
          {
            "file_id": "0c8f194b1be9391a18d4da433e03acfa",
            "file_name": "0c8f194b1be9391a18d4da433e03acfa.jpg",
            "original_file_name": "1645707.jpg",
            "cdn_url": "https://grup-cdn.circleapp.in/50/0c8f194b1be9391a18d4da433e03acfa.jpg",
            "service": "gcp"
          }
        ]

        post :create_v2, params: { content: "Hi", user_id: @user.id, photos: photos }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:unprocessable_entity)
        expect(body["success"]).to be_falsey
        expect(body["message"]).to eq("Photo path is blank")
      end

      it "not creates when service being null" do
        videos = [{
                    "file_id": "3bb2ae5b0476337bc6f2852f5825cbbf",
                    "file_name": "3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "original_file_name": "sample_video.mp4",
                    "path": "50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "thumbnail": {
                      "path": "3bb2ae5b0476337bc6f2852f5825cbbf.jpg",
                      "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.jpg"
                    }
                  }]

        post :create_v2, params: { content: "Hi", user_id: @user.id, videos: videos }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:unprocessable_entity)
        expect(body["success"]).to be_falsey
        expect(body["message"]).to eq("Video service is blank")
      end

      it "not creates when url being null" do
        videos = [{
                    "file_id": "3bb2ae5b0476337bc6f2852f5825cbbf",
                    "file_name": "3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "original_file_name": "sample_video.mp4",
                    "path": "50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "thumbnail": {
                      "path": "3bb2ae5b0476337bc6f2852f5825cbbf.jpg",
                      "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.jpg"
                    },
                    "service": "gcp"
                  }]

        post :create_v2, params: { content: "Hi", user_id: @user.id, videos: videos }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:unprocessable_entity)
        expect(body["success"]).to be_falsey
        expect(body["message"]).to eq("Video url is blank")
      end

      it "not creates when path being null" do
        videos = [{
                    "file_id": "3bb2ae5b0476337bc6f2852f5825cbbf",
                    "file_name": "3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "original_file_name": "sample_video.mp4",
                    "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "thumbnail": {
                      "path": "3bb2ae5b0476337bc6f2852f5825cbbf.jpg",
                      "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.jpg"
                    },
                    "service": "gcp"
                  }]

        post :create_v2, params: { content: "Hi", user_id: @user.id, videos: videos }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:unprocessable_entity)
        expect(body["success"]).to be_falsey
        expect(body["message"]).to eq("Video path is blank")
      end

      it "creates when thumbnail being null" do
        AppVersionSupport.new('2302.28.12')
        @request.headers['X-App-Version'] = "2302.28.12"
        videos = [{
                    "file_id": "3bb2ae5b0476337bc6f2852f5825cbbf",
                    "file_name": "3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "original_file_name": "sample_video.mp4",
                    "path": "50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
                    "thumbnail": nil,
                    "service": "gcp"
                  }]

        post :create_v2, params: { content: "Hi", user_id: @user.id, videos: videos }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body["success"]).to be_truthy
        expect(body["post_id"].present?).to be_truthy
      end

      it "creates metadata with key is_poster_photo_post if post has poster photo" do
        photos = [{
                    "file_id": "poster(11)",
                    "file_name": "poster(11).png",
                    "original_file_name": "poster(11).png",
                    "path": "50/poster(11).png",
                    "cdn_url": "https://grup-cdn.circleapp.in/50/poster(11).png",
                    "service": "gcp"
                  },
                  {
                    "file_id": "premium_poster_12",
                    "file_name": "premium_poster_12.png",
                    "original_file_name": "premium_poster_12.png",
                    "path": "50/premium_poster_12.png",
                    "cdn_url": "https://grup-cdn.circleapp.in/50/premium_poster_12.png",
                    "service": "gcp"
                  },
                  {
                    "file_id": "premium_poster",
                    "file_name": "premium_poster.png",
                    "original_file_name": "premium_poster.png",
                    "path": "50/premium_poster.png",
                    "cdn_url": "https://grup-cdn.circleapp.in/50/premium_poster.png",
                    "service": "gcp"
                  }
        ]
        post :create_v2, params: { content: "Hi", user_id: @user.id, photos: photos }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        post = Post.find_by(id: body["post_id"])
        expect(post.metadatum.where(key: Constants.get_is_poster_photo_key_for_post).exists?).to be_truthy
      end

      it "does not creates metadata if post has no poster photo" do
        photos = [{
                    "file_id": "premium_poster_a1",
                    "file_name": "premium_poster_a1.png",
                    "original_file_name": "premium_poster_a1.png",
                    "path": "50/premium_poster_a1.png",
                    "cdn_url": "https://grup-cdn.circleapp.in/50/premium_poster_a1.png",
                    "service": "gcp"
                  },
                  {
                    "file_id": "poster_1",
                    "file_name": "poster_1.png",
                    "original_file_name": "poster_1.png",
                    "path": "50/poster_1.png",
                    "cdn_url": "https://grup-cdn.circleapp.in/50/poster_1.png",
                    "service": "gcp"
                  },
                  {
                    "file_id": "poster(1)",
                    "file_name": "poster(1).jpeg",
                    "original_file_name": "poster(1).jpeg",
                    "path": "50/poster(1).jpeg",
                    "cdn_url": "https://grup-cdn.circleapp.in/50/poster(1).jpeg",
                    "service": "gcp"
                  }
        ]
        post :create_v2, params: { content: "Hi", user_id: @user.id, photos: photos }
        body = JSON.parse(response.body)
        post = Post.find_by(id: body["post_id"])
        expect(post.metadatum.where(key: Constants.get_is_poster_photo_key_for_post).exists?).to be_falsey
      end
    end

    context "create v2 azure service" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        AppVersionSupport.new('2305.28.12')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "2305.28.12"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "creates a post with video from azure service" do
        videos = [{
                    "service": "azure",
                    "original_file_name": "sample-mp4-file-small.mp4",
                    "file_id": "2f4b6854e33f523c3aecd71d96f0444c",
                    "file_name": "2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "bucket": "prodprajaruv",
                    "path": "user-116909/2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "cdn_url": "https://azruv.thecircleapp.in/user-116909/2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "thumbnail": {
                      "service": "azure",
                      "original_file_name": "poster.jpeg",
                      "file_id": "2f4b6854e33f523c3aecd71d96f0444c",
                      "file_name": "2f4b6854e33f523c3aecd71d96f0444c.jpeg",
                      "bucket": "prodprajaruv",
                      "path": "116909/2f4b6854e33f523c3aecd71d96f0444c.jpeg",
                      "cdn_url": "https://az-ip-cdn.thecircleapp.in/116909/2f4b6854e33f523c3aecd71d96f0444c.jpeg"
                    }
                  }]
        post :create_v2, params: { content: "Hi", user_id: @user.id, videos: videos }
        body = JSON.parse(response.body)
        post = Post.find_by(id: body["post_id"])
        expect(response).to have_http_status(:ok)
        expect(post.video_ids.size).to eq(1)
        expect(post.videos.first.service.to_sym).to eq(:azure)
      end

      it 'if created send post media to media service job calls' do
        photos = [
          {
            "service": "azure",
            "original_file_name": "McDonalds-food-poster-design.jpg",
            "file_id": "5203ae521f73c09863199909b48041d2",
            "file_name": "5203ae521f73c09863199909b48041d2.jpg",
            "bucket": "prodprajarup",
            "path": "user-116909/5203ae521f73c09863199909b48041d2.jpg",
            "cdn_url": "https://az-ip-cdn.thecircleapp.in/user-116909/5203ae521f73c09863199909b48041d2.jpg"
          }
        ]
        post :create_v2, params: { content: "Hi", user_id: @user.id, photos: photos }
        expect(SendPostMediaToMediaService.jobs.size).to eq(1)
      end

      it 'if created with video send post media to media service job calls' do
        videos = [{
                    "service": "azure",
                    "original_file_name": "sample-mp4-file-small.mp4",
                    "file_id": "2f4b6854e33f523c3aecd71d96f0444c",
                    "file_name": "2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "bucket": "prodprajaruv",
                    "path": "user-116909/2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "cdn_url": "https://azruv.thecircleapp.in/user-116909/2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "thumbnail": {
                      "service": "azure",
                      "original_file_name": "poster.jpeg",
                      "file_id": "2f4b6854e33f523c3aecd71d96f0444c",
                      "file_name": "2f4b6854e33f523c3aecd71d96f0444c.jpeg",
                      "bucket": "prodprajaruv",
                      "path": "116909/2f4b6854e33f523c3aecd71d96f0444c.jpeg",
                      "cdn_url": "https://az-ip-cdn.thecircleapp.in/116909/2f4b6854e33f523c3aecd71d96f0444c.jpeg"
                    }
                  }]
        post :create_v2, params: { content: "Hi", user_id: @user.id, videos: videos }
        expect(SendPostMediaToMediaService.jobs.size).to eq(1)
      end

      it 'if created with one photo, with service gcp enqueues one media service job' do
        photos = [{
                    "service": "azure",
                    "original_file_name": "McDonalds-food-poster-design.jpg",
                    "file_id": "5203ae521f73c09863199909b48041d2",
                    "file_name": "5203ae521f73c09863199909b48041d2.jpg",
                    "bucket": "prodprajarup",
                    "path": "user-116909/5203ae521f73c09863199909b48041d2.jpg",
                    "cdn_url": "https://az-ip-cdn.thecircleapp.in/user-116909/5203ae521f73c09863199909b48041d2.jpg"
                  }]
        post :create_v2, params: { content: "Hi", user_id: @user.id, photos: photos }
        body = JSON.parse(response.body)
        post = Post.find_by(id: body["post_id"])
        SendPostMediaToMediaService.new.perform(post.id)
        expect(SendDataToMediaService.jobs.size).to eq(1)
      end

      it 'if created with one video, with service gcp enqueues one media service job' do
        videos = [{
                    "service": "azure",
                    "original_file_name": "sample-mp4-file-small.mp4",
                    "file_id": "2f4b6854e33f523c3aecd71d96f0444c",
                    "file_name": "2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "bucket": "prodprajaruv",
                    "path": "user-116909/2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "cdn_url": "https://azruv.thecircleapp.in/user-116909/2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "thumbnail": {
                      "service": "azure",
                      "original_file_name": "poster.jpeg",
                      "file_id": "2f4b6854e33f523c3aecd71d96f0444c",
                      "file_name": "2f4b6854e33f523c3aecd71d96f0444c.jpeg",
                      "bucket": "prodprajaruv",
                      "path": "116909/2f4b6854e33f523c3aecd71d96f0444c.jpeg",
                      "cdn_url": "https://az-ip-cdn.thecircleapp.in/116909/2f4b6854e33f523c3aecd71d96f0444c.jpeg"
                    }
                  }]
        post :create_v2, params: { content: "Hi", user_id: @user.id, videos: videos }
        body = JSON.parse(response.body)
        post = Post.find_by(id: body["post_id"])
        SendPostMediaToMediaService.new.perform(post.id)
        expect(SendDataToMediaService.jobs.size).to eq(1)
      end

      it 'if created  with photo,  response of media service job to be 200' do
        photos = [{
                    "service": "azure",
                    "original_file_name": "McDonalds-food-poster-design.jpg",
                    "file_id": "5203ae521f73c09863199909b48041d2",
                    "file_name": "5203ae521f73c09863199909b48041d2.jpg",
                    "bucket": "prodprajarup",
                    "path": "user-116909/5203ae521f73c09863199909b48041d2.jpg",
                    "cdn_url": "https://az-ip-cdn.thecircleapp.in/user-116909/5203ae521f73c09863199909b48041d2.jpg"
                  }]

        stub_request(:post, "#{Constants.media_service_callbacks_baseurl}/photos/post-created")
          .to_return(status: 201, body: '', headers: {})

        post :create_v2, params: { content: "Hi", user_id: @user.id, photos: photos }
        body = JSON.parse(response.body)
        post = Post.find_by(id: body["post_id"])

        url = "#{Constants.media_service_callbacks_baseurl}/photos/post-created"
        body =
          {
            "user_id": post.user_id,
            "photos": {
              "path": "user-116909/5203ae521f73c09863199909b48041d2.jpg"
            },
            "metadata": {
              "photo_ids": post.photo_ids
            }
          }

        # Call your worker that makes the HTTPS request
        response = SendDataToMediaService.new.perform(url, body)

        # Assert that the HTTPS request returned a 200 status code
        expect(response.code.to_i).to eq(201)
      end

      it 'if created  with video, response of media service job to be 200' do
        videos = [{
                    "service": "azure",
                    "original_file_name": "sample-mp4-file-small.mp4",
                    "file_id": "2f4b6854e33f523c3aecd71d96f0444c",
                    "file_name": "2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "bucket": "prodprajaruv",
                    "path": "user-116909/2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "cdn_url": "https://azruv.thecircleapp.in/user-116909/2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "thumbnail": {
                      "service": "azure",
                      "original_file_name": "poster.jpeg",
                      "file_id": "2f4b6854e33f523c3aecd71d96f0444c",
                      "file_name": "2f4b6854e33f523c3aecd71d96f0444c.jpeg",
                      "bucket": "prodprajaruv",
                      "path": "116909/2f4b6854e33f523c3aecd71d96f0444c.jpeg",
                      "cdn_url": "https://az-ip-cdn.thecircleapp.in/116909/2f4b6854e33f523c3aecd71d96f0444c.jpeg"
                    }
                  }]

        stub_request(:post, "#{Constants.media_service_callbacks_baseurl}/videos/post-created")
          .to_return(status: 201, body: '', headers: {})

        post :create_v2, params: { content: "Hi", user_id: @user.id, videos: videos }
        body = JSON.parse(response.body)
        post = Post.find_by(id: body["post_id"])

        url = "#{Constants.media_service_callbacks_baseurl}/videos/post-created"
        body =
          {
            "user_id": post.user_id,
            "videos": [{
                         "path": "user-116909/2f4b6854e33f523c3aecd71d96f0444c.mp4"
                       }],
            "metadata": {
              "video_ids": post.video_ids
            }
          }

        # Call your worker that makes the HTTPS request
        response = SendDataToMediaService.new.perform(url, body)

        # Assert that the HTTPS request returned a 200 status code
        expect(response.code.to_i).to eq(201)
      end

      it "not creates when service being null" do
        photos = [
          {
            "original_file_name": "McDonalds-food-poster-design.jpg",
            "file_id": "5203ae521f73c09863199909b48041d2",
            "file_name": "5203ae521f73c09863199909b48041d2.jpg",
            "bucket": "prodprajarup",
            "path": "user-116909/5203ae521f73c09863199909b48041d2.jpg",
            "cdn_url": "https://az-ip-cdn.thecircleapp.in/user-116909/5203ae521f73c09863199909b48041d2.jpg"
          }
        ]

        post :create_v2, params: { content: "Hi", user_id: @user.id, photos: photos }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:unprocessable_entity)
        expect(body["success"]).to be_falsey
        expect(body["message"]).to eq("Photo service is blank")
      end

      it "not creates when where photo url being null" do
        photos = [
          {
            "service": "azure",
            "original_file_name": "McDonalds-food-poster-design.jpg",
            "file_id": "5203ae521f73c09863199909b48041d2",
            "file_name": "5203ae521f73c09863199909b48041d2.jpg",
            "bucket": "prodprajarup",
            "path": "user-116909/5203ae521f73c09863199909b48041d2.jpg"
          }
        ]

        post :create_v2, params: { content: "Hi", user_id: @user.id, photos: photos }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:unprocessable_entity)
        expect(body["success"]).to be_falsey
        expect(body["message"]).to eq("Photo url is blank")
      end

      it "not creates when photo path being null" do
        photos = [
          {
            "service": "azure",
            "original_file_name": "McDonalds-food-poster-design.jpg",
            "file_id": "5203ae521f73c09863199909b48041d2",
            "file_name": "5203ae521f73c09863199909b48041d2.jpg",
            "bucket": "prodprajarup",
            "cdn_url": "https://az-ip-cdn.thecircleapp.in/user-116909/5203ae521f73c09863199909b48041d2.jpg"
          }
        ]

        post :create_v2, params: { content: "Hi", user_id: @user.id, photos: photos }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:unprocessable_entity)
        expect(body["success"]).to be_falsey
        expect(body["message"]).to eq("Photo path is blank")
      end

      it "not creates when video service being null" do
        videos = [{
                    "original_file_name": "sample-mp4-file-small.mp4",
                    "file_id": "2f4b6854e33f523c3aecd71d96f0444c",
                    "file_name": "2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "bucket": "prodprajaruv",
                    "path": "user-116909/2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "cdn_url": "https://azruv.thecircleapp.in/user-116909/2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "thumbnail": {
                      "service": "azure",
                      "original_file_name": "poster.jpeg",
                      "file_id": "2f4b6854e33f523c3aecd71d96f0444c",
                      "file_name": "2f4b6854e33f523c3aecd71d96f0444c.jpeg",
                      "bucket": "prodprajaruv",
                      "path": "116909/2f4b6854e33f523c3aecd71d96f0444c.jpeg",
                      "cdn_url": "https://az-ip-cdn.thecircleapp.in/116909/2f4b6854e33f523c3aecd71d96f0444c.jpeg"
                    }
                  }]

        post :create_v2, params: { content: "Hi", user_id: @user.id, videos: videos }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:unprocessable_entity)
        expect(body["success"]).to be_falsey
        expect(body["message"]).to eq("Video service is blank")
      end

      it "not creates when video url being null" do
        videos = [{
                    "service": "azure",
                    "original_file_name": "sample-mp4-file-small.mp4",
                    "file_id": "2f4b6854e33f523c3aecd71d96f0444c",
                    "file_name": "2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "bucket": "prodprajaruv",
                    "path": "user-116909/2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "thumbnail": {
                      "service": "azure",
                      "original_file_name": "poster.jpeg",
                      "file_id": "2f4b6854e33f523c3aecd71d96f0444c",
                      "file_name": "2f4b6854e33f523c3aecd71d96f0444c.jpeg",
                      "bucket": "prodprajaruv",
                      "path": "116909/2f4b6854e33f523c3aecd71d96f0444c.jpeg",
                      "cdn_url": "https://az-ip-cdn.thecircleapp.in/116909/2f4b6854e33f523c3aecd71d96f0444c.jpeg"
                    }
                  }]

        post :create_v2, params: { content: "Hi", user_id: @user.id, videos: videos }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:unprocessable_entity)
        expect(body["success"]).to be_falsey
        expect(body["message"]).to eq("Video url is blank")
      end

      it "not creates when video path being null" do
        videos = [{
                    "service": "azure",
                    "original_file_name": "sample-mp4-file-small.mp4",
                    "file_id": "2f4b6854e33f523c3aecd71d96f0444c",
                    "file_name": "2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "bucket": "prodprajaruv",
                    "cdn_url": "https://azruv.thecircleapp.in/user-116909/2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "thumbnail": {
                      "service": "azure",
                      "original_file_name": "poster.jpeg",
                      "file_id": "2f4b6854e33f523c3aecd71d96f0444c",
                      "file_name": "2f4b6854e33f523c3aecd71d96f0444c.jpeg",
                      "bucket": "prodprajaruv",
                      "path": "116909/2f4b6854e33f523c3aecd71d96f0444c.jpeg",
                      "cdn_url": "https://az-ip-cdn.thecircleapp.in/116909/2f4b6854e33f523c3aecd71d96f0444c.jpeg"
                    }
                  }]

        post :create_v2, params: { content: "Hi", user_id: @user.id, videos: videos }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:unprocessable_entity)
        expect(body["success"]).to be_falsey
        expect(body["message"]).to eq("Video path is blank")
      end

      it "not creates when thumbnail being null" do
        AppVersionSupport.new('2302.28.12')
        @request.headers['X-App-Version'] = "2302.28.12"
        videos = [{
                    "service": "azure",
                    "original_file_name": "sample-mp4-file-small.mp4",
                    "file_id": "2f4b6854e33f523c3aecd71d96f0444c",
                    "file_name": "2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "bucket": "prodprajaruv",
                    "path": "user-116909/2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "cdn_url": "https://azruv.thecircleapp.in/user-116909/2f4b6854e33f523c3aecd71d96f0444c.mp4",
                    "thumbnail": nil
                  }]

        post :create_v2, params: { content: "Hi", user_id: @user.id, videos: videos }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body["success"]).to be_truthy
        expect(body["post_id"].present?).to be_truthy
      end
    end

    context "create v2 response has attachments data" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        AppVersionSupport.new('2402.12.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "2402.12.01"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "create a post with attachment data when photo type is admin medium" do
        image_630x940 = fixture_file_upload('app/assets/images/poster_creative_630x940.png', 'image/png')
        image_600x750 = fixture_file_upload('app/assets/images/poster_creative_600x750.png', 'image/png')
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        post :create_v2, params: { content: "Hi", user_id: @user.id, attachments: [{
                                                                                     "type": "creative",
                                                                                     "id": @poster_creative.id }] }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body["success"]).to be_truthy
        post = Post.find_by(id: body["post_id"])
        expect(post.photos.size).to eq(1)
        expect(post.photos.first.url).to eq(@admin_medium_1.url)
      end
      it "create a post with attachment data when photo type is Photo" do
        image_800x1000 = fixture_file_upload('app/assets/images/poster_creative_800x1000.png', 'image/png')
        photo = FactoryBot.create(:photo, blob_data: image_800x1000)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: photo, photo_v2: nil, primary: true)
        post :create_v2, params: { content: "Hi", user_id: @user.id, attachments: [{ "type": "creative",
                                                                                     "id": @poster_creative.id }] }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(body["success"]).to be_truthy
        post = Post.find_by(id: body["post_id"])
        expect(post.photos.size).to eq(1)
      end
      it 'send creative id which is not present' do
        post :create_v2, params: { content: "Hi", user_id: @user.id, attachments: [{ "type": "creative", "id": -1 }] }
        body = JSON.parse(response.body)
        expect(response).to have_http_status(:unprocessable_entity)
        expect(body["success"]).to be_falsey
        expect(body["message"]).to eq("పోస్టు క్రియేటివ్ కనుగొనబడలేదు")
      end
    end

  end

  describe "GET #get_post_preview" do
    context "fetching the post preview data" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        @post = FactoryBot.create(:post, user: @user)
        AppVersionSupport.new('2304.01.01')
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "2304.01.01"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "renders a JSON response with post preview hash for dm " do
        get :get_post_preview, params: { :post_id => @post.id }
        body = JSON.parse(response.body)
        expect(body["id"]).to be_present
        expect(body["user"]).to be_present
      end
    end
  end

  describe "PUT #update_comments_config" do
    context "update comments_type for a post" do
      before :each do
        @user = FactoryBot.create(:user)
        @post = FactoryBot.create(:post, user: @user)
        AppVersionSupport.new('1.17.3')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "renders a JSON response with comments_type updated" do
        put :update_comments_config, params: { :post_id => @post.id, :comments_type => "1" }
        expect(response).to have_http_status(:ok)
      end
    end
  end

  # new comments privacy test
  describe "GET #posts show" do
    context "for a post with comments privacy all users by user" do
      before do
        @post_user = FactoryBot.create(:user, affiliated_party_circle_id: nil)
        @post = FactoryBot.create(:post, user: @post_user, comments_type: 0, party_id_on_comments_type: nil)
        AppVersionSupport.new('1.17.3')
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "neutral user can comment" do
        @user = FactoryBot.create(:user, affiliated_party_circle_id: nil)
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        get :show, params: { :id => @post.id }
        body = JSON.parse(response.body)
        expect(body["enable_comments"]).to be(true)
      end
      it "excl. party user can comment" do
        @circle = FactoryBot.create(:circle)
        @user = FactoryBot.create(:user, affiliated_party_circle_id: @circle.id)
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        get :show, params: { :id => @post.id }
        body = JSON.parse(response.body)
        expect(body["enable_comments"]).to be(true)
      end
    end
    context "for a post with comments privacy none by user" do
      before do
        @post_user = FactoryBot.create(:user, affiliated_party_circle_id: nil)
        @post = FactoryBot.create(:post, user: @post_user, comments_type: 1, party_id_on_comments_type: nil)
        AppVersionSupport.new('1.17.3')
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "neutral user can't comment" do
        @user = FactoryBot.create(:user, affiliated_party_circle_id: nil)
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        get :show, params: { :id => @post.id }
        body = JSON.parse(response.body)
        expect(body["enable_comments"]).to be(false)
      end
      it "excl. party user can't comment" do
        @circle = FactoryBot.create(:circle)
        @user = FactoryBot.create(:user, affiliated_party_circle_id: @circle.id)
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        get :show, params: { :id => @post.id }
        body = JSON.parse(response.body)
        expect(body["enable_comments"]).to be(false)
      end
    end
    context "for a post with comments privacy only excl. party fans by user" do
      before do
        @circle = FactoryBot.create(:circle)
        @post_user = FactoryBot.create(:user, affiliated_party_circle_id: @circle.id)
        @post = FactoryBot.create(:post, user: @post_user, comments_type: 2, party_id_on_comments_type: @circle.id)
        AppVersionSupport.new('1.17.3')
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "neutral user can't comment" do
        @user = FactoryBot.create(:user, affiliated_party_circle_id: nil)
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        get :show, params: { :id => @post.id }
        body = JSON.parse(response.body)
        expect(body["enable_comments"]).to be(false)
      end
      it "same excl. party user can comment" do
        @user = FactoryBot.create(:user, affiliated_party_circle_id: @circle.id)
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        get :show, params: { :id => @post.id }
        body = JSON.parse(response.body)
        expect(body["enable_comments"]).to be(true)
      end
      it "other excl. party user can't comment" do
        @circle = FactoryBot.create(:circle)
        @user = FactoryBot.create(:user, affiliated_party_circle_id: @circle.id)
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        get :show, params: { :id => @post.id }
        body = JSON.parse(response.body)
        expect(body["enable_comments"]).to be(false)
      end
    end
    context "for a post with comments privacy excl. party fans and neutral users by user" do
      before do
        @circle = FactoryBot.create(:circle)
        @post_user = FactoryBot.create(:user, affiliated_party_circle_id: @circle.id)
        @post = FactoryBot.create(:post, user: @post_user, comments_type: 3, party_id_on_comments_type: @circle.id)
        AppVersionSupport.new('1.17.3')
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "neutral user can comment" do
        @user = FactoryBot.create(:user, affiliated_party_circle_id: nil)
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        get :show, params: { :id => @post.id }
        body = JSON.parse(response.body)
        expect(body["enable_comments"]).to be(true)
      end
      it "same excl. party user can comment" do
        @user = FactoryBot.create(:user, affiliated_party_circle_id: @circle.id)
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        get :show, params: { :id => @post.id }
        body = JSON.parse(response.body)
        expect(body["enable_comments"]).to be(true)
      end
      it "other excl. party user can't comment" do
        @circle = FactoryBot.create(:circle)
        @user = FactoryBot.create(:user, affiliated_party_circle_id: @circle.id)
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        get :show, params: { :id => @post.id }
        body = JSON.parse(response.body)
        expect(body["enable_comments"]).to be(false)
      end
    end
    context "for a post by neutral user with comments type all users try to update comments config" do
      before do
        @circle = FactoryBot.create(:circle)
        @post_user = FactoryBot.create(:user, affiliated_party_circle_id: nil)
        @post = FactoryBot.create(:post, user: @post_user, comments_type: 0, party_id_on_comments_type: nil)
        AppVersionSupport.new('1.17.3')
        @token = @post_user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "after joined in one party show four options" do
        FactoryBot.create(:user_circle, user: @post_user, circle: @circle)
        get :show, params: { :id => @post.id }
        body = JSON.parse(response.body)
        expect(body['comments_config']['default_comments_type_identifier']).to be(0)
        expect(body['comments_config']['available_comment_options'].count).to be(2)
      end
    end
    context "for a post by neutral user with comments type none try to update comments config" do
      before do
        @circle = FactoryBot.create(:circle)
        @post_user = FactoryBot.create(:user, affiliated_party_circle_id: nil)
        @post = FactoryBot.create(:post, user: @post_user, comments_type: 1, party_id_on_comments_type: nil)
        AppVersionSupport.new('1.17.3')
        @token = @post_user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "after joined in one party show four options" do
        FactoryBot.create(:user_circle, user: @post_user, circle: @circle)
        get :show, params: { :id => @post.id }
        body = JSON.parse(response.body)
        expect(body['comments_config']['default_comments_type_identifier']).to be(1)
        expect(body['comments_config']['available_comment_options'].count).to be(2)
      end
    end
    context "for a post by excl. party user with comments type excl. party fans try to update comments config" do
      before do
        @post_user = FactoryBot.create(:user)
        @circle1 = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @post_user, circle: @circle1)
        @post = FactoryBot.create(:post, user: @post_user, comments_type: 2, party_id_on_comments_type: @circle1.id)
        AppVersionSupport.new('1.17.3')
        @token = @post_user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "after joined in another excl. party show four options" do
        @post_user.circles.destroy(@circle1)
        @circle2 = FactoryBot.create(:circle)
        FactoryBot.create(:user_circle, user: @post_user, circle: @circle2)
        get :show, params: { :id => @post.id }
        body = JSON.parse(response.body)
        expect(body['comments_config']['default_comments_type_identifier']).to be(2)
        expect(body['comments_config']['available_comment_options'][2]['display_text']).to eq("మీ #{@circle1.name} సభ్యులు మాత్రమే")
        expect(body['comments_config']['available_comment_options'].count).to be(4)
      end
    end
    context "for a post by excl. party user with comments type excl. party fans and neutal users try to update comments config" do
      before do
        @post_user = FactoryBot.create(:user)
        @circle1 = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @post_user, circle: @circle1)
        @post = FactoryBot.create(:post, user: @post_user, comments_type: 3, party_id_on_comments_type: @circle1.id)
        AppVersionSupport.new('1.17.3')
        @token = @post_user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "after joined in another excl. party show four options" do
        @post_user.circles.destroy(@circle1)
        @circle2 = FactoryBot.create(:circle)
        FactoryBot.create(:user_circle, user: @post_user, circle: @circle2)
        get :show, params: { :id => @post.id }
        body = JSON.parse(response.body)
        expect(body['comments_config']['default_comments_type_identifier']).to be(3)
        expect(body['comments_config']['available_comment_options'][2]['display_text']).to eq("మీ #{@circle1.name} సభ్యులు మాత్రమే")
        expect(body['comments_config']['available_comment_options'].count).to be(4)
      end
    end
  end

  describe "POST #remove_circle_tag" do
    context "update post after removing tag if user has permission" do
      before :each do
        @user = FactoryBot.create(:user)

        @post = FactoryBot.create(:post, user: @user)
        @circle = FactoryBot.create(:circle)
        @post_circle = FactoryBot.create(:post_circle, post: @post, circle: @circle)

        @permission_group = FactoryBot.build(:permission_group)
        @permission_group.save(validate: false)
        @permission_group_permission = FactoryBot.build(:permission_group_permission,
                                                        permission_group_id: @permission_group.id,
                                                        permission_identifier: :remove_tag)
        @permission_group_permission.save

        @user_circle_permission = UserCirclePermissionGroup.create(user: @user, circle: @circle,
                                                                   permission_group: @permission_group)

        @user_permissions = @user.get_all_circle_permissions(@circle.id)

        @permission_id = PermissionGroupPermission.permission_identifiers[@user_permissions.first]
        AppVersionSupport.new('1.17.3')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "renders a JSON response with post updated" do

        post :remove_circle_tag, params: { :permission_id => @permission_id, :circle_id => @circle.id,
                                           :user_id => @user.id, :post_id => @post.id }

        body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)

        expect(body["tagged_circles_ids"]).not_to include(@circle.id)
      end
    end

    context "failed request if user haven't permission to remove tag" do
      before :each do
        @user = FactoryBot.create(:user)
        @post = FactoryBot.create(:post, user: @user)
        @circle = FactoryBot.create(:circle)
        @post_circle = FactoryBot.create(:post_circle, post: @post, circle: @circle)

        @permission_group = FactoryBot.build(:permission_group)
        @permission_group.save(validate: false)
        @permission_group_permission = FactoryBot.build(:permission_group_permission,
                                                        permission_group_id: @permission_group.id,
                                                        permission_identifier: :add_tag)

        @permission_group_permission.save
        @user_circle_permission = UserCirclePermissionGroup.create(user: @user, circle: @circle,
                                                                   permission_group: @permission_group)

        @user_permissions = @user.get_all_circle_permissions(@circle.id)

        @permission_id = PermissionGroupPermission.permission_identifiers[@user_permissions.first]

        AppVersionSupport.new('1.17.3')
        @token = @user.generate_login_token(nil, nil)
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = "1.17.3"
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end
      it "renders a JSON response with post updated" do

        post :remove_circle_tag, params: { :permission_id => @permission_id, :circle_id => @circle.id,
                                           :user_id => @user.id, :post_id => @post.id }

        expect(response).to have_http_status(:forbidden)
      end
    end
  end

  describe "POST #share_to_whatsapp" do
    before :each do
      @user = FactoryBot.create(:user)

      @post = FactoryBot.create(:post, user: @user)
      AppVersionSupport.new('2304.01.0')
      @token = @user.generate_jwt_token
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = '2304.01.0'
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end
    context "share post to whatsapp" do
      it "renders a JSON response with post updated" do
        post :share_to_whatsapp, params: { :post_id => @post.id }
        JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
      end

      it "update whatsapp share count" do
        expect {
          post :share_to_whatsapp, params: { :post_id => @post.id }
        }.to change { @post.unique_users_whatsapp_count }.by(1)
      end

      it "get unique users whatsapp share count" do
        post :share_to_whatsapp, params: { :post_id => @post.id }
        post :share_to_whatsapp, params: { :post_id => @post.id }

        JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(@post.unique_users_whatsapp_count).to eq(1)
      end

      it "get unique users whatsapp share count when two users shared" do
        post :share_to_whatsapp, params: { :post_id => @post.id }

        # adding other user share count to redis
        @user_1 = FactoryBot.create(:user)
        $redis.hincrby("post_whatsapp_shares_#{@post.id}", @user_1.id.to_s, 1)

        expect(@post.unique_users_whatsapp_count).to eq(2)
      end
    end
  end

  describe "POST #share" do
    before :each do
      @user = FactoryBot.create(:user)

      @post = FactoryBot.create(:post, user: @user)
      AppVersionSupport.new('2305.01.01')
      @token = @user.generate_jwt_token
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = '2305.01.01'
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end
    context "share" do
      it "renders a JSON response with post updated" do
        post :share, params: { :post_id => @post.id }
        JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
      end

      it "update share count" do
        expect {
          post :share, params: { :post_id => @post.id }
        }.to change { @post.unique_users_whatsapp_count }.by(1)
      end

      it "get unique users share count" do
        post :share, params: { :post_id => @post.id }
        post :share, params: { :post_id => @post.id }

        JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(@post.unique_users_whatsapp_count).to eq(1)
      end

      it "get unique users share count when two users shared" do
        post :share, params: { :post_id => @post.id }

        # adding other user share count to redis
        @user_1 = FactoryBot.create(:user)
        $redis.hincrby("post_whatsapp_shares_#{@post.id}", @user_1.id.to_s, 1)

        expect(@post.unique_users_whatsapp_count).to eq(2)
      end
    end
  end

  describe "GET #get_post_opinions" do
    it "renders a JSON response with post opinions" do
      @user = FactoryBot.create(:user)
      @post = FactoryBot.create(:post, user: @user)
      AppVersionSupport.new('2308.01.01')
      @token = @user.generate_jwt_token
      request.headers['Authorization'] = "Bearer #{@token}"
      request.headers['X-App-Version'] = "2308.01.01"
      request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      @post_opinion = FactoryBot.create(:post, parent_post: @post)
      @post_opinion2 = FactoryBot.create(:post, parent_post: @post)
      @post_opinion3 = FactoryBot.create(:post, parent_post: @post)
      @post_opinion4 = FactoryBot.create(:post, parent_post: @post)
      @post_opinion5 = FactoryBot.create(:post, parent_post: @post)
      @post_opinion6 = FactoryBot.create(:post, parent_post: @post)

      get :get_post_opinions, params: { :post_id => @post.id, :count => 5, :last_opinion_id => @post_opinion6.id }
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body).count).to eq(5)
      expect(JSON.parse(response.body).first["id"]).to eq(@post_opinion5.id)
      expect(JSON.parse(response.body).last["id"]).to eq(@post_opinion.id)
    end
  end

  describe "PUT #delete" do
    it "renders a JSON response with post deleted" do
      @user = FactoryBot.create(:user)
      @post = FactoryBot.create(:post, user: @user)
      AppVersionSupport.new('2308.01.01')
      @token = @user.generate_jwt_token
      @request.headers['Authorization'] = "Bearer #{@token}"
      @request.headers['X-App-Version'] = "2308.01.01"
      @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      put :delete, params: { :post_id => @post.id }
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["message"]).to eq(I18n.t('posts.post_deleted'))

      @post.reload
      expect(@post.active).to be_falsey
    end
  end

  describe "PUT #report" do
    before :each do
      @user = FactoryBot.create(:user)
      @post = FactoryBot.create(:post)
      AppVersionSupport.new('2308.01.01')
      @token = @user.generate_jwt_token
      request.headers['Authorization'] = "Bearer #{@token}"
      request.headers['X-App-Version'] = "2308.01.01"
      request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end

    it "do report of a post with a reason having a name " do
      put :report, params: { :post_id => @post.id, :reason => { name: "ఇతరుల ప్రాపంచిక నియమాలను ఉల్లంఘించిన పోస్ట్" } }
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["message"]).to eq(I18n.t('posts.post_reported'))
    end

    it "don't report of a post without reason" do
      put :report, params: { :post_id => @post.id }
      expect(response).to have_http_status(:bad_request)
      expect(JSON.parse(response.body)["message"]).to eq(I18n.t('mention_reason'))
    end
  end

  describe "GET #liked_users" do
    before :each do
      @user = FactoryBot.create(:user)
      @post = FactoryBot.create(:post)
    end

    it "get liked users of a post with pagination for app version greater than '1.16.1'" do
      AppVersionSupport.new('2308.01.01')
      @token = @user.generate_jwt_token
      request.headers['Authorization'] = "Bearer #{@token}"
      request.headers['X-App-Version'] = "2308.01.01"
      request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      get :liked_users, params: { :post_id => @post.id, offset: 0, limit: 1 }
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)).to be_empty

      FactoryBot.create(:post_like, post: @post, user: @user)
      get :liked_users, params: { :post_id => @post.id }
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)).not_to be_empty
      expect(JSON.parse(response.body).count).to eq(1)
      expect(JSON.parse(response.body).first["id"]).to eq(@user.id)
    end

    it "get liked users of a post without pagination for app version less than '1.16.1'" do
      AppVersionSupport.new('1.16.0')
      @token = @user.generate_login_token(nil, nil)
      request.headers['Authorization'] = "Bearer #{@token}"
      request.headers['X-App-Version'] = "1.16.0"
      request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      FactoryBot.create(:post_like, post: @post, user: @user)
      FactoryBot.create(:post_like, post: @post)
      get :liked_users, params: { :post_id => @post.id, offset: 0, limit: 1 }
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)).not_to be_empty
      expect(JSON.parse(response.body).count).to eq(2)
      expect(JSON.parse(response.body).first["id"]).to eq(@user.id)
    end
  end

  describe "PUT #mark_as_seen" do
    it "queues the post to mark it as seen" do
      @user = FactoryBot.create(:user)
      @post = FactoryBot.create(:post)
      AppVersionSupport.new('2308.01.01')
      @token = @user.generate_jwt_token
      request.headers['Authorization'] = "Bearer #{@token}"
      request.headers['X-App-Version'] = "2308.01.01"
      request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      expect_any_instance_of(Post).to receive(:queue_mark_as_seen).with(@user.id)
      put :mark_as_seen, params: { :post_id => @post.id }
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["success"]).to be_truthy
    end
  end

  describe "PUT #mark_as_seen_bulk" do
    before :each do
      @user = FactoryBot.create(:user)
      @post = FactoryBot.create(:post)
      AppVersionSupport.new('2308.01.01')
      @token = @user.generate_jwt_token
      request.headers['Authorization'] = "Bearer #{@token}"
      request.headers['X-App-Version'] = "2308.01.01"
      request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
    end

    it "doesn't queue the posts to mark them as seen returns success as false if post_ids are empty" do
      put :mark_as_seen_bulk, params: { :post_ids => [] }
      expect(response).to have_http_status(:bad_request)
      expect(JSON.parse(response.body)["success"]).to be_falsey
    end

    it "queues the post to mark them as seen by triggering QueueUserPostViews worker and returns success as true" do
      @post2 = FactoryBot.create(:post)
      params = { @post.id.to_s => Time.zone.now.to_s, @post2.id.to_s => Time.zone.now.to_s }
      allow(QueueUserPostViews).to receive(:perform_async).with(@user.id, params).once
      put :mark_as_seen_bulk, params: { :post_ids => params }
      expect(QueueUserPostViews).to have_received(:perform_async).with(@user.id, params).once
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["success"]).to be_truthy
    end
  end

  describe "GET #get_share_text" do
    before :each do
      @user = FactoryBot.create(:user)
    end

    it "returns success as true and share text based if app version greater than '1.11.3'" do
      @post = FactoryBot.create(:post)
      AppVersionSupport.new('2308.01.01')
      @token = @user.generate_jwt_token
      request.headers['Authorization'] = "Bearer #{@token}"
      request.headers['X-App-Version'] = "2308.01.01"
      request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      post_long_link = "https://prajaapp.sng.link/A3x5b/oe88?" +
        "_dl=praja%3A%2F%2Fposts/#{@post.id}&" +
        "_android_redirect=https://m.praja.buzz/posts/#{@post.hashid}&" +
        "_ios_redirect=https://m.praja.buzz/posts/#{@post.hashid}&" +
        "_samsung_redirect=https://m.praja.buzz/posts/#{@post.hashid}&" +
        "paffid=#{@user.id}"

      post_link = Singular.shorten_link(post_long_link)

      get :get_share_text, params: { :post_id => @post.id }

      share_text = I18n.t('get_share_text.share_text_v2', post_link: post_link)

      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["success"]).to be_truthy
      expect(JSON.parse(response.body)["text"]).to eq(share_text)

      FactoryBot.create(:user_role, user_id: @user.id)

      user_role = @user.get_badge_role

      get :get_share_text, params: { :post_id => @post.id }
      badge_user_text = I18n.t('get_share_text.share_text_v1.badge_user_text', primary_circle: user_role.get_primary_circle.name, role_name: user_role.get_role_name, post_link: post_link)

      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["success"]).to be_truthy
      expect(JSON.parse(response.body)["text"]).to eq(badge_user_text)
    end

    it "returns success as true and share text based if app version less than '1.11.3'" do
      @post = FactoryBot.create(:post)
      AppVersionSupport.new('1.11.2')
      @token = @user.generate_jwt_token
      request.headers['Authorization'] = "Bearer #{@token}"
      request.headers['X-App-Version'] = "1.11.2"
      request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

      get :get_share_text, params: { :post_id => @post.id }
      expect(response).to have_http_status(:ok)

      FactoryBot.create(:user_role, user_id: @user.id)

      get :get_share_text, params: { :post_id => @post.id }
      expect(response).to have_http_status(:ok)
    end
  end

  describe "GET #notification_post_feed" do
    before :each do
      @notification_post = FactoryBot.create(:post)
      @state = FactoryBot.create(:circle, name: "Andhra Pradesh", level: :state, circle_type: :location)
      @district = FactoryBot.create(:circle, name: "Anantapur", parent_circle: @state, level: :district, circle_type: :location)
      @mandal = FactoryBot.create(:circle, name: "Anantapur", parent_circle: @district, level: :mandal, circle_type: :location)
      @village = FactoryBot.create(:circle, name: "Anantapur", parent_circle: @mandal, level: :village, circle_type: :location)
      @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id, state_id: @state.id)
      AppVersionSupport.new('2401.22.01')
      @token = @user.generate_jwt_token
      request.headers['Authorization'] = "Bearer #{@token}"
      request.headers['X-App-Version'] = "2401.22.01"
      request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      @post1 = FactoryBot.create(:post)
      @post2 = FactoryBot.create(:post)
    end

    it "returns success with notification post and user unseen trending feed post" do
      allow_any_instance_of(User).to receive(:get_post_view_ids).and_return([@post1.id])
      allow($redis).to receive(:zrangebyscore).and_return([@post2.id])
      get :notification_post_feed, params: { post_id: @notification_post.id }
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body).count).to eq(2)
      expect(JSON.parse(response.body).first["id"]).to eq(@notification_post.id)
      expect(JSON.parse(response.body).last["id"]).to eq(@post2.id)
    end

    it "returns success with only notification post even though only seen post from trending feed post" do
      allow_any_instance_of(User).to receive(:get_post_view_ids).and_return([@post2.id])
      allow($redis).to receive(:zrangebyscore).and_return([@post2.id])
      get :notification_post_feed, params: { post_id: @notification_post.id }
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body).count).to eq(1)
      expect(JSON.parse(response.body).first["id"]).to eq(@notification_post.id)
    end

    it "returns first post of trending feed which is not seen" do
      @post3 = FactoryBot.create(:post)
      @post4 = FactoryBot.create(:post)

      allow_any_instance_of(User).to receive(:get_post_view_ids).and_return([@post2.id, @post3.id])
      allow($redis).to receive(:zrangebyscore).and_return([@post2.id, @post3.id, @post4.id])
      get :notification_post_feed, params: { post_id: @notification_post.id }
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body).count).to eq(2)
      expect(JSON.parse(response.body).first["id"]).to eq(@notification_post.id)
      expect(JSON.parse(response.body).last["id"]).to eq(@post4.id)

      allow_any_instance_of(User).to receive(:get_post_view_ids).and_return([])
      allow($redis).to receive(:zrangebyscore).and_return([@post2.id, @post3.id, @post4.id])
      get :notification_post_feed, params: { post_id: @notification_post.id }
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body).count).to eq(2)
      expect(JSON.parse(response.body).first["id"]).to eq(@notification_post.id)
      expect(JSON.parse(response.body).last["id"]).to eq(@post2.id)

      allow_any_instance_of(User).to receive(:get_post_view_ids).and_return([@post2.id])
      allow($redis).to receive(:zrangebyscore).and_return([@notification_post.id, @post3.id, @post2.id])
      get :notification_post_feed, params: { post_id: @notification_post.id }
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body).count).to eq(2)
      expect(JSON.parse(response.body).first["id"]).to eq(@notification_post.id)
      expect(JSON.parse(response.body).last["id"]).to eq(@post3.id)
    end
  end
end
