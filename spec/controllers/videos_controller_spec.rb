require 'rails_helper'

RSpec.describe VideosController, type: :controller do
  describe "POST /upload-video" do
    context "returns" do
      before :each do
        @user = FactoryBot.create(:user)
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2302.28.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "false" do
        post :upload
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "POST /processing_update" do
    context "if no output url is present" do
      before :each do
        @user = FactoryBot.create(:user)
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2302.28.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]
      end

      it "returns false" do
        post :processing_update, body: { cdn_url: nil }.to_json, as: :json
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end

    context "if output url is present" do
      before :each do
        @user = FactoryBot.create(:user)
        @token = @user.generate_jwt_token
        @request.headers['Authorization'] = "Bearer #{@token}"
        @request.headers['X-App-Version'] = '2302.28.01'
        @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

        @post = FactoryBot.create(:post, user: @user)
        @video = FactoryBot.create(:video, hash_key: "d92f8e837b48ec5cba1bc18bcefe92b9")
        @post_video = FactoryBot.create(:post_video, post: @post, video: @video)
      end

      it "if video_hash_key is present returns updates video to processed" do
        post :processing_update, body: { cdn_url: "https://gpuv.thecircleapp.in/#{@user.id}/d92f8e837b48ec5cba1bc18bcefe92b9.m3u8"}.to_json, as: :json
        expect(response).to have_http_status(:ok)
      end
    end
  end
end