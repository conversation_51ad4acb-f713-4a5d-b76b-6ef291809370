require 'rails_helper'

RSpec.describe MediaServiceController, type: :controller do
  describe "GET /index" do
    context "check if the request is coming from valid service" do
      it "returns http unauthorized" do
        @token = JsonWebToken.encode({ service: 'dummy' })
        @request.headers['Authorization'] = "Bearer #{@token}"

        get "index"
        expect(response).to have_http_status(:unauthorized)
      end

      it "w/ invalid token, returns http unauthorized" do
        @request.headers['Authorization'] = "Bearer alskjdfhlsakdfhlkdsjfh"
        get "index"
        expect(response).to have_http_status(:unauthorized)
      end

      it "w/ other service valid token, returns unauthorized" do
        @token = JsonWebToken.encode({ service: 'dm' })
        @request.headers['Authorization'] = "Bearer #{@token}"

        get :index
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe "GET /index" do
    context "request from media service" do
      it "w/ valid token, returns http success" do
        @token = JsonWebToken.encode({ service: 'media' })
        @request.headers['Authorization'] = "Bearer #{@token}"

        get "index"
        expect(response).to have_http_status(:success)
      end
    end
  end

  describe "POST update-video-process" do
    context "request from media service" do
      before :each do
        video =
          {
            "file_id": "3bb2ae5b0476337bc6f2852f5825cbbf",
            "file_name": "3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
            "original_file_name": "sample_video.mp4",
            "path": "50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
            "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
            "thumbnail": {
              "path": "3bb2ae5b0476337bc6f2852f5825cbbf.jpg",
              "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.jpg"
            }
          }
        @post = FactoryBot.create(:post)
        @video = FactoryBot.create(:video, ms_data: video, service: :gcp)
        @post_video = FactoryBot.create(:post_video, post: @post, video: @video)

        @token = JsonWebToken.encode({ service: 'media' })
        @request.headers['Authorization'] = "Bearer #{@token}"
      end
      it "update the video status to processed" do
        post :update_video_process, params: { processed_url: "processed_url", metadata: {video_id: @video.id} }

        expect(response).to have_http_status(:success)

        @video.reload
        expect(@video.status).to eq("processed")
      end

      it "return unprocessable entity if params not present" do
        post :update_video_process

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it "return unprocessable entity if metadata not present" do
        post :update_video_process, params: { processed_url: "", metadata: { } }

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it "return unprocessable entity if processed_url not present" do
        post :update_video_process, params: { processed_url: "", metadata: { video_id: @video.id } }

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it "return unprocessable entity if video_id not present" do
        post :update_video_process,params: { processed_url: "", metadata: { video_id: nil } }

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "POST my_poster_page" do
    context "when user_id is not present" do
      before :each do
        @token = JsonWebToken.encode({ service: 'media' })
        @request.headers['Authorization'] = "Bearer #{@token}"
      end
      it "should return 'User not found' message" do
        post :my_poster_page
        expect(response).to have_http_status(:not_found)
        response_body = JSON.parse(response.body)
        expect(response_body["message"]).to eq("User not found")
      end
    end
    context "when creative_id is not present" do
      before :each do
        @user = FactoryBot.create(:user)
        @token = JsonWebToken.encode({ service: 'media' })
        @request.headers['Authorization'] = "Bearer #{@token}"
      end
      it "should return 'Creative not found' message" do
        post :my_poster_page, params: { user_id: @user.id }
        expect(response).to have_http_status(:not_found)
        response_body = JSON.parse(response.body)
        expect(response_body["message"]).to eq("Creative not found")
      end

      it "should return Layout not found message" do
        post :my_poster_page, params: { user_id: @user.id }
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        post :my_poster_page, params: { user_id: @user.id, creative_id: @poster_creative.id, frame_id: 102 }
        expect(response).to have_http_status(:not_found)
        response_body = JSON.parse(response.body)
        expect(response_body["message"]).to eq("Layout not found")
      end
    end

    context "when creative_id is present and frame_id is present" do
      before :each do
        @token = JsonWebToken.encode({ service: 'media' })
        @request.headers['Authorization'] = "Bearer #{@token}"
        @user = FactoryBot.create(:user, name: "testing")
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)
        AppVersionSupport.new('2306.27.01')

        @user_poster_layout =
          FactoryBot.create(:user_poster_layout, entity: @user,
                            h1_count: 1,
                            h2_count: 1,
                            user_leader_photos: [
                              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                header_type: :header_1, priority: 1),
                              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                header_type: :header_2, priority: 1)])
        @frame = FactoryBot.create(:frame)
        @user_frame = FactoryBot.create(:user_frame, user: @user, frame: @frame)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
      end
      it "should return layout and creative" do
        post :my_poster_page, params: { creative_id: @poster_creative.id, frame_id: @frame.id , user_id: @user.id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body["layout"]).not_to be_nil
        expect(response_body["creative"]).not_to be_nil
      end

      it "should return user not found message" do
        post :my_poster_page, params: { creative_id: @poster_creative.id, frame_id: @frame.id , user_id: 0 }
        expect(response).to have_http_status(:not_found)
        response_body = JSON.parse(response.body)
        expect(response_body["message"]).to eq("User not found")
      end
    end
  end
end
