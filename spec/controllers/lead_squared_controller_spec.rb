# frozen_string_literal: true

require 'rails_helper'
require "sidekiq/testing"
# Sidekiq::Testing.inline!

RSpec.describe LeadSquaredController, type: :controller do
  describe "POST #lead_stage_update" do
    before :all do
      @phone = "91#{Faker::Number.unique.number(digits: 8)}"
      @user = FactoryBot.create(:user, phone: @phone)
      @token = Base64.encode64("#{Rails.application.credentials[:lead_squared][:auth_username]}:#{Rails.application.credentials[:lead_squared][:auth_secret]}")
    end
    context "with valid params" do
      it "returns a success response" do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :lead_stage_update, params: { lead_squared: { After: { Phone: "+91-#{@phone}", ProspectStage: 'stage' } } }
        expect(response).to be_successful
      end
    end

    context "with invalid params" do
      it "returns a unprocessable_entity response" do
        request.headers['Authorization'] = "Bearer #{@token}"
        post :lead_stage_update, params: { lead_squared: { After: { Phone: "+91-#{@phone}", ProspectStage: '' } } }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end
end
