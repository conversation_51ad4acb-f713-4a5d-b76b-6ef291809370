require 'rails_helper'

RSpec.describe SendNotificationsForComment, type: :worker do
  context "send notifications for comment" do
    before :each do
      @user = FactoryBot.create(:user)
      @post = FactoryBot.create(:post, user: @user)
      @post_comment = FactoryBot.create(:post_comment, post: @post, user: @user)
    end

    it { is_expected.to be_processed_in :notifications }

    it { is_expected.to be_retryable 0 }

    it "check for the same argument in job been called" do
      described_class.perform_async(@post_comment.id)
      expect(described_class.jobs[0]['args']).to eq([@post_comment.id])
    end

    it "calls send_notification_for_post_creator and send_notification_for_other_commented_users methods" do
      expect_any_instance_of(PostComment).to receive(:send_notification_for_post_creator).once
      expect_any_instance_of(PostComment).to receive(:send_notification_for_other_commented_users).once

      SendNotificationsForComment.new.perform(@post_comment.id)
    end
  end
end
