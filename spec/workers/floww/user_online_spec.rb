# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Floww::UserOnline, type: :worker do
  it { is_expected.to be_processed_in :default }

  describe '#perform' do
    let(:user) { FactoryBot.create(:user) }

    context 'when user is not a floww contact yet' do
      it 'does not trigger user online' do
        allow(User).to receive(:find).and_return(user)
        allow(user).to receive(:get_floww_contact_id).and_return(nil)
        allow(FlowwApi).to receive(:update_user_online_new_activity)

        expect(user).to receive(:get_floww_contact_id)
        expect(FlowwApi).to_not receive(:update_user_online_new_activity)
        described_class.new.perform(user.id)
      end
    end

    context 'when user is a floww contact' do
      it 'triggers user online' do
        allow(User).to receive(:find).and_return(user)
        allow(user).to receive(:get_floww_contact_id).and_return('floww_contact_id')
        allow(user).to receive(:premium_poster_usage_count_after_trial_enabled).and_return(2)
        allow(FlowwApi).to receive(:update_user_online_new_activity)

        expect(FlowwApi).to receive(:update_user_online_new_activity)
        described_class.new.perform(user.id)
      end
    end
  end
end
