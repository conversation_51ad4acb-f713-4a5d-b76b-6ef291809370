# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Floww::IncompleteLayoutBadge, type: :worker do
  it { is_expected.to be_processed_in :default }

  describe '#perform' do
    let(:user) { FactoryBot.create(:user) }

    context 'when user is not a floww contact yet' do
      it 'does not trigger incomplete layout badge activity' do
        allow(User).to receive(:find_by_id).and_return(user)
        allow(user).to receive(:get_floww_contact_id).and_return(nil)
        allow(FlowwApi).to receive(:update_incomplete_layout_badge_activity)

        expect(user).to receive(:get_floww_contact_id)
        expect(FlowwApi).to_not receive(:update_incomplete_layout_badge_activity)
        described_class.new.perform(user.id)
      end
    end

    context 'when user is a floww contact' do
      it 'triggers incomplete layout badge activity' do
        allow(User).to receive(:find_by_id).and_return(user)
        allow(user).to receive(:get_floww_contact_id).and_return('floww_contact_id')
        allow(FlowwApi).to receive(:update_incomplete_layout_badge_activity)

        expect(user).to receive(:get_floww_contact_id)
        expect(Floww<PERSON><PERSON>).to receive(:update_incomplete_layout_badge_activity).with(user.id)
        described_class.new.perform(user.id)
      end
    end
  end
end
