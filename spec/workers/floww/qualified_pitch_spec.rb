# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Floww::QualifiedPitch, type: :worker do
  it { is_expected.to be_processed_in :default }

  describe '#perform' do
    let(:user) { FactoryBot.create(:user) }

    context 'when user is not a floww contact yet' do
      it 'does not trigger qualified pitch' do
        allow(User).to receive(:find).and_return(user)
        allow(user).to receive(:get_floww_contact_id).and_return(nil)
        allow(FlowwApi).to receive(:update_qualified_pitch_activity)

        expect(user).to receive(:get_floww_contact_id)
        expect(FlowwApi).to_not receive(:update_qualified_pitch_activity)
        described_class.new.perform(user.id)
      end
    end

    context 'when user is a floww contact' do
      it 'triggers qualified pitch' do
        allow(User).to receive(:find).and_return(user)
        allow(user).to receive(:get_floww_contact_id).and_return('floww_contact_id')
        allow(user).to receive(:premium_poster_usage_count_after_trial_enabled).and_return(10)
        allow(Metadatum).to receive(:get_user_trial_start_date_and_duration).and_return([Time.zone.today - 1.day, 30])
        allow(FlowwApi).to receive(:update_qualified_pitch_activity)

        expect(FlowwApi).to receive(:update_qualified_pitch_activity)
        described_class.new.perform(user.id)
      end
    end
  end
end
