require 'rails_helper'
require 'webmock/rspec'

RSpec.describe SendWhatsappMsgUsingCsv, type: :worker do
  let(:template_name) { 'welcome_template' }
  let(:broadcast_name) { 'new_users_broadcast' }
  let(:s3_object_path) { 'path/to/whatsapp_numbers.csv' }
  let(:csv_content) { "phone_number,name\n1234567890,<PERSON>\n0987654321,<PERSON>" }
  let(:wati_end_point) { 'https://wati-api.example.com/api/v1/sendTemplateMessageUsingCSV' }
  let(:wati_api_key) { 'wati-api-key-12345' }

  before do
    allow(Constants).to receive(:aws_s3_bucket_name_for_csvs).and_return('test-csv-bucket')
    allow(Constants).to receive(:get_send_message_wati_csv_url).and_return(wati_end_point)
    allow(Rails.application).to receive_message_chain(:credentials, :[]).with(:aws_access_key_id).and_return('test-access-key')
    allow(Rails.application).to receive_message_chain(:credentials, :[]).with(:aws_secret_access_key).and_return('test-secret-key')
    allow(Rails.application).to receive_message_chain(:credentials, :[]).with(:wati_api_key).and_return(wati_api_key)
  end

  describe "#perform" do
    before do
      @s3_object = instance_double(Aws::S3::Object)
      @s3_resource = instance_double(Aws::S3::Resource)
      @s3_bucket = instance_double(Aws::S3::Bucket)
      @s3_response = instance_double(Aws::S3::Types::GetObjectOutput)
      @http_response = instance_double(Net::HTTPResponse)
    end
    context "when S3 file exists and API call succeeds" do
      before do
        allow(Aws::S3::Resource).to receive(:new).and_return(@s3_resource)
        allow(@s3_resource).to receive(:bucket).with('test-csv-bucket').and_return(@s3_bucket)
        allow(@s3_bucket).to receive(:object).with(s3_object_path).and_return(@s3_object)
        allow(@s3_object).to receive(:get).and_return(@s3_response)
        allow(@s3_response).to receive_message_chain(:body, :read).and_return(csv_content)
        allow(@http_response).to receive(:code).and_return('200')
        allow(@http_response).to receive(:body).and_return('{"result": true}')
        allow_any_instance_of(Net::HTTP).to receive(:request).and_return(@http_response)
      end

      it "should send WATI successfully" do
        expect {
          subject.perform(template_name, broadcast_name, s3_object_path)
        }.not_to raise_error
      end

      it "should make HTTP request with correct parameters" do
        http_double = instance_double(Net::HTTP)
        allow(Net::HTTP).to receive(:new).and_return(http_double)

        expect(Net::HTTP).to receive(:new).with('wati-api.example.com', 443)
        expect(http_double).to receive(:use_ssl=).with(true)
        expect(http_double).to receive(:request) do |request|
          expect(request['Authorization']).to eq(wati_api_key)
          allow(@http_response).to receive(:code).and_return('200')
          allow(@http_response).to receive(:body).and_return('{"result": true}')
          @http_response
        end

        subject.perform(template_name, broadcast_name, s3_object_path)
      end
    end

    context "when URL cannot be formed" do
      before do
        allow(Constants).to receive(:get_send_message_wati_csv_url).and_return('invalid://url')
        allow(Aws::S3::Resource).to receive(:new).and_return(@s3_resource)
        allow(@s3_resource).to receive(:bucket).with('test-csv-bucket').and_return(@s3_bucket)
        allow(@s3_bucket).to receive(:object).with(s3_object_path).and_return(@s3_object)
        allow(@s3_object).to receive(:get).and_return(@s3_response)
        allow(@s3_response).to receive_message_chain(:body, :read).and_return(csv_content)
        uri_double = instance_double(URI::Generic)
        allow(uri_double).to receive(:host).and_return(nil)
        allow(URI).to receive(:parse).and_return(uri_double)
      end

      it "should raise an error" do
        expect {
          subject.perform(template_name, broadcast_name, s3_object_path)
        }.to raise_error("Unable to form URL")
      end
    end

    context "when API call returns non-200 response" do
      before do
        allow(Aws::S3::Resource).to receive(:new).and_return(@s3_resource)
        allow(@s3_resource).to receive(:bucket).with('test-csv-bucket').and_return(@s3_bucket)
        allow(@s3_bucket).to receive(:object).with(s3_object_path).and_return(@s3_object)
        allow(@s3_object).to receive(:get).and_return(@s3_response)
        allow(@s3_response).to receive_message_chain(:body, :read).and_return(csv_content)
        allow(@http_response).to receive(:code).and_return('400')
        allow(@http_response).to receive(:body).and_return('{"result": false, "error": "Bad request"}')
        allow_any_instance_of(Net::HTTP).to receive(:request).and_return(@http_response)
        allow(Honeybadger).to receive(:notify)
      end

      it "should notify Honeybadger" do
        expect(Honeybadger).to receive(:notify).with(
          "WATI CSV message trigger error: 400",
          context: {
            response_code: '400',
            response_body: {"result" => false, "error" => "Bad request"}
          }
        )
        subject.perform(template_name, broadcast_name, s3_object_path)
      end
    end

    context "when API call returns 200 but result is not true" do
      before do
        allow(Aws::S3::Resource).to receive(:new).and_return(@s3_resource)
        allow(@s3_resource).to receive(:bucket).with('test-csv-bucket').and_return(@s3_bucket)
        allow(@s3_bucket).to receive(:object).with(s3_object_path).and_return(@s3_object)
        allow(@s3_object).to receive(:get).and_return(@s3_response)
        allow(@s3_response).to receive_message_chain(:body, :read).and_return(csv_content)
        allow(@http_response).to receive(:code).and_return('200')
        allow(@http_response).to receive(:body).and_return('{"result": false, "message": "Invalid data"}')
        allow_any_instance_of(Net::HTTP).to receive(:request).and_return(@http_response)
        allow(Honeybadger).to receive(:notify)
      end

      it "should notify Honeybadger" do
        expect(Honeybadger).to receive(:notify).with(
          "WATI CSV message trigger error: 200",
          context: {
            response_code: '200',
            response_body: {"result" => false, "message" => "Invalid data"}
          }
        )
        subject.perform(template_name, broadcast_name, s3_object_path)
      end
    end

    context "when S3 operation fails" do
      before do
        allow(Aws::S3::Resource).to receive(:new).and_raise(Aws::S3::Errors::NotFound.new(nil, "The specified key does not exist."))
      end

      it "should raise the S3 error" do
        expect {
          subject.perform(template_name, broadcast_name, s3_object_path)
        }.to raise_error(Aws::S3::Errors::NotFound)
      end
    end
  end
end
