require 'rails_helper'
require 'webmock/rspec'

RSpec.describe EventTracker, type: :worker do
  describe '#perform' do
    before do
      @user = FactoryBot.create(:user)
      @event_name = 'event_name'
      @properties = { 'property' => 'value' }
      @user_device_token = FactoryBot.create(:user_device_token, user: @user, active: true, firebase_app_instance_id: 'firebase_app_instance_id', app_os: 'android')

      allow(Rails.application.credentials).to receive(:[]).with(:firebase_api_secret).and_return('firebase-api-secret')
    end

    context 'Sidekiq options' do
      it { is_expected.to be_processed_in :default }
      it { is_expected.to be_retryable 3 }
    end


    context 'calls Mixpanel tracker' do
      it 'tracks the event on Mixpanel' do
        expect($mixpanel_tracker).to receive(:track).with(@user.id, @event_name, @properties)
        described_class.new.perform(@user.id, @event_name, @properties)
      end
    end

    context 'calls Firebase' do
      it 'sends event data to Firebase' do
        stub_request(:post, "#{Constants.get_firebase_url}?api_secret=firebase-api-secret&firebase_app_id=firebase-app-id").
          with(
            body: {
              app_instance_id: 'firebase_instance_id',
              events: [
                { name: @event_name, params: @properties.merge(user_id: @user.id) }
              ]
            }.to_json,
            headers: { 'Content-Type' => 'application/json' }
          ).
          to_return(status: 200)

        described_class.new.perform(@user.id, @event_name, @properties)
      end
    end

  end
end
