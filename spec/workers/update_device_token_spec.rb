require "rails_helper"

RSpec.describe UpdateDeviceToken do
  describe "#perform" do
    context 'sidekiq_options' do
      it { is_expected.to be_retryable 0 }
      it { is_expected.to be_processed_in :low }
    end

    context 'when args is not empty' do
      before :each do
        @user = FactoryBot.create(:user)
        @device_token = "123"
        @app_version = '1.3.6'
        @app_os = 'android'
        @device_id = 'device-id'
        @device_make = 'device-make'
        @device_model = 'device-model'

        @user_device_token = FactoryBot.create(:user_device_token, user: @user)
        @user_device_token2 = FactoryBot.create(:user_device_token, device_token: @device_token, active: false)
        @user_device_token3 = FactoryBot.create(:user_device_token, device_token: @device_token, user: @user, app_version: @app_version, app_os: @app_os)
      end

      it 'creates a new user device token if device_token has no user_device_token records' do
        allow(UserDeviceToken).to receive_message_chain(:where, :exists?).and_return([])
        allow(UserDeviceToken).to receive_message_chain(:where, :delete_all).and_return([@user_device_token2])
        allow(UserDeviceToken).to receive(:create).and_return(@user_device_token3)

        described_class.new.perform(@device_token, @user.id, @app_version, @app_os,
                                    @device_id, @device_make, @device_model)

        expect(UserDeviceToken).to have_received(:create).with(device_token: @device_token,
                                                               user: @user,
                                                               app_version: @app_version,
                                                               app_os: @app_os,
                                                               device_id: @device_id,
                                                               device_make: @device_make,
                                                               device_model: @device_model,
                                                               mapped_device_id: an_instance_of(Integer))
      end

      it 'do not create if device_token has user_device_token records' do
        allow(UserDeviceToken).to receive_message_chain(:where, :exists?).and_return([@user_device_token])
        allow(UserDeviceToken).to receive_message_chain(:where, :update_all).and_return([@user_device_token2])
        allow(UserDeviceToken).to receive(:create)

        described_class.new.perform(@user_device_token.device_token, @user.id, @app_version, @app_os,
                                    @device_id, @device_make, @device_model)

        expect(UserDeviceToken).not_to have_received(:create)
      end
    end
  end
end
