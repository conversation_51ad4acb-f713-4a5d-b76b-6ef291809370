require 'rails_helper'

RSpec.describe GenerateCirclePreviewImage, type: :worker do
  describe '#perform' do
    context 'when circle owner is not verified' do
      before :each do
        data =
          {
            'file_id': '0c8f194b1be9391a18d4da433e03acfa',
            'file_name': '0c8f194b1be9391a18d4da433e03acfa.jpg',
            'original_file_name': '1645707.jpg',
            'path': '50/0c8f194b1be9391a18d4da433e03acfa.jpg',
            'cdn_url': 'https://cdn.thecircleapp.in/50/0c8f194b1be9391a18d4da433e03acfa.jpg',
          }
        @photo = FactoryBot.create(:photo, ms_data: data, service: :azure)
        @photo.set_photo_data
        @circle = FactoryBot.create(:circle, photo: @photo)
        @user = FactoryBot.create(:user)
        @permission_group = FactoryBot.build(:permission_group, name: 'owner')
        @user_circle_permission_group = FactoryBot.build(:user_circle_permission_group, user_id: @user.id, circle_id: @circle.id, permission_group: @permission_group)
      end

      subject { described_class.new }

      it 'generates un-official circle preview' do
        allow(subject).to receive(:generate_html).with(@circle.name, instance_of(String), false).and_return('sample-html')
        allow(subject).to receive(:capture_html_as_image).and_return({ 'cdn_url' => 'sample-circle-preview-image-url' })
        subject.perform(@circle.id)

        value = Metadatum.where(entity: @circle, key: 'circle_preview_image_url').first.value
        expect(value).to eq('sample-circle-preview-image-url')
      end
    end

    context 'when circle owner is verified' do
      before :each do
        data =
          {
            'file_id': '0c8f194b1be9391a18d4da433e03acfa',
            'file_name': '0c8f194b1be9391a18d4da433e03acfa.jpg',
            'original_file_name': '1645707.jpg',
            'path': '50/0c8f194b1be9391a18d4da433e03acfa.jpg',
            'cdn_url': 'https://cdn.thecircleapp.in/50/0c8f194b1be9391a18d4da433e03acfa.jpg',
          }
        @photo = FactoryBot.create(:photo, ms_data: data, service: :azure)
        @photo.set_photo_data
        @circle = FactoryBot.create(:circle, photo: @photo, conversation_type: :channel)
      end

      subject { described_class.new }

      it 'generates official circle preview' do
        allow(subject).to receive(:generate_html).with(@circle.name, instance_of(String), true).and_return('sample-html')
        allow(subject).to receive(:capture_html_as_image).and_return({ 'cdn_url' => 'sample-circle-preview-image-url' })
        subject.perform(@circle.id)

        value = Metadatum.where(entity: @circle, key: 'circle_preview_image_url').first.value
        expect(value).to eq('sample-circle-preview-image-url')
      end
    end
  end
end
