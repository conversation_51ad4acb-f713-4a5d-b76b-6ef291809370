require 'rails_helper'

RSpec.describe IndexRedWord, type: :worker do
  describe 'perform' do
    let(:red_word) { FactoryBot.create(:red_word) }

    it { is_expected.to be_processed_in :low }

    it 'calls index_red_word' do
      expect_any_instance_of(described_class).to receive(:perform_index).with(red_word.id, red_word.word, red_word.is_hate_speech)
      described_class.new.perform(red_word.id, red_word.word, red_word.is_hate_speech)
    end

    it 'calls perform_request' do
      allow(ES_CLIENT).to receive(:method_missing).with(:perform_request, 'POST',
                                                        "#{EsUtil.get_red_word_index}/_doc/#{red_word.id}",
                                                        {},
                                                        { red_word: red_word.word, is_hate_speech: red_word.is_hate_speech }
      )
      described_class.new.perform(red_word.id, red_word.word, red_word.is_hate_speech)
      expect(ES_CLIENT).to have_received(:method_missing).with(:perform_request, 'POST',
                                                               "#{EsUtil.get_red_word_index}/_doc/#{red_word.id}",
                                                               {},
                                                               { red_word: red_word.word, is_hate_speech: red_word.is_hate_speech }
      )
    end
  end
end
