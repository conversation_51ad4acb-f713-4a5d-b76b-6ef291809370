require "rails_helper"

RSpec.describe QueuePostView, type: :worker do
  it { is_expected.to be_processed_in :critical }

  it { is_expected.to be_retryable 1 }

  context "perform" do
    let(:post) { FactoryBot.create(:post) }
    let(:user) { FactoryBot.create(:user) }

    it "adds post view to redis" do
      allow($redis).to receive(:sadd)
      
      described_class.new.perform(post.id, user.id)

      expect($redis).to have_received(:sadd)
    end
  end
end
