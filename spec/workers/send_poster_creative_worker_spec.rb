require 'rails_helper'

RSpec.describe SendPosterCreativeWorker, type: :worker do
  context "send poster creative worker" do
    it { is_expected.to be_processed_in :default }
    it { is_expected.to be_retryable 0 }
  end

  context "perform" do
    before :each do
      allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 201, body: { success: true }.to_json))
      @circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private)
      @circle.conversation_type = :channel
      @circle.save!
      image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
      image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
      @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
      @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
      @poster_creative = FactoryBot.build(:poster_creative,
                                          photo_v3: @admin_medium_1,
                                          photo_v2: @admin_medium_2,
                                          primary: true,
                                          paid: false,
                                          poster_creative_circles: [FactoryBot.build(:poster_creative_circle, circle: @circle)])
      @circle_package = FactoryBot.create(:circle_package)
      FactoryBot.create(:circle_package_mapping, circle_package: @circle_package, circle: @circle)
      FactoryBot.create(:circle_monthly_usage, circle: @circle)
    end

    it "should send poster creative message if circle has channel" do
      @admin_user = FactoryBot.create(:admin_user)
      @user = User.find_by_phone(Constants.praja_account_phone)
      @poster_creative.creator = @admin_user
      @poster_creative.save!

      allow(SendDmPosterCreativeMessage).to receive(:perform_async)
      SendPosterCreativeWorker.new.perform(@poster_creative.id, true, "Test message")
      expect(SendDmPosterCreativeMessage).to have_received(:perform_async).once
      expect(Metadatum.where(entity: @poster_creative, key: Constants.poster_creative_jid_key).count).to eq(0)
    end

    it "should not send poster creative message if circle has no channel" do
      # an unlimited circle package mapping will be created for the circle
      # need to delete it
      @circle.active_circle_package_mapping.destroy
      allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 200, body: { success: true }.to_json))
      @circle.conversation_type = :none
      @circle.save!
      # commenting out as checks to send post or poster has been removed
      # allow_any_instance_of(Circle).to receive(:can_send_poster_message).and_return(true)
      # allow_any_instance_of(Circle).to receive(:can_send_with_notification).and_return(true)

      @admin_user = FactoryBot.create(:admin_user)
      @user = User.find_by_phone(Constants.praja_account_phone)
      @poster_creative.creator = @admin_user
      @poster_creative.save!

      allow(SendDmPosterCreativeMessage).to receive(:perform_async)
      SendPosterCreativeWorker.new.perform(@poster_creative.id, true, "Test message")
      expect(SendDmPosterCreativeMessage).not_to have_received(:perform_async)
      expect(Metadatum.where(entity: @poster_creative, key: Constants.poster_creative_jid_key).count).to eq(0)
    end

    # it "should not send poster creative message if circle has channel and is not eligible to send dm message" do
    #   allow_any_instance_of(Circle).to receive(:can_send_poster_message).and_return(false)
    #   allow_any_instance_of(Circle).to receive(:can_send_with_notification).and_return(true)
    #   @admin_user = FactoryBot.create(:admin_user)
    #   @user = User.find_by_phone(Constants.praja_account_phone)
    #   @poster_creative.creator = @admin_user
    #   @poster_creative.save!
    #
    #   allow(SendDmPosterCreativeMessage).to receive(:perform_async)
    #   SendPosterCreativeWorker.new.perform(@poster_creative.id, true, "Test message")
    #   expect(SendDmPosterCreativeMessage).not_to have_received(:perform_async)
    #   expect(Metadatum.where(entity: @poster_creative, key: Constants.poster_creative_jid_key).count).to eq(0)
    # end

    # it "should not send poster creative message if circle has channel and is not eligible to send dm message with notification" do
    #   allow_any_instance_of(Circle).to receive(:can_send_poster_message).and_return(true)
    #   allow_any_instance_of(Circle).to receive(:can_send_with_notification).and_return(false)
    #   @admin_user = FactoryBot.create(:admin_user)
    #   @user = User.find_by_phone(Constants.praja_account_phone)
    #   @poster_creative.creator = @admin_user
    #   @poster_creative.save!
    #
    #   allow(SendDmPosterCreativeMessage).to receive(:perform_async)
    #   SendPosterCreativeWorker.new.perform(@poster_creative.id, true, "Test message")
    #   expect(SendDmPosterCreativeMessage).not_to have_received(:perform_async)
    #   expect(Metadatum.where(entity: @poster_creative, key: Constants.poster_creative_jid_key).count).to eq(0)
    # end

    # it "should send poster creative message if without notification and has no limit" do
    #   allow_any_instance_of(Circle).to receive(:can_send_poster_message).and_return(true)
    #   allow_any_instance_of(Circle).to receive(:can_send_with_notification).and_return(false)
    #   @admin_user = FactoryBot.create(:admin_user)
    #   @user = User.find_by_phone(Constants.praja_account_phone)
    #   @poster_creative.creator = @admin_user
    #   @poster_creative.save!
    #
    #   allow(SendDmPosterCreativeMessage).to receive(:perform_async)
    #   SendPosterCreativeWorker.new.perform(@poster_creative.id, false, "Test message")
    #   expect(SendDmPosterCreativeMessage).to have_received(:perform_async)
    #   expect(Metadatum.where(entity: @poster_creative, key: Constants.poster_creative_jid_key).count).to eq(0)
    # end

    it "should not send if poster creative is not active" do
      # allow_any_instance_of(Circle).to receive(:can_send_poster_message).and_return(true)
      # allow_any_instance_of(Circle).to receive(:can_send_with_notification).and_return(true)
      @poster_creative.primary = false
      @poster_creative.active = false
      @poster_creative.save!
      allow(SendDmPosterCreativeMessage).to receive(:perform_async)
      SendPosterCreativeWorker.new.perform(@poster_creative.id, true, "Test message")
      expect(SendDmPosterCreativeMessage).not_to have_received(:perform_async)
      expect(Metadatum.where(entity: @poster_creative, key: Constants.poster_creative_jid_key).count).to eq(0)
    end

    it "should not send if poster creative is not in the time range" do
      # allow_any_instance_of(Circle).to receive(:can_send_poster_message).and_return(true)c
      # allow_any_instance_of(Circle).to receive(:can_send_with_notification).and_return(true)
      @poster_creative.start_time = Time.zone.now + 1.day
      @poster_creative.end_time = Time.zone.now + 2.days
      @poster_creative.save!
      allow(SendDmPosterCreativeMessage).to receive(:perform_async)
      SendPosterCreativeWorker.new.perform(@poster_creative.id, true, "Test message")
      expect(SendDmPosterCreativeMessage).not_to have_received(:perform_async)
      expect(Metadatum.where(entity: @poster_creative, key: Constants.poster_creative_jid_key).count).to eq(0)
    end
  end
end
