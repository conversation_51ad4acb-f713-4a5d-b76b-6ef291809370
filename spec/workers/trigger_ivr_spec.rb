require 'rails_helper'
require 'webmock/rspec'

RSpec.describe TriggerIvr, type: :worker do
  let(:user) { create(:user, phone: '**********') }
  let(:user_id) { user.id }
  let(:twilio_account_sid) { 'account_sid' }
  let(:twilio_auth_token) { 'auth_token' }
  let(:url) { "https://api.twilio.com/2010-04-01/Accounts/#{twilio_account_sid}/Calls.json" }

  before :each do
    allow(Rails.application.credentials).to receive(:[]).with(:twilio_account_sid).and_return(twilio_account_sid)
    allow(Rails.application.credentials).to receive(:[]).with(:twilio_auth_token).and_return(twilio_auth_token)
    stub_request(:post, url).to_return(status: 200, body: "", headers: {})
  end

  describe '#perform' do
    context 'when user exists and has a phone number' do
      it 'send the analytics' do
        allow(EventTracker).to receive(:perform_async)
        described_class.new.perform(user_id)
        expect(EventTracker).to have_received(:perform_async).with(user_id, "charge_ivr_initiated", {})
      end

      it 'makes a call to Twilio API with correct parameters' do
        described_class.new.perform(user_id)
        expect(WebMock).to have_requested(:post, url)
      end
    end

    context 'when user does not exist' do
      it 'does not trigger any event or API call' do
        allow(EventTracker).to receive(:perform_async)
        response = described_class.new.perform(nil)
        expect(response).to be_nil
        expect(EventTracker).not_to have_received(:perform_async)
        expect(WebMock).not_to have_requested(:post, url)
      end
    end

    context 'when user has no phone number' do
      let(:user_without_phone) { create(:user, phone: nil) }
      it 'does not call Twilio API' do
        described_class.new.perform(user_without_phone.id)
        expect(WebMock).not_to have_requested(:post, url)
      end
    end
  end
end
