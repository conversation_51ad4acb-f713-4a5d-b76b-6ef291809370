require 'rails_helper'

RSpec.describe CheckForLatestNotification, type: :worker do
  describe '#perform' do
    let(:post) { FactoryBot.create(:post) }
    let(:notification_time) { Time.now.to_i }

    context 'sidekiq options as' do
      it { is_expected.to be_processed_in :notifications }

      it { is_expected.to be_retryable 0 }
    end

    context 'calls' do
      it 'calls send_like_notification method on post if post is active' do
        allow($redis).to receive(:get).and_return(notification_time)

        expect_any_instance_of(Post).to receive(:send_like_notification)
        described_class.new.perform(post.id, notification_time)
      end

      it 'does not call send_like_notification method on post if post is not active' do
        allow_any_instance_of(Post).to receive(:active?).and_return(false)
        allow($redis).to receive(:get).and_return(notification_time)

        expect_any_instance_of(Post).not_to receive(:send_like_notification)
        described_class.new.perform(post.id, notification_time)
      end

      it 'does not call send_like_notification method on post if post is nil' do
        allow(post).to receive(:active?).and_return(true)
        allow(Post).to receive(:find).and_return(nil)

        expect_any_instance_of(Post).not_to receive(:send_like_notification)
        described_class.new.perform(post.id, notification_time)
      end

      it 'does not call send_like_notification method on post if notification_time is not equal to redis value' do
        allow($redis).to receive(:get).and_return(notification_time + 1)

        expect_any_instance_of(Post).not_to receive(:send_like_notification)
        described_class.new.perform(post.id, notification_time)
      end
    end
  end
end
