# frozen_string_literal: true

require 'rails_helper'

RSpec.describe IncreasePremiumPitchCountWorker, type: :worker do
  it { is_expected.to be_retryable 0 }
  it { is_expected.to be_processed_in :default }

  context 'when valid user id comes' do
    it 'increases the premium pitch count' do
      user = FactoryBot.create(:user)

      described_class.new.perform(user.id)

      expect(user.get_premium_pitch_view_count).to eq(1)
    end
  end

  context 'when invalid user id comes' do
    it 'does not increase the premium pitch count' do
      described_class.new.perform(rand(10000))

      expect_any_instance_of(User).not_to receive(:update_pitch_premium_layout_view_count)
    end
  end
end
