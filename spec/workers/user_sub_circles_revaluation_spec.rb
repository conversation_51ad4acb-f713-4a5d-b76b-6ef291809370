# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UserSubCirclesRevaluation, type: :worker do
  context "user sub circles revaluation" do

    it { is_expected.to be_processed_in :default }

    it { is_expected.to be_retryable 1 }

    it "returns if user_id is nil" do
      described_class.new.perform(nil)
      expect(User).not_to receive(:find_by).with(user_id: nil)
    end

    it "returns if user does not exist" do
      expect(User).to receive(:find_by).with(id: -1).and_return(nil)
      described_class.new.perform(-1)
    end

    it "should join user to the sub_circles" do
      state = FactoryBot.create(:circle , circle_type: :location, level: :state)
      district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state)
      mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district)
      village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
      political = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      political2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      sub_circle1 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political)
      sub_circle2 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political2)
      user1 = FactoryBot.create(:user, village_id: village.id, mandal_id: mandal.id, district_id: district.id, state_id: state.id)
      user_circle1 = FactoryBot.create(:user_circle, user: user1, circle: village)
      user_circle2 = FactoryBot.create(:user_circle, user: user1, circle: political)
      user_circle3 = FactoryBot.create(:user_circle, user: user1, circle: sub_circle2)
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_location_circle_id', filter_value: village.id)
      expect(UserCircle).to receive(:create!).with(user_id: user1.id, circle_id: sub_circle1.id).once
      described_class.new.perform(user1.id)
    end

    it "should not do anything as there is no relation for the existing sub circles for the user" do
      state = FactoryBot.create(:circle , circle_type: :location, level: :state)
      district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state)
      mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district)
      village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
      political = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      political2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      sub_circle1 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political)
      sub_circle2 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political2)
      user1 = FactoryBot.create(:user, village_id: village.id, mandal_id: mandal.id, district_id: district.id, state_id: state.id)
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_location_circle_id', filter_value: village.id)
      expect(UserCircle).not_to receive(:create!)
      described_class.new.perform(user1.id)
    end
    
    it "user should join the sub circles with role with purview and location filters" do
      state = FactoryBot.create(:circle , circle_type: :location, level: :state)
      district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state)
      mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district)
      village_circle = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
      village_circle2 = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
      political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      political2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      sub_circle1 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political_circle)
      sub_circle2 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political2)
      user1 = FactoryBot.create(:user, village_id: village_circle.id, mandal_id: mandal.id, district_id: district.id, state_id: state.id)
      admin_user = FactoryBot.create(:admin_user)
      badge_icon_group = FactoryBot.create(:badge_icon_group, circle_id: political_circle.id)
      data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
      admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: admin_user.id)
      badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :GOLD,
                                     badge_icon_group_id: badge_icon_group.id)
      user_circle1 = FactoryBot.create(:user_circle, user: user1, circle: village_circle)
      user_circle2 = FactoryBot.create(:user_circle, user: user1, circle: political_circle)
      user_circle3 = FactoryBot.create(:user_circle, user: user1, circle: sub_circle2)
      role1 = FactoryBot.create(:role, grade_level: :grade_4, purview_level: :village, has_purview: true)
      role2 = FactoryBot.create(:role, grade_level: :grade_4, purview_level: :village, has_purview: true)
      user1 = FactoryBot.create(:user, village_id: village_circle.id)
      user2 = FactoryBot.create(:user, village_id: village_circle.id)
      user_role1 = FactoryBot.create(:user_role, user: user1, role: role1, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id, purview_circle_id: village_circle2.id)
      user_role2 = FactoryBot.create(:user_role, user: user2, role: role2, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id, purview_circle_id: village_circle2.id)

      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_location_circle_id', filter_value: village_circle.id)
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_role_id', filter_value: role1.id)
      expect(UserCircle).to receive(:create!).with(user_id: user1.id, circle_id: sub_circle1.id).once
      described_class.new.perform(user1.id)
    end
    it "user should join the sub circles with role without purview and location filters" do
      state = FactoryBot.create(:circle , circle_type: :location, level: :state)
      district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state)
      mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district)
      village_circle = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
      political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      political2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      sub_circle1 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political_circle)
      sub_circle2 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political2)
      user1 = FactoryBot.create(:user, village_id: village_circle.id, mandal_id: mandal.id, district_id: district.id, state_id: state.id)
      admin_user = FactoryBot.create(:admin_user)
      badge_icon_group = FactoryBot.create(:badge_icon_group, circle_id: political_circle.id)
      data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
      admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: admin_user.id)
      badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :GOLD,
                                     badge_icon_group_id: badge_icon_group.id)
      user_circle1 = FactoryBot.create(:user_circle, user: user1, circle: village_circle)
      user_circle2 = FactoryBot.create(:user_circle, user: user1, circle: political_circle)
      user_circle3 = FactoryBot.create(:user_circle, user: user1, circle: sub_circle2)
      role1 = FactoryBot.create(:role, grade_level: :grade_4)
      role2 = FactoryBot.create(:role, grade_level: :grade_4)
      user1 = FactoryBot.create(:user, village_id: village_circle.id)
      user2 = FactoryBot.create(:user, village_id: village_circle.id)
      user_role1 = FactoryBot.create(:user_role, user: user1, role: role1, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id)
      user_role2 = FactoryBot.create(:user_role, user: user2, role: role2, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id)

      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_location_circle_id', filter_value: village_circle.id)
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_role_id', filter_value: role1.id)
      expect(UserCircle).to receive(:create!).with(user_id: user1.id, circle_id: sub_circle1.id).once
      described_class.new.perform(user1.id)
    end

    it "user should join the sub circles with role without purview" do
      state = FactoryBot.create(:circle , circle_type: :location, level: :state)
      district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state)
      mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district)
      village_circle = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
      political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      political2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      sub_circle1 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political_circle)
      sub_circle2 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political2)
      user1 = FactoryBot.create(:user, village_id: village_circle.id, mandal_id: mandal.id, district_id: district.id, state_id: state.id)
      admin_user = FactoryBot.create(:admin_user)
      badge_icon_group = FactoryBot.create(:badge_icon_group, circle_id: political_circle.id)
      data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
      admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: admin_user.id)
      badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :GOLD,
                                     badge_icon_group_id: badge_icon_group.id)
      user_circle1 = FactoryBot.create(:user_circle, user: user1, circle: village_circle)
      user_circle2 = FactoryBot.create(:user_circle, user: user1, circle: political_circle)
      user_circle3 = FactoryBot.create(:user_circle, user: user1, circle: sub_circle2)
      role1 = FactoryBot.create(:role, grade_level: :grade_4)
      role2 = FactoryBot.create(:role, grade_level: :grade_4)
      user1 = FactoryBot.create(:user, village_id: village_circle.id)
      user2 = FactoryBot.create(:user, village_id: village_circle.id)
      user_role1 = FactoryBot.create(:user_role, user: user1, role: role1, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id)
      user_role2 = FactoryBot.create(:user_role, user: user2, role: role2, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id)
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_role_id', filter_value: role1.id)
      expect(UserCircle).to receive(:create!).with(user_id: user1.id, circle_id: sub_circle1.id).once
      described_class.new.perform(user1.id)
    end

    it "user should join the sub circles with role with purview " do
      state = FactoryBot.create(:circle , circle_type: :location, level: :state)
      district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state)
      mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district)
      village_circle = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
      village_circle2 = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
      political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      political2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      sub_circle1 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political_circle)
      sub_circle2 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political2)
      user1 = FactoryBot.create(:user, village_id: village_circle.id, mandal_id: mandal.id, district_id: district.id, state_id: state.id)
      admin_user = FactoryBot.create(:admin_user)
      badge_icon_group = FactoryBot.create(:badge_icon_group, circle_id: political_circle.id)
      data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
      admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: admin_user.id)
      badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :GOLD,
                                     badge_icon_group_id: badge_icon_group.id)
      user_circle1 = FactoryBot.create(:user_circle, user: user1, circle: village_circle)
      user_circle2 = FactoryBot.create(:user_circle, user: user1, circle: political_circle)
      user_circle3 = FactoryBot.create(:user_circle, user: user1, circle: sub_circle2)
      role1 = FactoryBot.create(:role, grade_level: :grade_4, purview_level: :village, has_purview: true)
      role2 = FactoryBot.create(:role, grade_level: :grade_4, purview_level: :village, has_purview: true)
      user1 = FactoryBot.create(:user, village_id: village_circle.id)
      user2 = FactoryBot.create(:user, village_id: village_circle.id)
      user_role1 = FactoryBot.create(:user_role, user: user1, role: role1, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id, purview_circle_id: village_circle2.id)
      user_role2 = FactoryBot.create(:user_role, user: user2, role: role2, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id, purview_circle_id: village_circle2.id)

      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_role_id', filter_value: role1.id)
      expect(UserCircle).to receive(:create!).with(user_id: user1.id, circle_id: sub_circle1.id).once
      described_class.new.perform(user1.id)
    end

    it "user should join the sub circles with role with purview with only grade level filters" do
      state = FactoryBot.create(:circle , circle_type: :location, level: :state)
      district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state)
      mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district)
      village_circle = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
      village_circle2 = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
      political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      political2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      sub_circle1 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political_circle)
      sub_circle2 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political2)
      user1 = FactoryBot.create(:user, village_id: village_circle.id, mandal_id: mandal.id, district_id: district.id, state_id: state.id)
      admin_user = FactoryBot.create(:admin_user)
      badge_icon_group = FactoryBot.create(:badge_icon_group, circle_id: political_circle.id)
      data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
      admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: admin_user.id)
      badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :GOLD,
                                     badge_icon_group_id: badge_icon_group.id)
      user_circle1 = FactoryBot.create(:user_circle, user: user1, circle: village_circle)
      user_circle2 = FactoryBot.create(:user_circle, user: user1, circle: political_circle)
      user_circle3 = FactoryBot.create(:user_circle, user: user1, circle: sub_circle2)
      role1 = FactoryBot.create(:role, grade_level: :grade_4, purview_level: :village, has_purview: true)
      role2 = FactoryBot.create(:role, grade_level: :grade_4, purview_level: :village, has_purview: true)
      user1 = FactoryBot.create(:user, village_id: village_circle.id)
      user2 = FactoryBot.create(:user, village_id: village_circle.id)
      user_role1 = FactoryBot.create(:user_role, user: user1, role: role1, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id, purview_circle_id: village_circle2.id)
      user_role2 = FactoryBot.create(:user_role, user: user2, role: role2, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id, purview_circle_id: village_circle2.id)

      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_grade_level', filter_value: "5")
      expect(UserCircle).to receive(:create!).with(user_id: user1.id, circle_id: sub_circle1.id).once
      described_class.new.perform(user1.id)
    end

    it "user should join the sub circles with role with purview with only grade level filters and location filters" do
      state = FactoryBot.create(:circle , circle_type: :location, level: :state)
      district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state)
      mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district)
      village_circle = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
      village_circle2 = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
      political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      political2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      sub_circle1 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political_circle)
      sub_circle2 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political2)
      user1 = FactoryBot.create(:user, village_id: village_circle.id, mandal_id: mandal.id, district_id: district.id, state_id: state.id)
      admin_user = FactoryBot.create(:admin_user)
      badge_icon_group = FactoryBot.create(:badge_icon_group, circle_id: political_circle.id)
      data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
      admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: admin_user.id)
      badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :GOLD,
                                     badge_icon_group_id: badge_icon_group.id)
      user_circle1 = FactoryBot.create(:user_circle, user: user1, circle: village_circle)
      user_circle2 = FactoryBot.create(:user_circle, user: user1, circle: political_circle)
      user_circle3 = FactoryBot.create(:user_circle, user: user1, circle: sub_circle2)
      role1 = FactoryBot.create(:role, grade_level: :grade_4, purview_level: :village, has_purview: true)
      role2 = FactoryBot.create(:role, grade_level: :grade_4, purview_level: :village, has_purview: true)
      user1 = FactoryBot.create(:user, village_id: village_circle.id)
      user2 = FactoryBot.create(:user, village_id: village_circle.id)
      user_role1 = FactoryBot.create(:user_role, user: user1, role: role1, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id, purview_circle_id: village_circle2.id)
      user_role2 = FactoryBot.create(:user_role, user: user2, role: role2, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id, purview_circle_id: village_circle2.id)

      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_grade_level', filter_value: "5")
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_location_circle_id', filter_value: village_circle.id)
      expect(UserCircle).to receive(:create!).with(user_id: user1.id, circle_id: sub_circle1.id).once
      described_class.new.perform(user1.id)
    end

    it "user should join the sub circles with role with purview with only grade level filters and role filters" do

      state = FactoryBot.create(:circle , circle_type: :location, level: :state)
      district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state)
      mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district)
      village_circle = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
      village_circle2 = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
      political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      political2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      sub_circle1 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political_circle)
      sub_circle2 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political2)
      user1 = FactoryBot.create(:user, village_id: village_circle.id, mandal_id: mandal.id, district_id: district.id, state_id: state.id)
      admin_user = FactoryBot.create(:admin_user)
      badge_icon_group = FactoryBot.create(:badge_icon_group, circle_id: political_circle.id)
      data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
      admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: admin_user.id)
      badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :GOLD,
                                     badge_icon_group_id: badge_icon_group.id)
      user_circle1 = FactoryBot.create(:user_circle, user: user1, circle: village_circle)
      user_circle2 = FactoryBot.create(:user_circle, user: user1, circle: political_circle)
      user_circle3 = FactoryBot.create(:user_circle, user: user1, circle: sub_circle2)
      role1 = FactoryBot.create(:role, grade_level: :grade_4, purview_level: :village, has_purview: true)
      role2 = FactoryBot.create(:role, grade_level: :grade_4, purview_level: :village, has_purview: true)
      user1 = FactoryBot.create(:user, village_id: village_circle.id)
      user2 = FactoryBot.create(:user, village_id: village_circle.id)
      user_role1 = FactoryBot.create(:user_role, user: user1, role: role1, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id, purview_circle_id: village_circle2.id)
      user_role2 = FactoryBot.create(:user_role, user: user2, role: role2, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id, purview_circle_id: village_circle2.id)

      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_grade_level', filter_value: "5")
      FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_role_id', filter_value: role1.id)
      expect(UserCircle).to receive(:create!).with(user_id: user1.id, circle_id: sub_circle1.id).once
      described_class.new.perform(user1.id)

    end

    it "user should be removed as role is changed and user is not eligible to join the sub circles with role with purview with only grade level filters and role filters" do

        state = FactoryBot.create(:circle , circle_type: :location, level: :state)
        district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state)
        mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district)
        village_circle = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
        village_circle2 = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
        political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        political2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        sub_circle1 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political_circle)
        sub_circle2 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political2)
        admin_user = FactoryBot.create(:admin_user)
        badge_icon_group = FactoryBot.create(:badge_icon_group, circle_id: political_circle.id)
        data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: admin_user.id)
        badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :GOLD,
                                       badge_icon_group_id: badge_icon_group.id)
        role1 = FactoryBot.create(:role, grade_level: :grade_4, purview_level: :village, has_purview: true)
        role2 = FactoryBot.create(:role, grade_level: :grade_4, purview_level: :village, has_purview: true)
        user1 = FactoryBot.create(:user, village_id: village_circle.id)
        user2 = FactoryBot.create(:user, village_id: village_circle.id)

        user_circle1 = FactoryBot.create(:user_circle, user: user1, circle: village_circle)
        user_circle2 = FactoryBot.create(:user_circle, user: user1, circle: political_circle)
        user_circle3 = FactoryBot.create(:user_circle, user: user1, circle: sub_circle2)
        user_role1 = FactoryBot.create(:user_role, user: user1, role: role1, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id, purview_circle_id: village_circle2.id)
        user_role2 = FactoryBot.create(:user_role, user: user2, role: role2, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id, purview_circle_id: village_circle2.id)

        FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_grade_level', filter_value: "5")
        FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_role_id', filter_value: role1.id)

        described_class.new.perform(user1.id)
        user_circle = UserCircle.where(user_id: user1.id, circle_id: sub_circle1.id).first
        expect(user_circle).not_to be_nil
        user_role1.update(grade_level: :grade_4)
        described_class.new.perform(user1.id)
        user_circle = UserCircle.where(user_id: user1.id, circle_id: sub_circle1.id).first
        expect(user_circle).to be_nil

    end

    it "user should be removed as role is changed and user is not eligible to join the sub circles with role without purview with only grade level filters and role filters" do

        state = FactoryBot.create(:circle , circle_type: :location, level: :state)
        district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state)
        mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district)
        village_circle = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
        village_circle2 = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
        political_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        political2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
        sub_circle1 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political_circle)
        sub_circle2 = FactoryBot.create(:circle, circle_type: :sub, level: :sub, parent_circle: political2)
        admin_user = FactoryBot.create(:admin_user)
        badge_icon_group = FactoryBot.create(:badge_icon_group, circle_id: political_circle.id)
        data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: admin_user.id)
        badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :GOLD,
                                       badge_icon_group_id: badge_icon_group.id)
        role1 = FactoryBot.create(:role, grade_level: :grade_4)
        role2 = FactoryBot.create(:role, grade_level: :grade_4)
        user1 = FactoryBot.create(:user, village_id: village_circle.id)
        user2 = FactoryBot.create(:user, village_id: village_circle.id)

        user_circle1 = FactoryBot.create(:user_circle, user: user1, circle: village_circle)
        user_circle2 = FactoryBot.create(:user_circle, user: user1, circle: political_circle)
        user_circle3 = FactoryBot.create(:user_circle, user: user1, circle: sub_circle2)
        user_role1 = FactoryBot.create(:user_role, user: user1, role: role1, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id)
        user_role2 = FactoryBot.create(:user_role, user: user2, role: role2, grade_level: :grade_5, parent_circle_id: political_circle.id, badge_icon_id: badge_icon.id)

        FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_grade_level', filter_value: "5")
        FactoryBot.create(:sub_circle_filter, sub_circle_id: sub_circle1.id, filter_key: 'filter_role_id', filter_value: role1.id)

        described_class.new.perform(user1.id)
        user_circle = UserCircle.where(user_id: user1.id, circle_id: sub_circle1.id).first
        expect(user_circle).not_to be_nil
        user_role1.update(grade_level: :grade_4)
        described_class.new.perform(user1.id)
        user_circle = UserCircle.where(user_id: user1.id, circle_id: sub_circle1.id).first
        expect(user_circle).to be_nil

    end

  end
end
