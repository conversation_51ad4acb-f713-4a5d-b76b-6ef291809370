require 'rails_helper'

RSpec.describe QueueValidContact do
  describe '#perform' do
    context 'sidekiq_options' do
      it { is_expected.to be_retryable 1 }
      it { is_expected.to be_processed_in :contacts }
    end

    context 'return if user is internal' do
      before :each do
        @user = FactoryBot.create(:user, phone: "20#{Faker::Number.unique.number(digits: 8)}".to_i)
        @user2 = FactoryBot.create(:user)
        @phone_user = FactoryBot.create(:user, phone: Faker::PhoneNumber.cell_phone_in_e164)
      end

      it 'returns if user is internal' do
        allow(JSON).to receive(:parse)

        described_class.new.perform({ "name" => @phone_user.name, "phone" => @phone_user.phone, "phone_user_id" => @phone_user.id }.to_json, @user.id)

        expect(JSON).not_to have_received(:parse)
      end
    end
  end
end
