require 'rails_helper'

RSpec.describe FetchPostTags, type: :worker do
  describe "#perform" do
    context "when post_id is blank" do
      it "returns nil" do
        expect(FetchPostTags.new.perform(nil)).to eq(nil)
      end
    end

    context "when post_id is not blank" do
      it "creates tagging for the post with pending status" do
        hash = [{ :description => "Poster" }, { :description => "Local Event" }, { :description => "Advertising" }, { :description => "Cuisine" }]
        @post = FactoryBot.create(:post, post_photos: [ FactoryBot.build(:post_photo) ])
        @tag = FactoryBot.create(:tag, identifier: "poster")
        @tag2 = FactoryBot.create(:tag, identifier: "advertisement")
        @tag3 = FactoryBot.create(:tag, identifier: "local_event")
        allow(GOOGLE_CLOUD_VISION).to receive_message_chain(:label_detection, :responses, :first, :label_annotations)
                                          .and_return(hash)
        allow(Metadatum).to receive(:create)

        expect(@post.taggings.count).to eq(0)

        FetchPostTags.new.perform(@post.id)

        expect(Metadatum).to have_received(:create).with(entity: @post.post_photos.first, key: "photo_label", value: hash.to_json)
        expect(@post.taggings.count).to eq(3)
        expect(@post.taggings.first.status.to_sym).to eq(:pending)
      end

      it "does not create tagging for the post if tag already exists" do
        hash = [{ :description => "Poster" }]
        @post = FactoryBot.create(:post, post_photos: [ FactoryBot.build(:post_photo) ])
        @tag = FactoryBot.create(:tag, identifier: "poster")
        @post_taggings = FactoryBot.create(:tagging, taggable: @post, tag: @tag, status: :approved)
        allow(GOOGLE_CLOUD_VISION).to receive_message_chain(:label_detection, :responses, :first, :label_annotations)
                                          .and_return(hash)
        allow(Metadatum).to receive(:create)

        expect(@post.taggings.count).to eq(1)

        FetchPostTags.new.perform(@post.id)

        expect(Metadatum).to have_received(:create).with(entity: @post.post_photos.first, key: "photo_label", value: hash.to_json)
        expect(@post.taggings.count).to eq(1)
        expect(@post.taggings.first.status.to_sym).to eq(:approved)
      end

      it "does not create tagging for the post if tag is not present in cloud_vision_tags" do
        @post = FactoryBot.create(:post, post_photos: [ FactoryBot.build(:post_photo) ])
        @tag = FactoryBot.create(:tag, identifier: "poster")
        allow(GOOGLE_CLOUD_VISION).to receive_message_chain(:label_detection, :responses, :first, :label_annotations)
                                          .and_return([])
        allow(Metadatum).to receive(:create)

        expect(@post.taggings.count).to eq(0)

        FetchPostTags.new.perform(@post.id)
        expect(Metadatum).not_to have_received(:create)
        expect(@post.taggings.count).to eq(0)
      end

      it "does not create tagging for the post if tag is not defined in constants" do
        hash = [{ :description => "Cuisine" }]
        @post = FactoryBot.create(:post, post_photos: [ FactoryBot.build(:post_photo) ])
        @tag = FactoryBot.create(:tag, identifier: "poster")
        allow(GOOGLE_CLOUD_VISION).to receive_message_chain(:label_detection, :responses, :first, :label_annotations)
                                          .and_return(hash)
        allow(Metadatum).to receive(:create)

        expect(@post.taggings.count).to eq(0)

        FetchPostTags.new.perform(@post.id)
        expect(Metadatum).to have_received(:create)
        expect(@post.taggings.count).to eq(0)
      end

      it "does not create metadata and taggings for post without photo" do
        hash = [{ :description => "Poster" }]
        @post = FactoryBot.create(:post)
        @tag = FactoryBot.create(:tag, identifier: "poster")
        allow(GOOGLE_CLOUD_VISION).to receive_message_chain(:label_detection, :responses, :first, :label_annotations)
                                          .and_return(hash)
        allow(Metadatum).to receive(:create)

        expect(@post.taggings.count).to eq(0)

        FetchPostTags.new.perform(@post.id)
        expect(Metadatum).not_to have_received(:create)
        expect(@post.taggings.count).to eq(0)
      end

      it "raises Honeybadger error if error occurs while creating tagging" do
        hash = [{ :description => "Poster" }]
        fetched_tags = ["poster"]
        @post = FactoryBot.create(:post, post_photos: [ FactoryBot.build(:post_photo) ])
        @tag = FactoryBot.create(:tag, identifier: "poster")
        allow(GOOGLE_CLOUD_VISION).to receive_message_chain(:label_detection, :responses, :first, :label_annotations)
                                          .and_return(hash)
        allow(Metadatum).to receive(:create)
        allow_any_instance_of(Post).to receive(:save!).and_raise(StandardError)
        allow(Honeybadger).to receive(:notify)
        expect(@post.taggings.count).to eq(0)

        FetchPostTags.new.perform(@post.id)

        expect(Metadatum).to have_received(:create).with(entity: @post.post_photos.first, key: "photo_label", value: hash.to_json)
        expect(@post.taggings.count).to eq(0)
        expect(Honeybadger).to have_received(:notify).with("Error in create taggings on post: #{@post.id} with #{fetched_tags}")
      end

      it 'raises Honeybadger error if error occurs while fetching tags' do
        @post = FactoryBot.create(:post, post_photos: [ FactoryBot.build(:post_photo) ])
        allow(GOOGLE_CLOUD_VISION).to receive_message_chain(:label_detection, :responses, :first, :label_annotations)
                                          .and_raise(StandardError)
        allow(Honeybadger).to receive(:notify)
        expect(@post.taggings.count).to eq(0)

        FetchPostTags.new.perform(@post.id)

        expect(@post.taggings.count).to eq(0)
        expect(Honeybadger).to have_received(:notify).with("Error StandardError on post: #{@post.id}")
      end
    end
  end
end
