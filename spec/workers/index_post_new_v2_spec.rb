require 'rails_helper'

RSpec.describe IndexPostNewV2, type: :worker do
  let (:instance) { described_class.new }
  context "perform" do
    before :each do
      @user = FactoryBot.create(:user)
      @post = FactoryBot.create(:post, user: @user)
      @post.update(created_at: Time.zone.now)
      @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
      @pc1 = FactoryBot.create(:post_circle, post: @post)
      FactoryBot.create(:post_circle, post: @post, circle: @state)
      FactoryBot.create(:post_hashtag, post: @post)
      FactoryBot.create(:post_like, post: @post)
      FactoryBot.create(:post_comment, post: @post)

      FactoryBot.create(:role)
      FactoryBot.create(:user_role, user: @user)

      active = @post.active
      post_created_at = @post.created_at.to_i * 1000
      likes_count = @post.likes_count
      comments_count = @post.comments_count
      opinions_count = @post.opinions_count
      whatsapp_count = @post.whatsapp_count
      unique_users_whatsapp_count = @post.unique_users_whatsapp_count
      hashtag_ids = @post.hashtag_ids
      trends_timestamps = @post.likes.map { |pl| pl.created_at.to_i * 1000 }
      post_user = @post.user
      post_circle = @post.circles.first
      badge_user_affiliated_circle_id = post_user.get_badge_role.get_badge_user_affiliated_party_circle_id.to_i
      liked_user_ids = @post.get_liked_user_ids
      tagged_circles = @post.circles.where.not(id: 0)
      tagged_village_circle_ids = []
      tagged_mandal_circle_ids = []
      tagged_mla_constituency_circle_ids = []
      tagged_mp_constituency_circle_ids = []
      tagged_district_circle_ids = []
      tagged_state_circle_ids = [@state.id]
      tagged_interest_circle_ids = [@pc1.circle_id]
      is_political_party_tagged = true
      post_user_followers = []
      post_user_auto_followers = []
      post_user_members_tab_followers = []
      has_poster_photo = false
      has_external_url = false
      post_user_grade_level = 2
      reports_count = @post.reports.count
      post_photos_count = @post.photos.count
      post_videos_count = @post.videos.count
      post_first_video_duration = @post.videos.first&.duration.to_i
      post_content_length = @post.content&.length.to_i
      news_worthy_score, _ = @post.get_news_worthy_score
      news_worthy_score = news_worthy_score.to_f


      @body = {
        script: {
          source: "" "
                ctx._source.likes_count = params.likes_count;
                ctx._source.comments_count = params.comments_count;
                ctx._source.opinions_count = params.opinions_count;
                ctx._source.whatsapp_count = params.whatsapp_count;
                ctx._source.unique_users_whatsapp_count = params.unique_users_whatsapp_count;
                ctx._source.trends_timestamps = params.trends_timestamps;
                ctx._source.active = params.active;
                ctx._source.badge_user_affiliated_circle_id = params.badge_user_affiliated_circle_id;
                ctx._source.user_village_id = params.user_village_id;
                ctx._source.user_mandal_id = params.user_mandal_id;
                ctx._source.user_mla_constituency_id = params.user_mla_constituency_id;
                ctx._source.user_mp_constituency_id = params.user_mp_constituency_id;
                ctx._source.user_district_id = params.user_district_id;
                ctx._source.user_state_id = params.user_state_id;
                ctx._source.hashtag_ids = params.hashtag_ids;
                ctx._source.is_badge_user = params.is_badge_user;
                ctx._source.liked_user_ids = params.liked_user_ids;
                ctx._source.tagged_circles_ids = params.tagged_circles_ids;
                ctx._source.tagged_interest_circle_ids = params.tagged_interest_circle_ids;
                ctx._source.is_political_party_tagged = params.is_political_party_tagged;
                ctx._source.post_user_followers = params.post_user_followers;
                ctx._source.post_user_auto_followers = params.post_user_auto_followers;
                ctx._source.post_user_members_tab_followers = params.post_user_members_tab_followers;
                ctx._source.tagged_village_circle_ids = params.tagged_village_circle_ids;
                ctx._source.tagged_mandal_circle_ids = params.tagged_mandal_circle_ids;
                ctx._source.tagged_mla_constituency_circle_ids = params.tagged_mla_constituency_circle_ids;
                ctx._source.tagged_mp_constituency_circle_ids = params.tagged_mp_constituency_circle_ids;
                ctx._source.tagged_district_circle_ids = params.tagged_district_circle_ids;
                ctx._source.tagged_state_circle_ids = params.tagged_state_circle_ids;
                ctx._source.has_poster_photo = params.has_poster_photo;
                ctx._source.has_external_url = params.has_external_url;
                ctx._source.post_user_grade_level = params.post_user_grade_level;
                ctx._source.reports_count = params.reports_count;
                ctx._source.post_photos_count = params.post_photos_count;
                ctx._source.post_videos_count = params.post_videos_count;
                ctx._source.post_first_video_duration = params.post_first_video_duration;
                ctx._source.post_content_length = params.post_content_length;
                ctx._source.news_worthy_score = params.news_worthy_score;
            " "",
          params: {
            active: active,
            likes_count: likes_count,
            comments_count: comments_count,
            opinions_count: opinions_count,
            whatsapp_count: whatsapp_count,
            unique_users_whatsapp_count: unique_users_whatsapp_count,
            hashtag_ids: hashtag_ids,
            trends_timestamps: trends_timestamps,
            badge_user_affiliated_circle_id: badge_user_affiliated_circle_id,
            user_village_id: post_user.village_id,
            user_mandal_id: post_user.mandal_id,
            user_mla_constituency_id: post_user.mla_constituency_id,
            user_mp_constituency_id: post_user.mp_constituency_id,
            user_district_id: post_user.district_id,
            user_state_id: post_user.state_id,
            is_badge_user: post_user.get_badge_role.present?,
            liked_user_ids: liked_user_ids,
            tagged_circles_ids: tagged_circles.ids,
            tagged_interest_circle_ids: tagged_interest_circle_ids,
            is_political_party_tagged: is_political_party_tagged,
            post_user_followers: post_user_followers,
            post_user_auto_followers: post_user_auto_followers,
            post_user_members_tab_followers: post_user_members_tab_followers,
            tagged_village_circle_ids: tagged_village_circle_ids,
            tagged_mandal_circle_ids: tagged_mandal_circle_ids,
            tagged_mla_constituency_circle_ids: tagged_mla_constituency_circle_ids,
            tagged_mp_constituency_circle_ids: tagged_mp_constituency_circle_ids,
            tagged_district_circle_ids: tagged_district_circle_ids,
            tagged_state_circle_ids: tagged_state_circle_ids,
            has_poster_photo: has_poster_photo,
            has_external_url: has_external_url,
            post_user_grade_level: post_user_grade_level,
            reports_count: reports_count,
            post_photos_count: post_photos_count,
            post_videos_count: post_videos_count,
            post_first_video_duration: post_first_video_duration,
            post_content_length: post_content_length,
            news_worthy_score: news_worthy_score
          }
        },
        upsert: {
          id: @post.id,
          active: active,
          likes_count: likes_count,
          comments_count: comments_count,
          opinions_count: opinions_count,
          whatsapp_count: whatsapp_count,
          unique_users_whatsapp_count: unique_users_whatsapp_count,
          hashtag_ids: hashtag_ids,
          trends_timestamps: trends_timestamps,
          parent_post_id: @post.parent_post.nil? ? 0 : @post.parent_post.id,
          circle_id: post_circle.id,
          circle_level: post_circle.level,
          district_id: post_circle.is_village_level? ? post_circle.parent_circle.parent_circle_id : 0,
          tagged_circles_ids: tagged_circles.ids,
          tagged_interest_circle_ids: tagged_interest_circle_ids,
          is_political_party_tagged: is_political_party_tagged,
          created_at: post_created_at,
          user_id: post_user.id,
          user_village_id: post_user.village_id,
          user_mandal_id: post_user.mandal_id,
          user_mla_constituency_id: post_user.mla_constituency_id,
          user_mp_constituency_id: post_user.mp_constituency_id,
          user_district_id: post_user.district_id,
          user_state_id: post_user.state_id,
          is_badge_user: post_user.get_badge_role.present?,
          badge_user_affiliated_circle_id: badge_user_affiliated_circle_id,
          liked_user_ids: liked_user_ids,
          post_user_followers: post_user_followers,
          post_user_auto_followers: post_user_auto_followers,
          post_user_members_tab_followers: post_user_members_tab_followers,
          tagged_village_circle_ids: tagged_village_circle_ids,
          tagged_mandal_circle_ids: tagged_mandal_circle_ids,
          tagged_mla_constituency_circle_ids: tagged_mla_constituency_circle_ids,
          tagged_mp_constituency_circle_ids: tagged_mp_constituency_circle_ids,
          tagged_district_circle_ids: tagged_district_circle_ids,
          tagged_state_circle_ids: tagged_state_circle_ids,
          has_poster_photo: has_poster_photo,
          has_external_url: has_external_url,
          post_user_grade_level: post_user_grade_level,
          reports_count: reports_count,
          post_photos_count: post_photos_count,
          post_videos_count: post_videos_count,
          post_first_video_duration: post_first_video_duration,
          post_content_length: post_content_length,
          news_worthy_score: news_worthy_score
        }
      }
    end

    it { is_expected.to be_processed_in :posts_indexing }

    it { is_expected.to be_retryable 1 }

    it 'returns if id is nil' do
      returned_value = described_class.new.perform(nil)
      expect(returned_value).to eq(nil)
    end

    it 'returns if post is not found' do
      returned_value = described_class.new.perform(123)
      expect(returned_value).to eq(nil)
    end

    it 'returns if post is older than 3 days' do
      @post.update(created_at: 4.days.ago)
      returned_value = described_class.new.perform(@post.id)
      expect(returned_value).to eq(nil)
    end

    it 'do post index' do
      tag = FactoryBot.create(:tag, id: Constants.post_poster_tag_id, tag_type: :post)
      FactoryBot.create(:tagging, taggable: @post, tag: tag, status: :approved)
      body = @body.deep_dup
      body[:script][:params][:has_poster_photo] = true
      body[:upsert][:has_poster_photo] = true
      expect(instance).to receive(:perform_index).with(@post.id, @post, body)
      instance.perform(@post.id)
    end

    it 'do post index with poster metadatum' do
      FactoryBot.create(:metadatum, key: Constants.get_is_poster_photo_key_for_post, value: true, entity: @post)
      body = @body.deep_dup
      body[:script][:params][:has_poster_photo] = true
      body[:upsert][:has_poster_photo] = true
      expect(instance).to receive(:perform_index).with(@post.id, @post, body)
      instance.perform(@post.id)
    end

    it 'do post index with post photo' do
      @post.photos << FactoryBot.create(:photo)
      allow(FastImage).to receive(:size).and_return([67, 100])
      body = @body.deep_dup
      body[:script][:params][:post_photos_count] = 1
      body[:script][:params][:has_poster_photo] = true
      body[:upsert][:has_poster_photo] = true
      body[:upsert][:post_photos_count] = 1
      expect(instance).to receive(:perform_index).with(@post.id, @post, body)
      instance.perform(@post.id)
    end
  end
end
