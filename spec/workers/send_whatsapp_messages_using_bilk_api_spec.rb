require 'rails_helper'
require 'webmock/rspec'

RSpec.describe SendWhatsappMessagesUsingBulkApi, type: :worker do
  let(:template_name) { "fake_template" }
  let(:broadcast_name) { "fake_broadcast" }
  let(:wati_endpoint) { "https://fake.wati.endpoint/send_bulk" }
  let(:redis_key) { Constants.wati_campaigns_redis_key }
  let(:mock_redis) { instance_double(Redis) }
  let(:receivers_data) { {
    "919999999999" => {
      name: "Shashi",
      poster_image_url: "fake_poster_image_url",
      event_name: "fake_event",
      amount: "34"
    }
  }}
  let(:request_body) { {
    template_name: template_name,
    broadcast_name: broadcast_name,
    receivers: [
      {
        whatsappNumber: "919999999999",
        customParams: [
          { name: "name", value: "Shashi" },
          { name: "poster_image_url", value: "fake_poster_image_url" },
          { name: "event_name", value: "fake_event" },
          { name: "amount", value: "34" }
        ]
      }
    ]
  } }

  before do
    allow(Constants).to receive(:get_send_messages_using_bulk_api).and_return(wati_endpoint)
    allow(Rails.application).to receive(:credentials).and_return({ wati_api_key: "fake_api_key" })
    allow(mock_redis).to receive(:hget).with(redis_key, broadcast_name).and_return(receivers_data.to_json)
  end

  describe "#perform" do
    context "when wati api response is 200" do
      before do
        stub_request(:post, wati_endpoint).with(
          body: request_body.to_json,
          headers: {
            "Authorization" => "fake_api_key",
            "Content-Type" => "application/json-patch+json"
          }).to_return(status: 200, body: { result: true }.to_json)
      end
      it "should send wati and delete redis key" do
        expect(Honeybadger).not_to receive(:notify)
        allow($redis).to receive(:hget).with(redis_key, broadcast_name).and_return(receivers_data.to_json)
        expect($redis).to receive(:hdel).with(redis_key, broadcast_name)
        described_class.new.perform(template_name, broadcast_name)
      end
    end

    context "when wati api response is not 200" do
      before do
        allow($redis).to receive(:hget).with(redis_key, broadcast_name).and_return(receivers_data.to_json)
        stub_request(:post, wati_endpoint).to_return(status: 500, body: { result: false }.to_json)
      end
      it "should notify honey badger and not delete redis data" do
        expect(Honeybadger).to receive(:notify).with("WATI Bulk messages trigger error: 500", context: {
          response_code: "500",
          response_body: { "result" => false }
        })
        expect(mock_redis).not_to receive(:hdel)
        described_class.new.perform(template_name, broadcast_name)
      end
    end

    context "when wati api response is 200 and result is false" do
      before do
        allow($redis).to receive(:hget).with(redis_key, broadcast_name).and_return(receivers_data.to_json)
        stub_request(:post, wati_endpoint).to_return(status: 200, body: { result: false }.to_json)
      end
      it "should notify honey badger and not delete redis data" do
        expect(Honeybadger).to receive(:notify).with("WATI Bulk messages trigger error: 200", context: {
          response_code: "200",
          response_body: { "result" => false }
        })
        expect(mock_redis).not_to receive(:hdel)
        described_class.new.perform(template_name, broadcast_name)
      end
    end
  end
end
