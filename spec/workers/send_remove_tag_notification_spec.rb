require 'rails_helper'

RSpec.describe SendRemoveTagNotification, type: :worker do
  describe "send remove tag notification" do
    context "send notification for remove tag" do
      before :each do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        @post = FactoryBot.create(:post, user: @user)
        @post_circle = FactoryBot.create(:post_circle, post: @post, circle: @circle)
      end

      it { is_expected.to be_processed_in :notifications }

      it { is_expected.to be_retryable 0 }

      it "check for the same argument in job been called" do
        described_class.perform_async(@post.id, @circle.id)
        expect(described_class.jobs[0]['args']).to eq([@post.id, @circle.id])
      end

      it "triggers garuda api to send user notification" do
        allow(GarudaNotification).to receive(:send_user_notification)
        described_class.new.perform(@post.id, @circle.id)
        expect(GarudaNotification).to have_received(:send_user_notification)
      end

      it "do not trigger garuda api if either one of params is nil" do
        allow(GarudaNotification).to receive(:send_user_notification)
        described_class.new.perform(@post.id, nil)
        expect(GarudaNotification).not_to have_received(:send_user_notification)

      end

      it "do not trigger garuda notification if post id is nil" do
        allow(GarudaNotification).to receive(:send_user_notification)
        described_class.new.perform(nil, @circle.id)
        expect(GarudaNotification).not_to have_received(:send_user_notification)

      end

      it "do not trigger garuda notification if either of the params are nil" do
        allow(GarudaNotification).to receive(:send_user_notification)
        described_class.new.perform(nil, nil)
        expect(GarudaNotification).not_to have_received(:send_user_notification)

      end
    end
  end
end
