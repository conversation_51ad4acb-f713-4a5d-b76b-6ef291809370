# frozen_string_literal: true

require 'rails_helper'

RSpec.describe QueuePosterCreativeViews, type: :worker do
  context "when worker runs" do
    it { is_expected.to be_processed_in :default }
    it { is_expected.to be_retryable 3 }
  end

  context "creative views enqueued" do
    it "enqueues creative views" do
      user_id = rand(10000)
      poster_creative_id = rand(10000)
      timestamp = Time.zone.now.to_i
      user_date_poster_creative_views_queue = Constants.user_date_poster_creative_views_queue_redis_key(user_id)

      $redis.hdel(Constants.poster_creative_views_redis_key, poster_creative_id.to_s)
      $redis.srem(Constants.poster_creative_views_queue_redis_key, { creative_id: poster_creative_id, user_id: user_id }.to_json)
      $redis.srem(user_date_poster_creative_views_queue, poster_creative_id.to_s)

      described_class.new.perform(user_id, { poster_creative_id => timestamp })

      Rails.logger.debug "poster_creative_views:#{poster_creative_id}"

      expect($redis.exists?(Constants.poster_creative_views_redis_key)).to eq(true)
      expect($redis.hexists(Constants.poster_creative_views_redis_key, poster_creative_id.to_s)).to eq(true)
      expect($redis.exists?(user_date_poster_creative_views_queue)).to eq(true)
      expect($redis.sismember(user_date_poster_creative_views_queue, poster_creative_id.to_s)).to eq(true)
      expect($redis.exists?(Constants.poster_creative_views_queue_redis_key)).to eq(true)
      expect($redis.sismember(Constants.poster_creative_views_queue_redis_key, { poster_creative_id: poster_creative_id, user_id: user_id, viewed_at: Time.zone.at(timestamp).to_datetime.strftime("%Y-%m-%d %H:%M:%S") }.to_json)).to eq(true)

      $redis.hdel(Constants.poster_creative_views_redis_key, poster_creative_id.to_s)
      $redis.srem(Constants.poster_creative_views_queue_redis_key, { poster_creative_id: poster_creative_id, user_id: user_id, viewed_at: Time.zone.at(timestamp).to_datetime.strftime("%Y-%m-%d %H:%M:%S") }.to_json)
      $redis.srem(user_date_poster_creative_views_queue, poster_creative_id.to_s)
    end
  end
end
