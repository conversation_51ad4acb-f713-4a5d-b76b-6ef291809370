require 'rails_helper'

RSpec.describe SendDmPosterCreativeMessage, type: :worker do
  describe "perform" do
    context "performs the job" do
      it { is_expected.to be_processed_in :critical }
      it { is_expected.to be_retryable 0 }
    end
  end

  describe "dm poster creative message" do
    context "send dm poster creative message to circle" do
      before :each do
        allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 201, body: { success: true }.to_json))
        @circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private,
                                    conversation_type: :channel)
        @user = FactoryBot.create(:user)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, primary: true, event: nil,
                                             photo_v2: @admin_medium_2, creative_kind: :schemes)
        @poster_creative_circle = FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)
      end

      it "calls post request to dm method" do
        text = "Poster creative message"
        send_message_hash = {
          circle_id: @circle.id,
          sender_id: @poster_creative.creator_id,
          creative_id: @poster_creative.id,
          text: text,
          sent_at: @poster_creative.created_at.to_s,
          category_kind: @poster_creative.creative_kind
        }

        allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 201, body: { success: true }.to_json))
        result = SendDmPosterCreativeMessage.new.perform(send_message_hash.to_json)
        expect(result).to eq(true)
      end

      it "calls Honeybadger notify if response code is not 201" do
        text = "Poster creative message"
        send_message_hash = {
          circle_id: @circle.id,
          sender_id: @poster_creative.creator_id,
          creative_id: @poster_creative.id,
          text: text,
          sent_at: @poster_creative.created_at.to_s,
          category_kind: @poster_creative.creative_kind
        }

        allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 400, body: { success: false }.to_json))
        allow(Honeybadger).to receive(:notify)

        SendDmPosterCreativeMessage.new.perform(send_message_hash.to_json)

        expect(Honeybadger).to have_received(:notify)
      end
    end

    context "doesn't send dm poster creative message to circle" do
      it "doesn't call post request to dm method if circle_id is nil" do
        text = "Poster creative message"
        send_message_hash = {
          circle_id: nil,
          sender_id: 1,
          creative_id: 1,
          text: text,
          sent_at: Time.zone.now,
          category_kind: "schemes"
        }

        allow(DmUtil).to receive(:post_request_to_dm)

        SendDmPosterCreativeMessage.new.perform(send_message_hash.to_json)

        expect(DmUtil).not_to have_received(:post_request_to_dm)
      end

      it "doesn't call post request to dm method if sender_id is nil" do
        text = "Poster creative message"
        send_message_hash = {
          circle_id: 1,
          sender_id: nil,
          creative_id: 1,
          text: text,
          sent_at: Time.zone.now,
          category_kind: "schemes"
        }

        allow(DmUtil).to receive(:post_request_to_dm)

        SendDmPosterCreativeMessage.new.perform(send_message_hash.to_json)

        expect(DmUtil).not_to have_received(:post_request_to_dm)
      end

      it "doesn't call post request to dm method if creative_id is nil" do
        text = "Poster creative message"
        send_message_hash = {
          circle_id: 1,
          sender_id: 1,
          creative_id: nil,
          text: text,
          sent_at: Time.zone.now,
          category_kind: "schemes"
        }

        allow(DmUtil).to receive(:post_request_to_dm)

        SendDmPosterCreativeMessage.new.perform(send_message_hash.to_json)

        expect(DmUtil).not_to have_received(:post_request_to_dm)
      end

      it "doesn't call post request to dm method if sent_at is nil" do
        text = "Poster creative message"
        send_message_hash = {
          circle_id: 1,
          sender_id: 1,
          creative_id: 1,
          text: text,
          sent_at: nil,
          category_kind: "schemes"
        }

        allow(DmUtil).to receive(:post_request_to_dm)

        SendDmPosterCreativeMessage.new.perform(send_message_hash.to_json)

        expect(DmUtil).not_to have_received(:post_request_to_dm)
      end
    end
  end
end
