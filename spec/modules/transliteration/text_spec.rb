require 'rails_helper'

RSpec.describe Transliteration::Text do
  describe 'Should be able to transliterate text' do
    it 'should be able to transliterate text from telugu to english' do
      uri = "some_url"
      response = '[{"text":"telugu"}]'
      response_double = double(response)
      allow(URI).to receive(:parse).and_return(uri)
      allow(Net::HTTP).to receive(:post).and_return(response_double)
      allow(response_double).to receive(:code).and_return(200)
      allow(response_double).to receive(:read_body).and_return(response)

      text = Transliteration::Text.new('తెలుగు')
      expect(text.transliterate('en')).to eq('telugu')
    end
    it 'should be able to transliterate text from english to telugu' do
      uri = "some_url"
      response = '[{"text":"ఇంగ్లీష్"}]'
      response_double = double(response)
      allow(URI).to receive(:parse).and_return(uri)
      allow(Net::HTTP).to receive(:post).and_return(response_double)
      allow(response_double).to receive(:code).and_return(200)
      allow(response_double).to receive(:read_body).and_return(response)

      text = Transliteration::Text.new('english')
      expect(text.transliterate('te')).to eq('ఇంగ్లీష్')
    end
  end
end
