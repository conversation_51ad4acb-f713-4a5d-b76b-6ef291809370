require 'rails_helper'

RSpec.describe Transliteration::Word do
  describe 'Should be able to detect languages' do
    it 'should be able to detect telugu text' do
      text = Transliteration::Word.new('తెలుగు')
      expect(text.language).to eq(Transliteration::LANGUAGE_CODE_TELUGU)
    end

    it 'should be able to detect english text' do
      text = Transliteration::Word.new('english')
      expect(text.language).to eq(Transliteration::LANGUAGE_CODE_ENGLISH)
    end

    it 'should be able to detect hindi text' do
      text = Transliteration::Word.new('हिन्दी')
      expect(text.language).to eq(Transliteration::LANGUAGE_CODE_HINDI)
    end

    it 'should be able to detect tamil text' do
      text = Transliteration::Word.new('தமிழ்')
      expect(text.language).to eq(Transliteration::LANGUAGE_CODE_TAMIL)
    end

    it 'should be able to detect kannada text' do
      text = Transliteration::Word.new('ಕನ್ನಡ')
      expect(text.language).to eq(Transliteration::LANGUAGE_CODE_KANNADA)
    end

    it 'should be able to detect malayalam text' do
      text = Transliteration::Word.new('മലയാളം')
      expect(text.language).to eq(Transliteration::LANGUAGE_CODE_MALAYALAM)
    end

    it 'should be able to detect punjabi text' do
      text = Transliteration::Word.new('ਪੰਜਾਬੀ')
      expect(text.language).to eq(Transliteration::LANGUAGE_CODE_PUNJABI)
    end

    it 'should be able to detect oriya text' do
      text = Transliteration::Word.new('ଓଡ଼ିଆ')
      expect(text.language).to eq(Transliteration::LANGUAGE_CODE_ORIYA)
    end

    it 'should be able to detect bengali text' do
      text = Transliteration::Word.new('বাংলা')
      expect(text.language).to eq(Transliteration::LANGUAGE_CODE_BENGALI)
    end
  end

  describe 'Should be able to transliterate text' do
    it 'should be able to transliterate text from telugu to english' do
      uri = "some_url"
      response = '[{"text":"telugu"}]'
      response_double = double(response)
      allow(URI).to receive(:parse).and_return(uri)
      allow(Net::HTTP).to receive(:post).and_return(response_double)
      allow(response_double).to receive(:code).and_return(200)
      allow(response_double).to receive(:read_body).and_return(response)

      text = Transliteration::Word.new('తెలుగు')
      expect(text.transliterate('en')).to eq('telugu')
    end
    it 'should be able to transliterate text from english to telugu' do
      uri = "some_url"
      response = '[{"text":"ఇంగ్లిష్"}]'
      response_double = double(response)
      allow(URI).to receive(:parse).and_return(uri)
      allow(Net::HTTP).to receive(:post).and_return(response_double)
      allow(response_double).to receive(:code).and_return(200)
      allow(response_double).to receive(:read_body).and_return(response)

      text = Transliteration::Word.new('english')
      expect(text.transliterate('te')).to eq('ఇంగ్లిష్')
    end
    it 'should be able not able to detect hebrew' do
      uri = "some_url"
      response = '[{"text":"מִבְחָן"}]'
      response_double = double(response)
      allow(URI).to receive(:parse).and_return(uri)
      allow(Net::HTTP).to receive(:post).and_return(response_double)
      allow(response_double).to receive(:code).and_return(200)
      allow(response_double).to receive(:read_body).and_return(response)

      text = Transliteration::Word.new('מִבְחָן')
      expect(text.language).to eq(nil)
      expect(text.transliterate('en')).to eq('מִבְחָן')
    end
    it 'should be able return same text if source language and target language is same' do
      uri = "some_url"
      response = '[{"text":"english"}]'
      response_double = double(response)
      allow(URI).to receive(:parse).and_return(uri)
      allow(Net::HTTP).to receive(:post).and_return(response_double)
      allow(response_double).to receive(:code).and_return(200)
      allow(response_double).to receive(:read_body).and_return(response)

      text = Transliteration::Word.new('english')
      expect(text.transliterate('en')).to eq('english')
    end
  end

  describe 'Should be able to handle failure cases' do
    it 'should be able to return same text if the azure api fails' do
      text = Transliteration::Word.new('english')
      uri = "some_url"
      response = '[]'
      response_double = double(response)
      allow(URI).to receive(:parse).and_return(uri)
      allow(Net::HTTP).to receive(:post).and_return(response_double)
      allow(response_double).to receive(:code).and_return(200)
      allow(response_double).to receive(:read_body).and_return(response)
      expect(text.transliterate('te')).to eq('english')
    end
    it 'should be able to return same text if the azure api fails with different response' do
      text = Transliteration::Word.new('english')
      uri = "some_url"
      response = '["SUCCESS", []]'
      response_double = double(response)
      allow(URI).to receive(:parse).and_return(uri)
      allow(Net::HTTP).to receive(:post).and_return(response_double)
      allow(response_double).to receive(:code).and_return(200)
      allow(response_double).to receive(:read_body).and_return(response)
      expect(text.transliterate('te')).to eq('english')
    end
  end
end
