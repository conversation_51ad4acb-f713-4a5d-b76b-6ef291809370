require 'rails_helper'

RSpec.describe UserToken, type: :model do
  describe "user token" do
    before :each do
      @user_token = FactoryBot.create(:user_token)
    end

    it "check to get an access token" do
      expect(@user_token.access_token.present?).to eq(true)
    end

    it "check to get an access token" do
      user_id = $redis.hget('user_tokens', @user_token.access_token).to_i
      expect(@user_token.user_id).to eq(user_id)
    end

    it "check to get an access token" do
      @user_token.active = false
      @user_token.save!
      user_id = $redis.hget('user_tokens', @user_token.access_token)
      expect(user_id).to be_nil
    end
  end
end