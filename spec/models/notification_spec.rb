require 'rails_helper'

RSpec.describe Notification, type: :model do
  describe "internal notifications count set" do
    context "check for given user_ids" do
      it "notifications sent count and ignored user_ids" do
        @user = FactoryBot.create(:user)
        @user1 = FactoryBot.create(:user)
        @user2 = FactoryBot.create(:user)
        @user3 = FactoryBot.create(:user)

        user_ids = [@user.id,  @user1.id, @user2.id, @user3.id]

        # set user 2 notifications count to 1
        $redis.set("notification_count_v1_user_#{@user1.id}", "1")

        # set user 2 notifications count to 2
        $redis.set("notification_count_v1_user_#{@user2.id}", "2")

        # set user 3 notifications count to 3
        $redis.set("notification_count_v1_user_#{@user3.id}", "3")

        ignored_user_ids = Notification.internal_notifications_count_set(user_ids)

        notifications_count_for_user = $redis.get("notification_count_v1_user_#{@user.id}").to_i
        notifications_count_for_user1 = $redis.get("notification_count_v1_user_#{@user1.id}").to_i
        notifications_count_for_user2 = $redis.get("notification_count_v1_user_#{@user2.id}").to_i
        notifications_count_for_user3 = $redis.get("notification_count_v1_user_#{@user3.id}").to_i

        expect(notifications_count_for_user).to eq(1)
        expect(notifications_count_for_user1).to eq(2)
        expect(notifications_count_for_user2).to eq(3)
        expect(notifications_count_for_user3).to eq(3)
        expect(@user3.id.in? ignored_user_ids).to be_truthy
      end
    end
  end
end
