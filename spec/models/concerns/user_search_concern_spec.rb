require 'rails_helper'

RSpec.describe UserSearchConcern do

  before :all do
    User.reindex
  end

  describe 'Should be able to search for users' do
    it "returns users with matching the search criteria" do
      query = 'somerandomquery'
      @user = FactoryBot.create(:user, village_id: 0, mandal_id: 0, district_id: 0, state_id: 0, mla_constituency_id: 0, mp_constituency_id: 0)
      @user2 = FactoryBot.create(:user, village_id: 0, mandal_id: 0, district_id: 0, state_id: 0, mla_constituency_id: 0, mp_constituency_id: 0)

      some_party_id = 132
      some_leader_id = 1173

      expect(@user).to receive(:get_user_joined_party_circle_ids).and_return([some_party_id])
      expect(@user).to receive(:get_user_joined_leader_ids).and_return([some_leader_id])

      expect(User).to receive(:search).and_wrap_original do |original_method, *args, &block|
        query_body = args[0]

        expect(query_body[:body][:query][:bool][:must][0][:bool][:should][0][:match_bool_prefix][:name][:query]).to eq(query)
        expect(query_body[:body][:query][:bool][:must][0][:bool][:should][1][:match_bool_prefix][:name_en][:query]).to eq(query)
        expect(query_body[:body][:query][:bool][:must][0][:bool][:should][2][:match_bool_prefix][:name_te][:query]).to eq(query)

        expect(query_body[:body][:query][:bool][:must][0][:bool][:should][3][:match_bool_prefix][:badge_description][:query]).to eq(query)
        expect(query_body[:body][:query][:bool][:must][0][:bool][:should][4][:match_bool_prefix][:badge_description_en][:query]).to eq(query)
        expect(query_body[:body][:query][:bool][:must][0][:bool][:should][5][:match_bool_prefix][:badge_description_te][:query]).to eq(query)

        expect(query_body[:body][:query][:bool][:must][0][:bool][:should][6][:match][:phone]).to eq(query)
        expect(query_body[:body][:query][:bool][:must][0][:bool][:should][7][:match_phrase][:name_en][:query]).to eq(query)
        expect(query_body[:body][:query][:bool][:must][0][:bool][:should][8][:match_phrase][:name_te][:query]).to eq(query)
        expect(query_body[:body][:query][:bool][:must][0][:bool][:should][9][:match_phrase][:name][:query]).to eq(query)

        expect(query_body[:body][:query][:bool][:must][0][:bool][:should][10][:match_phrase][:badge_description_en][:query]).to eq(query)
        expect(query_body[:body][:query][:bool][:must][0][:bool][:should][11][:match_phrase][:badge_description_te][:query]).to eq(query)
        expect(query_body[:body][:query][:bool][:must][0][:bool][:should][12][:match_phrase][:badge_description][:query]).to eq(query)

        expect(query_body[:body][:query][:bool][:must][0][:bool][:should][13][:fuzzy][:name][:value]).to eq(query)
        expect(query_body[:body][:query][:bool][:must][0][:bool][:should][14][:fuzzy][:name_en][:value]).to eq(query)
        expect(query_body[:body][:query][:bool][:must][0][:bool][:should][15][:fuzzy][:name_te][:value]).to eq(query)

        expect(query_body[:body][:query][:bool][:must][0][:bool][:should][16][:fuzzy][:badge_description][:value]).to eq(query)
        expect(query_body[:body][:query][:bool][:must][0][:bool][:should][17][:fuzzy][:badge_description_en][:value]).to eq(query)
        expect(query_body[:body][:query][:bool][:must][0][:bool][:should][18][:fuzzy][:badge_description_te][:value]).to eq(query)

        expect(query_body[:body][:query][:bool][:should][:bool][:should][0][:match][:village_id][:query]).to eq(@user.village_id)
        expect(query_body[:body][:query][:bool][:should][:bool][:should][1][:match][:mandal_id][:query]).to eq(@user.mandal_id)
        expect(query_body[:body][:query][:bool][:should][:bool][:should][2][:match][:district_id][:query]).to eq(@user.district_id)
        expect(query_body[:body][:query][:bool][:should][:bool][:should][3][:match][:mla_constituency_id][:query]).to eq(@user.mla_constituency_id)
        expect(query_body[:body][:query][:bool][:should][:bool][:should][4][:match][:mp_constituency_id][:query]).to eq(@user.mp_constituency_id)
        expect(query_body[:body][:query][:bool][:should][:bool][:should][5][:match][:state_id][:query]).to eq(@user.state_id)
        expect(query_body[:body][:query][:bool][:should][:bool][:should][6][:rank_feature][:field]).to eq('followers_count')
        expect(query_body[:body][:query][:bool][:should][:bool][:should][7][:match][:party_circle_id][:query]).to eq(some_party_id)
        expect(query_body[:body][:query][:bool][:should][:bool][:should][8][:match][:leader_circle_id][:query]).to eq(some_leader_id)

        user = FactoryBot.create(:user)
        expect(user).to receive(:id).and_return(@user2.id)
        [user]
      end

      response = User.search_users(user: @user, query: query, offset: 0, count: 10)
      expect(response).to be_an_instance_of(Array)
      expect(response.length).to eq(1)

      user = response[0]
      expect(user).to be_an_instance_of(Hash)
      expect(user).to have_key(:id)
      expect(user).to have_key(:name)
      expect(user).to have_key(:photo)
      expect(user).to have_key(:village)
      expect(user).to have_key(:followers_count)
      expect(user).to have_key(:loggedInUser)
      expect(user).to have_key(:follows)
      expect(user).to have_key(:badge)
      expect(user).to have_key(:avatar_color)
    end
  end

  describe "Search data" do
    it "returns the correct search data" do
      @user = FactoryBot.create(:user)
      some_party_id = 132
      some_leader_id = 1173
      expect(@user).to receive(:get_user_joined_party_circle_ids).and_return([some_party_id])
      expect(@user).to receive(:get_user_joined_leader_ids).and_return([some_leader_id])

      search_data = @user.search_data
      expect(search_data[:party_circle_id]).to eq([some_party_id])
      expect(search_data[:leader_circle_id]).to eq([some_leader_id])
    end
  end
end
