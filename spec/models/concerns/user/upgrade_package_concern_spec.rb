require 'rails_helper'
require 'spec_helper'

RSpec.describe User::UpgradePackageConcern do
  describe '#common_upgrade_package_conditions_met?' do
    let(:user) { create(:user) }
    let(:subscription) { create(:subscription, user: user) }

    before do
      allow(SubscriptionUtils).to receive(:has_user_subscribed?).with(user.id, allow_grace_period: false)
                                                                .and_return(true)
      allow(user).to receive(:active_subscription).and_return(subscription)
      allow(user).to receive(:has_user_subscribed_to_one_month_plan?).and_return(true)
      allow(user).to receive(:last_2_charges_successful_in_first_attempt?).with(subscription.id).and_return(true)
    end

    context 'when all conditions are met' do
      it 'returns true' do
        expect(user.common_upgrade_package_conditions_met?).to be true
      end
    end

    context 'when user is not subscribed to poster' do
      before do
        allow(SubscriptionUtils).to receive(:has_user_subscribed?).with(user.id, allow_grace_period: false)
                                                                  .and_return(false)
      end

      it 'returns false' do
        expect(user.common_upgrade_package_conditions_met?).to be false
      end
    end

    context 'when active subscription is blank' do
      before do
        allow(user).to receive(:active_subscription).and_return(nil)
      end

      it 'returns false' do
        expect(user.common_upgrade_package_conditions_met?).to be false
      end
    end

    context 'when user has not subscribed to one month plan' do
      before do
        allow(user).to receive(:has_user_subscribed_to_one_month_plan?).and_return(false)
      end

      it 'returns false' do
        expect(user.common_upgrade_package_conditions_met?).to be false
      end
    end

    context 'when last 2 charges were not successful in first attempt' do
      before do
        allow(user).to receive(:last_2_charges_successful_in_first_attempt?).with(subscription.id).and_return(false)
      end

      it 'returns false' do
        expect(user.common_upgrade_package_conditions_met?).to be false
      end
    end
  end

  describe '#upgrade_package_sheet_metadata_to_be_shown?' do
    let(:user) { create(:user) }

    context 'when upgrade package sheet is to be shown' do

      it 'returns true' do
        create(:user_metadatum, user: user, key: "upgrade_package_sheet", value: :to_be_shown)
        expect(user.upgrade_package_sheet_metadata_to_be_shown?).to be true
      end
    end

    context 'when upgrade package sheet is not to be shown' do
      it 'returns false' do
        expect(user.upgrade_package_sheet_metadata_to_be_shown?).to be false
      end
    end
  end

  describe '#mark_as_shown_upgrade_package_sheet' do
    let(:user) { create(:user) }

    it 'marks the upgrade package sheet as shown' do
      user.mark_as_shown_upgrade_package_sheet
      expect(UserMetadatum.exists?(user: user, key: "upgrade_package_sheet", value: :shown)).to be true
    end
  end

  describe '#mark_as_to_be_shown_upgrade_package_sheet' do
    let(:user) { create(:user) }

    it 'marks the upgrade package sheet as to be shown' do
      user.mark_as_to_be_shown_upgrade_package_sheet
      expect(UserMetadatum.exists?(user: user, key: "upgrade_package_sheet", value: :to_be_shown)).to be true
    end
  end

  describe '#last_2_charges_successful_in_first_attempt?' do
    let(:user) { create(:user) }
    let(:subscription) { create(:subscription, user: user) }

    context 'when last 2 charges are successful in the first attempt' do
      before do
        create(:subscription_charge, user: user, subscription: subscription, status: 'success', attempt_number: 1,
               charge_date: Time.zone.now - 1.month, amount: 100, charge_amount: 100)
        create(:subscription_charge, user: user, subscription: subscription, status: 'success', attempt_number: 1,
               amount: 100, charge_amount: 100)
      end

      it 'returns true' do
        expect(user.last_2_charges_successful_in_first_attempt?(subscription.id)).to be true
      end
    end

    context 'when there are less than 2 successful charges' do
      before do
        create(:subscription_charge, user: user, subscription: subscription, status: 'success', attempt_number: 1, charge_amount: 100)
      end

      it 'returns false' do
        expect(user.last_2_charges_successful_in_first_attempt?(subscription.id)).to be false
      end
    end

    context 'when there are no successful charges' do
      it 'returns false' do
        expect(user.last_2_charges_successful_in_first_attempt?(subscription.id)).to be false
      end
    end

    context 'when there are two successful charges but not in same subscription' do
      let(:subscription2) { create(:subscription, user: user) }
      before do
        create(:subscription_charge, user: user, subscription: subscription, status: 'success', attempt_number: 1,
               charge_date: Time.zone.now - 1.month, charge_amount: 100)
        create(:subscription_charge, user: user, subscription: subscription2, status: 'success', attempt_number: 1, charge_amount: 100)
      end

      it 'returns false' do
        expect(user.last_2_charges_successful_in_first_attempt?(subscription.id)).to be false
      end
    end
  end

  describe '#should_show_upgrade_package_nudge?' do
    let(:user) { create(:user) }
    let(:redis_key) { Constants.upgrade_package_nudge_count_key(user.id) }

    context 'when upgrade package nudge count is less than 10' do
      before do
        allow($redis).to receive(:get).with(redis_key).and_return('5')
      end

      it 'returns true' do
        expect(user.should_show_upgrade_package_nudge?).to be true
      end
    end

    context 'when upgrade package nudge count is exactly 10' do
      before do
        allow($redis).to receive(:get).with(redis_key).and_return('10')
      end

      it 'returns false' do
        expect(user.should_show_upgrade_package_nudge?).to be false
      end
    end

    context 'when upgrade package nudge count is more than 10' do
      before do
        allow($redis).to receive(:get).with(redis_key).and_return('15')
      end

      it 'returns false' do
        expect(user.should_show_upgrade_package_nudge?).to be false
      end
    end

    context 'when upgrade package nudge count is nil' do
      before do
        allow($redis).to receive(:get).with(redis_key).and_return(nil)
      end

      it 'returns true' do
        expect(user.should_show_upgrade_package_nudge?).to be true
      end
    end
  end

  describe '#increment_upgrade_package_nudge_seen_count' do
    let(:user) { create(:user) }
    let(:redis_key) { Constants.upgrade_package_nudge_count_key(user.id) }

    context 'when the nudge count is zero' do
      before do
        allow($redis).to receive(:get).with(redis_key).and_return(nil)
        allow($redis).to receive(:set).with(redis_key, 1, ex: 31.days.to_i)
      end

      it 'sets the nudge count to 1' do
        expect($redis).to receive(:set).with(redis_key, 1, ex: 31.days.to_i)
        user.increment_upgrade_package_nudge_seen_count
      end
    end

    context 'when the nudge count is greater than zero' do
      before do
        allow($redis).to receive(:get).with(redis_key).and_return('5')
        allow($redis).to receive(:incr).with(redis_key)
      end

      it 'increments the nudge count' do
        expect($redis).to receive(:incr).with(redis_key)
        user.increment_upgrade_package_nudge_seen_count
      end
    end
  end

  describe '#has_user_subscribed_to_one_month_plan?' do
    let(:user) { create(:user) }

    context 'when user plan is one month' do
      before :each do
        FactoryBot.create(:user_plan, user: user, plan: FactoryBot.create(:plan, duration_in_months: 1),
                          end_date: Time.zone.now + 1.month)
      end
      it 'returns true' do
        expect(user.has_user_subscribed_to_one_month_plan?).to be true
      end
    end

    context 'when user plan is not one month' do
      before :each do
        FactoryBot.create(:user_plan, user: user, plan: FactoryBot.create(:plan, duration_in_months: 2),
                          end_date: Time.zone.now + 2.months)
      end
      it 'returns false' do
        expect(user.has_user_subscribed_to_one_month_plan?).to be false
      end
    end

    context 'when user plan is nil' do
      it 'returns false' do
        expect(user.has_user_subscribed_to_one_month_plan?).to be false
      end
    end
  end

  describe '#delete_upgrade_package_nudge_count' do
    let(:user) { create(:user) }
    let(:redis_key) { Constants.upgrade_package_nudge_count_key(user.id) }

    context 'when the key exists in Redis' do
      before do
        allow($redis).to receive(:del).with(redis_key).and_return(1)
      end

      it 'deletes the key from Redis' do
        expect($redis).to receive(:del).with(redis_key)
        user.delete_upgrade_package_nudge_count
      end
    end

    context 'when the key does not exist in Redis' do
      before do
        allow($redis).to receive(:del).with(redis_key).and_return(0)
      end

      it 'does not raise an error' do
        expect { user.delete_upgrade_package_nudge_count }.not_to raise_error
      end
    end
  end
end
