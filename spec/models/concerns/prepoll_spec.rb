require 'rails_helper'
require 'spec_helper'

RSpec.describe Prepoll do
  include Prepoll

  describe '#prepoll_carousel' do
    context '#prepoll_carousel' do
      before :each do
        @state_circle = FactoryBot.create(:circle, name: 'state_circle', level: :state, circle_type: :location)
        @district_circle = FactoryBot.create(:circle, name: 'district_circle', level: :district, circle_type: :location, parent_circle: @state_circle)
        @mandal_circle = FactoryBot.create(:circle, name: 'mandal_circle', level: :mandal, circle_type: :location, parent_circle: @district_circle)
        @village_circle = FactoryBot.create(:circle, name: 'village_circle', level: :village, circle_type: :location, parent_circle: @mandal_circle)
        @village_circle2 = FactoryBot.create(:circle, name: 'village_circle2', level: :village, circle_type: :location, parent_circle: @mandal_circle)

        @user = FactoryBot.create(:user, village_id: @village_circle.id)
        @user.populate_location
      end
      it 'returns nil when there are no prepoll creatives on state' do
        expect(prepoll_carousel(@user)).to eq(nil)
      end
      it 'returns carousel with no next page when there are less prepoll creatives on state' do
        image_600x750 = fixture_file_upload('app/assets/images/poster_creative_600x750.png', 'image/png')
        admin_medium1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        poster_creative1 = FactoryBot.create(:poster_creative, creative_kind: :prepoll, photo_v3: admin_medium1, photo_v2: nil, event: nil)
        poster_creative2 = FactoryBot.create(:poster_creative, creative_kind: :prepoll, photo_v3: admin_medium1, photo_v2: nil, event: nil)
        FactoryBot.create(:poster_creative_circle, poster_creative: poster_creative1, circle: @state_circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: poster_creative2, circle: @state_circle)

        carousel = prepoll_carousel(@user)
        expect(carousel[:items].count).to eq(2)
        expect(carousel[:items].first[:id]).to eq(poster_creative2.id.to_s)
        expect(carousel[:items][1][:id]).to eq(poster_creative1.id.to_s)
        expect(carousel[:next_page_url]).to eq(nil)
      end
      it 'returns carousel with next page when there are more prepoll creatives on state' do
        image_600x750 = fixture_file_upload('app/assets/images/poster_creative_600x750.png', 'image/png')
        admin_medium1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        poster_creative1 = FactoryBot.create(:poster_creative, creative_kind: :prepoll, photo_v3: admin_medium1, photo_v2: nil, event: nil)
        poster_creative2 = FactoryBot.create(:poster_creative, creative_kind: :prepoll, photo_v3: admin_medium1, photo_v2: nil, event: nil)
        poster_creative3 = FactoryBot.create(:poster_creative, creative_kind: :prepoll, photo_v3: admin_medium1, photo_v2: nil, event: nil)
        poster_creative4 = FactoryBot.create(:poster_creative, creative_kind: :prepoll, photo_v3: admin_medium1, photo_v2: nil, event: nil)
        poster_creative5 = FactoryBot.create(:poster_creative, creative_kind: :prepoll, photo_v3: admin_medium1, photo_v2: nil, event: nil)
        poster_creative6 = FactoryBot.create(:poster_creative, creative_kind: :prepoll, photo_v3: admin_medium1, photo_v2: nil, event: nil)
        FactoryBot.create(:poster_creative_circle, poster_creative: poster_creative1, circle: @state_circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: poster_creative2, circle: @state_circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: poster_creative3, circle: @state_circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: poster_creative4, circle: @state_circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: poster_creative5, circle: @state_circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: poster_creative6, circle: @state_circle)

        carousel = prepoll_carousel(@user)
        expect(carousel[:items].count).to eq(5)
        expect(carousel[:items].pluck(:id)).to eq([poster_creative6.id.to_s, poster_creative5.id.to_s, poster_creative4.id.to_s, poster_creative3.id.to_s, poster_creative2.id.to_s])
        next_page_url = carousel[:next_page_url]

        next_page_uri = URI.parse(next_page_url)
        expect(next_page_uri.path).to eq('/creatives')
        query_params = CGI::parse(next_page_uri.query)
        expect(query_params).to eq({
          'circle_id' => [@state_circle.id.to_s],
          'category_kind' => ['prepoll'],
          'offset' => ['5'],
          'count' => ['5']
        })
      end
    end
  end
end
