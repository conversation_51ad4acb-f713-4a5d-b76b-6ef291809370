require 'rails_helper'
require 'spec_helper'

RSpec.describe Manifesto do
  include Manifesto
  include Elections2024

  describe '#manifesto_carousels' do
    context 'when there are party coalitions in the state' do
      it 'returns empty array' do
        user = FactoryBot.create(:user)
        expect(manifesto_carousels(user)).to eq([])
      end
    end

    context 'when there are party coalitions but no manifestos' do
      before :each do
        @user = FactoryBot.create(:user)
        @party1 = FactoryBot.create(:circle, level: :political_party)
        @party2 = FactoryBot.create(:circle, level: :political_party)

        allow(self).to receive(:state_coalitions).and_return([
          Elections2024::PartyCoalition.new(id: 'first', circle_ids: [@party1.id]),
          Elections2024::PartyCoalition.new(id: 'second', circle_ids: [@party2.id]),
        ])
        allow(self).to receive(:all_coalitions).and_return([
          Elections2024::PartyCoalition.new(id: 'first', circle_ids: [@party1.id]),
          Elections2024::PartyCoalition.new(id: 'second', circle_ids: [@party2.id]),
        ])
      end

      it 'returns empty array' do
        expect(manifesto_carousels(@user)).to eq([])
      end
    end

    context 'when there are party coalitions with manifestos' do
      before :each do
        @user = FactoryBot.create(:user)
        @party1 = FactoryBot.create(:circle, level: :political_party)
        @party2 = FactoryBot.create(:circle, level: :political_party)
        image_600x750 = fixture_file_upload('app/assets/images/poster_creative_600x750.png', 'image/png')
        image_630x940 = fixture_file_upload('app/assets/images/poster_creative_630x940.png', 'image/png')
        @admin_medium1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        FactoryBot.create(:user_circle, user: @user, circle: @party2)

        @user.update_affiliated_party_circle_id

        allow(self).to receive(:state_coalitions).and_return([
          Elections2024::PartyCoalition.new(id: 'first', circle_ids: [@party1.id]),
          Elections2024::PartyCoalition.new(id: 'second', circle_ids: [@party2.id]),
        ])
        allow(self).to receive(:all_coalitions).and_return([
          Elections2024::PartyCoalition.new(id: 'first', circle_ids: [@party1.id]),
          Elections2024::PartyCoalition.new(id: 'second', circle_ids: [@party2.id]),
        ])

        @party1_creatives = []

        for i in 1..(2*Constants.creatives_count)
          @party1_creatives << FactoryBot.create(:poster_creative, creative_kind: :manifesto, photo_v3: @admin_medium1, photo_v2: @admin_medium2, event: nil)
        end

        @party1_creatives.each do |creative|
          FactoryBot.create(:poster_creative_circle, poster_creative: creative, circle: @party1)
        end

        @party2_creatives = []

        for i in 1..(2*Constants.creatives_count)
          @party2_creatives << FactoryBot.create(:poster_creative, creative_kind: :manifesto, photo_v3: @admin_medium1, photo_v2: @admin_medium2, event: nil)
        end

        @party2_creatives.each do |creative|
          FactoryBot.create(:poster_creative_circle, poster_creative: creative, circle: @party2)
        end
      end

      it 'returns the same number of carousels as the number of party coalitions' do
        carousels = manifesto_carousels(@user)
        expect(carousels.length).to eq(2)
        first_carousel = carousels[0]
        expect(first_carousel[:items].pluck(:id)).to eq(@party2_creatives.map(&:id).map(&:to_s).first(Constants.creatives_count))
        expect(first_carousel[:next_page_url]).to be_present
        second_carousel = carousels[1]
        expect(second_carousel[:items].pluck(:id)).to eq(@party1_creatives.map(&:id).map(&:to_s).first(Constants.creatives_count))
        expect(second_carousel[:next_page_url]).to be_present

        first_carousel_next_page = manifesto_carousel_next_page(user: @user, circle_id: @party2.id, offset: Constants.creatives_count)
        expect(first_carousel_next_page[:items].pluck(:id)).to eq(@party2_creatives.map(&:id).map(&:to_s).last(Constants.creatives_count))

        second_carousel_next_page = manifesto_carousel_next_page(user: @user, circle_id: @party1.id, offset: Constants.creatives_count)
        expect(second_carousel_next_page[:items].pluck(:id)).to eq(@party1_creatives.map(&:id).map(&:to_s).last(Constants.creatives_count))
      end
    end
  end
end
