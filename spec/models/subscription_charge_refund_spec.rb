require 'rails_helper'

RSpec.describe SubscriptionChargeRefund, type: :model do
  describe 'validations' do
    let(:subscription) { create(:subscription) }
    let(:subscription_charge) { create(:subscription_charge, subscription: subscription, amount: 100, charge_amount: 100, status: :success) }

    it 'is valid with valid attributes' do
      refund = build(:subscription_charge_refund, subscription_charge: subscription_charge, amount: 50)
      expect(refund).to be_valid
    end

    it 'is not valid without an amount' do
      refund = build(:subscription_charge_refund, subscription_charge: subscription_charge, amount: nil)
      expect(refund).not_to be_valid
    end

    it 'is not valid with a negative amount' do
      refund = build(:subscription_charge_refund, subscription_charge: subscription_charge, amount: -10)
      expect(refund).not_to be_valid
    end

    it 'is not valid with a zero amount' do
      refund = build(:subscription_charge_refund, subscription_charge: subscription_charge, amount: 0)
      expect(refund).not_to be_valid
    end

    it 'is not valid with an amount exceeding available refund amount' do
      refund = build(:subscription_charge_refund, subscription_charge: subscription_charge, amount: 101)
      expect(refund).not_to be_valid
      expect(refund.errors[:amount]).to include("exceeds available refund amount of 100")
    end

    it 'is not valid when subscription charge is already partially refunded' do
      subscription_charge.update(refunded_amount: 60, amount: 40)
      refund = build(:subscription_charge_refund, subscription_charge: subscription_charge, amount: 40)
      expect(refund).to be_valid

      refund = build(:subscription_charge_refund, subscription_charge: subscription_charge, amount: 41)
      expect(refund).not_to be_valid
      expect(refund.errors[:amount]).to include("exceeds available refund amount of 40")
    end
  end

  describe 'state transitions' do
    let(:subscription) { create(:subscription) }
    let(:subscription_charge) { create(:subscription_charge, subscription: subscription, amount: 100, charge_amount: 100, status: :success) }
    let(:refund) { create(:subscription_charge_refund, subscription_charge: subscription_charge, amount: 50) }

    it 'is initially in initiated state' do
      expect(refund.status).to eq('initiated')
    end

    it 'can transition from initiated to failed' do
      expect(refund.may_mark_as_failed?).to be_truthy
      refund.mark_as_failed!
      expect(refund.status).to eq('failed')
    end

    it 'can transition from initiated to success' do
      expect(refund.may_mark_as_success?).to be_truthy

      # Mock the process_refund_completion method to avoid side effects
      allow(subscription_charge).to receive(:process_refund_completion)

      refund.mark_as_success!
      expect(refund.status).to eq('success')
      expect(subscription_charge).to have_received(:process_refund_completion).with(refund)
    end

    it 'cannot transition from failed to success' do
      refund.mark_as_failed!
      expect(refund.may_mark_as_success?).to be_falsey
    end

    it 'cannot transition from success to failed' do
      # Mock the process_refund_completion method to avoid side effects
      allow(subscription_charge).to receive(:process_refund_completion)

      refund.mark_as_success!
      expect(refund.may_mark_as_failed?).to be_falsey
    end
  end

  describe 'after_success callback' do
    let(:subscription) { create(:subscription) }
    let(:subscription_charge) { create(:subscription_charge, subscription: subscription, amount: 100, charge_amount: 100, status: :success) }
    let(:refund) { create(:subscription_charge_refund, subscription_charge: subscription_charge, amount: 50) }

    it 'calls process_refund_completion on the subscription charge' do
      expect(subscription_charge).to receive(:process_refund_completion).with(refund)
      refund.after_success
    end
  end


end
