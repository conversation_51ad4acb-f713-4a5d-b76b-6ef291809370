require 'rails_helper'

RSpec.describe Campaign, type: :model do
  describe '.latest_campaign_for_cohort' do
    let(:cohort) { 'test_cohort' }
    let(:duration_in_months) { 12 }
    let!(:campaign) { create(:campaign, cohort_details_json: { test_cohort: [{ plan_duration_in_months: 12,
                                                                               discount_percentage: 20 }] }) }

    it 'returns the latest active campaign for the given cohort and duration' do
      result = Campaign.latest_campaign_for_cohort(cohort: cohort, duration_in_months: duration_in_months)
      expect(result).to eq(campaign)
    end

    it 'returns nil if no active campaign matches the given cohort and duration' do
      result = Campaign.latest_campaign_for_cohort(cohort: 'non_existent_cohort',
                                                   duration_in_months: duration_in_months)
      expect(result).to be_nil
    end

    it 'returns nil if no active campaign is within the current time range' do
      campaign.update(start_time: 2.days.from_now, end_time: 3.days.from_now)
      result = Campaign.latest_campaign_for_cohort(cohort: cohort, duration_in_months: duration_in_months)
      expect(result).to be_nil
    end
  end

  describe '#cohort_discount_percentage_for_campaign' do
    let(:campaign) { create(:campaign, cohort_details_json: { test_cohort: [{ plan_duration_in_months: 12,
                                                                              discount_percentage: 20 }] }) }

    it 'returns the discount percentage for the given cohort and plan duration' do
      result = campaign.cohort_discount_percentage_for_campaign(cohort: 'test_cohort', plan_duration_in_months: 12)
      expect(result).to eq(20)
    end
  end
end
