require 'rails_helper'

RSpec.describe RedWord, type: :model do
  describe 'after save' do
    let(:red_word) { FactoryBot.create(:red_word) }

    it 'performs index' do
      allow(RedWord).to receive(:check_for_names).and_return(true)
      expect(IndexRedWord).to receive(:perform_async).with(red_word.id, red_word.word, red_word.is_hate_speech)
      red_word.save
    end
  end

  describe 'after destroy' do
    let(:red_word) { FactoryBot.create(:red_word) }

    it 'performs delete index' do
      allow(RedWord).to receive(:destroy).and_return(true)

      expect(DeleteRedWordIndex).to receive(:perform_async).with(red_word.id)
      red_word.destroy
    end
  end

  describe 'check false positive red words' do
    let(:red_word) { FactoryBot.create(:red_word, word: 'test') }
    let(:text) { 'test is a word' }

    it 'matches a word' do
      words = [{"_source"=>{"red_word"=>"test", "is_hate_speech"=>true}}]
      expect(RedWord.check_false_positive_red_words(text, words)).to eq(true)
    end

    it 'does not match a word' do
      words = [{"_source"=>{"red_word"=>"test1", "is_hate_speech"=>true}}]
      expect(RedWord.check_false_positive_red_words(text, words)).to eq(false)
    end
  end

  describe 'check' do
    let(:index) { EsUtil.get_red_word_index }

    it 'performs a search and handles red words when found' do
      text = 'test'
      red_word_hits = ['test', 'test2']

      expect(ES_CLIENT).to receive(:search).with(
        index: index,
        body: {
          query: {
            bool: {
              must: [
                {
                  match: {
                    red_word: {
                      query: text
                    }
                  }
                },
                {
                  match: {
                    is_hate_speech: {
                      query: true
                    }
                  }
                }
              ]
            }
          }
        }
      ).and_return('hits' => { 'hits' => red_word_hits })

      expect(RedWord).to receive(:check_false_positive_red_words).with(text, red_word_hits)

      RedWord.check(text)
    end

    it 'performs a search and handles red words when not found' do
      text = 'test'
      red_word_hits = []

      expect(ES_CLIENT).to receive(:search).with(
        index: index,
        body: {
          query: {
            bool: {
              must: [
                {
                  match: {
                    red_word: {
                      query: text
                    }
                  }
                },
                {
                  match: {
                    is_hate_speech: {
                      query: true
                    }
                  }
                }
              ]
            }
          }
        }
      ).and_return('hits' => { 'hits' => red_word_hits })

      expect(RedWord).not_to receive(:check_false_positive_red_words)
      expect(RedWord.check(text)).to eq(false)
    end

    it 'handles exceptions' do
      text = 'test'

      expect(ES_CLIENT).to receive(:search).with(
        index: index,
        body: {
          query: {
            bool: {
              must: [
                {
                  match: {
                    red_word: {
                      query: text
                    }
                  }
                },
                {
                  match: {
                    is_hate_speech: {
                      query: true
                    }
                  }
                }
              ]
            }
          }
        }
      ).and_raise('error')

      expect(RedWord).not_to receive(:check_false_positive_red_words)
      expect(RedWord.check(text)).to eq(false)
    end
  end

  describe 'check for names' do
    let(:index) { EsUtil.get_red_word_index }

    it 'performs a search and handles red words when found' do
      text = 'test'
      red_word_hits = ['test', 'test2']

      expect(ES_CLIENT).to receive(:search).with(
        index: index,
        body: {
          query: {
            bool: {
              must: [
                {
                  match: {
                    red_word: {
                      query: text
                    }
                  }
                }
              ]
            }
          }
        }
      ).and_return('hits' => { 'hits' => red_word_hits })

      expect(RedWord).to receive(:check_false_positive_red_words).with(text, red_word_hits)

      RedWord.check_for_names(text)
    end

    it 'performs a search and handles red words when not found' do
      text = 'test'
      red_word_hits = []

      expect(ES_CLIENT).to receive(:search).with(
        index: index,
        body: {
          query: {
            bool: {
              must: [
                {
                  match: {
                    red_word: {
                      query: text
                    }
                  }
                }
              ]
            }
          }
        }
      ).and_return('hits' => { 'hits' => red_word_hits })

      expect(RedWord).not_to receive(:check_false_positive_red_words)
      expect(RedWord.check_for_names(text)).to eq(false)
    end

    it 'handles exceptions' do
      text = 'test'

      expect(ES_CLIENT).to receive(:search).with(
        index: index,
        body: {
          query: {
            bool: {
              must: [
                {
                  match: {
                    red_word: {
                      query: text
                    }
                  }
                }
              ]
            }
          }
        }
      ).and_raise('error')

      expect(RedWord).not_to receive(:check_false_positive_red_words)
      expect(RedWord.check_for_names(text)).to eq(false)
    end

  end
end
