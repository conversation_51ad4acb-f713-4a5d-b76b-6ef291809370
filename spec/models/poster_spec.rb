require 'rails_helper'

RSpec.describe Poster, type: :model do

  describe 'poster' do
    context 'validate' do
      before :each do
        @party_circle = FactoryBot.create(:circle)

        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.create(:poster, circle: @party_circle, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo))])
      end
      it 'should be valid' do
        expect(@poster).to be_valid
      end
      it 'is not valid without a name' do
        @poster.name = nil
        expect(@poster).to_not be_valid
      end
      it 'is not valid without a circle' do
        @poster.circle = nil
        expect(@poster).to_not be_valid
      end
      it 'is not valid without a admin_user' do
        @poster.admin_user = nil
        expect(@poster).to_not be_valid
      end
      it 'is not valid without a poster_type' do
        @poster.poster_type = nil
        expect(@poster).to_not be_valid
      end
      it 'is not valid without a start_time' do
        @poster.start_time = nil
        expect(@poster).to_not be_valid
      end
      it 'is not valid without a end_time' do
        @poster.end_time = nil
        expect(@poster).to_not be_valid
      end
    end
  end

  describe "poster type" do
    context "validate poster with jpg photo" do
      before :each do
        @party_circle = FactoryBot.create(:circle)
      end

      it "valid with a normal poster type" do
        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.create(:poster, circle: @party_circle, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo))])
        expect(@poster.poster_type.to_sym).to eq(:normal)
        expect(@poster).to be_valid
      end

      it "not valid with a frame poster type" do
        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.build(:poster, circle: @party_circle, poster_type: :frame, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo), photo_orientation: :portrait)])
        expect(@poster.poster_type.to_sym).to eq(:frame)
        expect { @poster.save! }.to raise_error(ActiveRecord::RecordInvalid, "Validation failed: Poster photos framed posters should be in png format")
      end
    end

    context "validate poster with png photo" do
      before :each do
        @party_circle = FactoryBot.create(:circle)
      end

      it "valid with a frame poster type" do
        # send data attribute to pass validations
        frame_poster_photo = fixture_file_upload("app/assets/images/poster_frame.png", "image/png")

        @poster = FactoryBot.create(:poster, circle: @party_circle, poster_type: :frame, poster_photos: [FactoryBot.build(:poster_photo, blob_data: frame_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: frame_poster_photo), photo_orientation: :portrait)])
        expect(@poster.poster_type.to_sym).to eq(:frame)
        expect(@poster).to be_valid
      end
    end
  end

  describe "poster photo orientation" do
    context "validate poster with poster photo orientation" do
      before :each do
        @party_circle = FactoryBot.create(:circle)
      end

      it "valid with a portrait photo orientation" do
        # send data attribute to pass validations
        frame_poster_photo = fixture_file_upload("app/assets/images/poster_frame.png", "image/png")

        @poster = FactoryBot.create(:poster, circle: @party_circle, poster_type: :frame, poster_photos: [FactoryBot.build(:poster_photo, blob_data: frame_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: frame_poster_photo), photo_orientation: :portrait)])
        expect(@poster.poster_type.to_sym).to eq(:frame)
        expect(@poster.poster_photos.last.photo_orientation.to_sym).to eq(:portrait)
        expect(@poster).to be_valid
      end

      it "valid with a landscape photo orientation" do
        # send data attribute to pass validations
        frame_poster_photo = fixture_file_upload("app/assets/images/poster_frame.png", "image/png")

        @poster = FactoryBot.create(:poster, circle: @party_circle, poster_type: :frame, poster_photos: [FactoryBot.build(:poster_photo, blob_data: frame_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: frame_poster_photo), photo_orientation: :landscape)])
        expect(@poster.poster_type.to_sym).to eq(:frame)
        expect(@poster.poster_photos.last.photo_orientation.to_sym).to eq(:landscape)
        expect(@poster).to be_valid
      end

      it "not valid with a nil photo orientation" do
        # send data attribute to pass validations
        frame_poster_photo = fixture_file_upload("app/assets/images/poster_frame.png", "image/png")

        @poster = FactoryBot.build(:poster, circle: @party_circle, poster_type: :frame, poster_photos: [FactoryBot.build(:poster_photo, blob_data: frame_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: frame_poster_photo))])

        expect { @poster.save! }.to raise_error(ActiveRecord::RecordInvalid, "Validation failed: Poster photos photo orientation is absent")
      end
    end
  end

  describe "leader photo ring color" do
    context "validate poster with leader photo ring color" do
      before :each do
        @party_circle = FactoryBot.create(:circle)
      end

      it "valid with leader photo ring color" do
        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.create(:poster, circle: @party_circle, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo), leader_photo_ring_color: :light)])
        expect(@poster.poster_photos.first.leader_photo_ring_color.to_sym).to be(:light)
        expect(@poster).to be_valid
      end

      it "not valid with out a leader photo ring color" do
        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.build(:poster, circle: @party_circle, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo), leader_photo_ring_color: nil)])
        expect { @poster.save! }.to raise_error(ActiveRecord::RecordInvalid, "Validation failed: Poster photos leader photo ring color is absent")
      end
    end
  end

  describe "poster photos" do
    context "validate poster photos presence" do
      before :each do
        @party_circle = FactoryBot.create(:circle)
      end

      it "valid photos present" do
        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.create(:poster, circle: @party_circle, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo))])
        expect(@poster).to be_valid
      end

      it "not valid without photos" do
        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/logo.png", "image/png")

        @poster = FactoryBot.build(:poster, circle: @party_circle, poster_photos: [FactoryBot.build(:poster_photo, blob_data: nil, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo))])
        expect { @poster.save! }.to raise_error(ActiveRecord::RecordInvalid, "Validation failed: Poster photos photos are absent")
      end
    end
  end

  describe "poster photos" do
    context "validate poster photos aspect ratio" do
      before :each do
        @party_circle = FactoryBot.create(:circle)
      end

      it "valid with right aspect ratio" do
        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

        @poster = FactoryBot.create(:poster, circle: @party_circle, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo))])
        expect(@poster).to be_valid
      end

      it "not valid with wrong aspect ratio" do
        # send photo data to pass validations
        normal_poster_photo = fixture_file_upload("app/assets/images/logo.png", "image/png")

        @poster = FactoryBot.build(:poster, circle: @party_circle, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo))])
        expect { @poster.save! }.to raise_error(ActiveRecord::RecordInvalid, "Validation failed: Poster photos is not in valid aspect ratio")
      end
    end
  end

  describe "slack notification" do
    # creating a double for the object to create a mock object that imitates the behavior of the real object
    let(:notifier) { double('notifier') }

    before do
      # allow method to mock the Slack::Notifier.new method and return the notifier double.
      allow(Slack::Notifier).to receive(:new).and_return(notifier)
    end

    it 'sends a message to Slack' do
      message = 'Poster enabled'
      expect(notifier).to receive(:ping).with(message)

      Poster.ping_to_slack(message)
    end
  end

  describe "#get_share_text for poster" do
    before :each do
      @user = FactoryBot.create(:user)
      @party_circle = FactoryBot.create(:circle)

      # send photo data to pass validations
      normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")

      @poster = FactoryBot.create(:poster, circle: @party_circle, poster_photos:
        [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium,
                                                                                                 blob_data: normal_poster_photo))])
    end
    context "check share text for poster" do
      it "returns the share text for poster" do
        expect(@poster.get_share_text(@user)).to be_present
      end
    end
  end
end
