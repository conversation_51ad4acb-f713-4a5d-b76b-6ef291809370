require 'rails_helper'

RSpec.describe User, type: :model do
  # Commented out for new feature auto pay deployment - TAG: DONT_FIX_AFTER_AUTO_PAY_RELEASE
  # describe "#get_subscription_data of user" do
  #   context "when user poster layout data is collected" do
  #     it "should return the subscription data if user is not subscribed" do
  #       photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
  #       @user = FactoryBot.create(:user)
  #       @user.poster_photo = FactoryBot.create(:admin_medium, data: photo)
  #       @user.poster_photo_with_background = FactoryBot.create(:admin_medium, data: photo)
  #       @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
  #                                               h1_count: 1,
  #                                               h2_count: 1,
  #                                               user_leader_photos: [
  #                                                 FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, data: photo),
  #                                                                   header_type: :header_1, priority: 1),
  #                                                 FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, data: photo),
  #                                                                   header_type: :header_2, priority: 1)])
  #       @order = FactoryBot.create(:order, user: @user)
  #       expect(@user.get_subscription_data.length).to eq(2)
  #     end
  #   end
  #
  #   context "when user poster layout data is not collected" do
  #     it "should return nil if data is not collected" do
  #       @user = FactoryBot.create(:user)
  #       @order = FactoryBot.create(:order, user: @user)
  #       expect(@user.get_subscription_data).to eq([nil, nil])
  #     end
  #   end
  #
  #   context "when order is not present" do
  #     it "should return nil if order is not present" do
  #       @user = FactoryBot.create(:user)
  #       expect(@user.get_subscription_data).to eq([nil, nil])
  #     end
  #   end
  # end

  describe "#get_user_opted_frame_ids_with_is_locked" do
    context "when user has opted for frames" do
      it "returns frame ids with is_locked false if user subscribed" do
        @user = FactoryBot.create(:user)
        @frame1 = FactoryBot.create(:frame, identifier: :party_curved_identity, frame_type: :premium, gold_border: false,
                                    has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                    identity_type: :curved)
        @frame2 = FactoryBot.create(:frame, identifier: :party_neon_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: true, is_neutral_frame: false,
                                    has_footer_party_icon: true)

        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame1.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame2.id)

        user_opted_frame_ids_with_is_locked = @user.get_user_opted_frame_ids_with_is_locked(true)
        expect(user_opted_frame_ids_with_is_locked).to be_present
        expect(user_opted_frame_ids_with_is_locked.count).to eq(2)
        expect(user_opted_frame_ids_with_is_locked.keys).to eq([@frame1.id, @frame2.id])
        expect(user_opted_frame_ids_with_is_locked[@frame1.id]).to eq(false)
        expect(user_opted_frame_ids_with_is_locked[@frame2.id]).to eq(false)
      end
      it "returns frame ids with is_locked true if user not subscribed" do
        @user = FactoryBot.create(:user)
        @frame1 = FactoryBot.create(:frame, identifier: :party_curved_identity, frame_type: :premium, gold_border: false,
                                    has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                    identity_type: :curved)
        @frame2 = FactoryBot.create(:frame, identifier: :party_neon_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: true, is_neutral_frame: false,
                                    has_footer_party_icon: true)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame1.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame2.id)
        user_opted_frame_ids_with_is_locked = @user.get_user_opted_frame_ids_with_is_locked(false)
        expect(user_opted_frame_ids_with_is_locked).to be_present
        expect(user_opted_frame_ids_with_is_locked.count).to eq(2)
        expect(user_opted_frame_ids_with_is_locked.keys).to eq([@frame1.id, @frame2.id])
        expect(user_opted_frame_ids_with_is_locked[@frame1.id]).to eq(true)
        expect(user_opted_frame_ids_with_is_locked[@frame2.id]).to eq(true)
      end
    end
  end

  # Commented out for new feature auto pay deployment - TAG: DONT_FIX_AFTER_AUTO_PAY_RELEASE
  # describe "#get_subscription_toast" do
  #   context "when user poster order is in pending or last_transaction_failed then user get subscription toast" do
  #     before :each do
  #       @user = FactoryBot.create(:user)
  #     end
  #     it "should return the subscription toast of user if order is in pending" do
  #       @order = FactoryBot.create(:order, user: @user, status: :pending)
  #       subscription_toast = @user.get_subscription_toast(@order)
  #       expect(subscription_toast).not_to be_nil
  #       expect(subscription_toast[:feed_type]).to eq("feed_toast")
  #       expect(subscription_toast[:message]).to eq("మీ మునుపటి ట్రాన్సాక్షన్ ప్రాసెస్ చేయబడుతోంది. దయచేసి కాసేపు ఆగి చూడండి.")
  #       expect(subscription_toast[:icon_color]).to eq(0xff664d03)
  #       expect(subscription_toast[:close_icon_color]).to eq(0xff664d03)
  #       expect(subscription_toast[:toast_color]).to eq(0xfffff2cd)
  #       expect(subscription_toast[:header_font_color]).to eq(0xff664d03)
  #       expect(subscription_toast[:message_font_color]).to eq(0xff664d03)
  #     end
  #
  #     it "should return the subscription toast of user if order is in last_transaction_failed" do
  #       @order = FactoryBot.create(:order, user: @user, status: :last_transaction_failed)
  #       subscription_toast = @user.get_subscription_toast(@order)
  #       expect(subscription_toast).not_to be_nil
  #       expect(subscription_toast[:feed_type]).to eq("feed_toast")
  #       expect(subscription_toast[:message]).to eq("మీ మునుపటి ట్రాన్సాక్షన్ విఫలమైనది. దయచేసి మళ్లీ ప్రయత్నించండి.")
  #       expect(subscription_toast[:icon_color]).to eq(0xff180000)
  #       expect(subscription_toast[:close_icon_color]).to eq(0xff180000)
  #       expect(subscription_toast[:toast_color]).to eq(0xfffeb9b9)
  #       expect(subscription_toast[:header_font_color]).to eq(0xff180000)
  #       expect(subscription_toast[:message_font_color]).to eq(0xff180000)
  #     end
  #
  #     it "should return nil if order is not in pending or last_transaction_failed" do
  #       @order = FactoryBot.create(:order, user_id: @user.id, status: :opened, order_items: [
  #         FactoryBot.build(:order_item, item_type: "Product", item_id: Constants.get_poster_product_id,
  #                          duration_in_months: 1)
  #       ])
  #
  #       @order.update(status: :successful)
  #       subscription_toast = @user.get_subscription_toast(@order)
  #       expect(subscription_toast).to be_nil
  #     end
  #   end
  # end

  describe "#my_poster_sections" do
    context "it returns subscription toast, subscription banner,events and category sections" do
      before :each do
        AppVersionSupport.new('2402.15.01')
        @state_level_circle = FactoryBot.create(:circle,
                                                name: Faker::Name.unique.name,
                                                name_en: Faker::Name.unique.name,
                                                active: true,
                                                members_count: 0,
                                                circle_type: :location,
                                                level: :state)
        @user = FactoryBot.create(:user, state_id: @state_level_circle.id)
      end
      it "should return if subscription toast, subscription banner,events and category sections are present
          for party affiliated user" do
        @circle = FactoryBot.create(:circle) # interest circle
        @user.affiliated_party_circle_id = @circle.id
        @user.save
        # user role
        @role = FactoryBot.create(:role,
                                  name: "testing",
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :absolute,
                                  quota_value: 2,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  active: true)
        @private_circle = FactoryBot.create(:circle, level: :private, circle_type: :my_circle)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role,
                                       parent_circle_id: @circle.id)

        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true, creative_kind: :info)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)
        @poster_creative1 = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1,
                                              photo_v2: @admin_medium_2, primary: true, creative_kind: :info)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative1, circle: @private_circle)
        @user_private_circle = FactoryBot.create(:user_circle, user: @user, circle: @private_circle)
        @poster_sections = @user.my_poster_sections
        expect(@poster_sections).not_to be_nil
        expect(@poster_sections[0][:title]).to eq("సమాచారం")
        expect(@poster_sections[0][:categories].length).to eq(1)
      end

      it "should return if subscription toast, subscription banner,events and category sections are present
          for non party affiliated user" do
        @circle = FactoryBot.create(:circle) # interest circle
        @circle_1 = FactoryBot.create(:circle) # interest circle
        @poster_sections = @user.my_poster_sections
        expect(@poster_sections).not_to be_nil
      end
    end
  end

  # Commented out for new feature auto pay deployment - TAG: DONT_FIX_AFTER_AUTO_PAY_RELEASE
  # describe "#get_subscription_json" do
  #   context "context when user poster details has taken and user is not yet subscribed" do
  #     before :each do
  #       @user = FactoryBot.create(:user)
  #     end
  #     it "should return the subscription json of user if user order is opened" do
  #       @order = FactoryBot.create(:order, user: @user, status: :opened)
  #       subscription_json = @user.get_subscription_json(@order)
  #       expect(subscription_json).not_to be_nil
  #       expect(subscription_json[:feed_type]).to eq("subscription_banner")
  #       expect(subscription_json[:subscribe_info_text]).to eq("ప్రీమియం పోస్టర్లు కోసం సబ్స్క్రైబ్ చెయ్యండి")
  #       expect(subscription_json[:subscribe_banner_text]).to eq("జాయిన్!")
  #       expect(subscription_json[:subscription_screen]).not_to be_nil
  #     end
  #     it "should return the subscription json of user if user order is pending" do
  #       @order = FactoryBot.create(:order, user: @user, status: :pending)
  #       subscription_json = @user.get_subscription_json(@order)
  #       expect(subscription_json).not_to be_nil
  #       expect(subscription_json[:feed_type]).to eq("subscription_banner")
  #       expect(subscription_json[:subscribe_info_text]).to eq("ట్రాన్సాక్షన్ ప్రాసెస్ చేయబడుతోంది..")
  #       expect(subscription_json[:subscribe_banner_text]).to eq("కాసేపు ఆగి చెక్ చేయండి..")
  #       expect(subscription_json[:subscription_screen]).to be_nil
  #     end
  #   end
  # end

  # Commented out for new feature auto pay deployment - TAG: DONT_FIX_AFTER_AUTO_PAY_RELEASE
  # describe "#get_subscription_screen_json" do
  #   context "when user poster details has taken and user is not yet subscribed" do
  #     before :each do
  #       @user = FactoryBot.create(:user)
  #     end
  #     it "should return the subscription screen json of user if order is opened" do
  #       @order = FactoryBot.create(:order, user: @user, status: :opened)
  #       subscription_screen_json = @user.get_subscription_screen_json(@order)
  #       expect(subscription_screen_json).not_to be_nil
  #       expect(subscription_screen_json[:header]).to eq("ప్రీమియం పోస్టర్లు")
  #       expect(subscription_screen_json[:order_id]).to eq("#{@order.id}")
  #     end
  #
  #     it "should return nil if order is pending" do
  #       @order = FactoryBot.create(:order, user: @user, status: :pending)
  #       subscription_screen_json = @user.get_subscription_screen_json(@order)
  #       expect(subscription_screen_json).to be_nil
  #     end
  #   end
  # end

  describe "#get_events_section" do
    context "get the data of events section" do
      before :each do
        @user = FactoryBot.create(:user)
      end
      it "should return the events section data nil if no events are there" do
        @events_section = @user.get_events_section([], @user.is_eligible_for_premium_creatives?)
        expect(@events_section).to be_nil
      end
      it "should return the events section data if events are there" do
        @event = FactoryBot.create(:event)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        expect(@event).not_to be_nil
        expect(@poster_creative).not_to be_nil
        @events_section = @user.get_events_section(@user.circles, @user.is_eligible_for_premium_creatives?)
        expect(@events_section).not_to be_nil
        expect(@events_section[:title]).to eq("ఈవెంట్స్")
        expect(@events_section[:categories]).not_to be_nil
        categories = @events_section[:categories]
        expect(categories.first[:title]).to eq(@event.name)
        expect(categories.first[:params][:id]).to eq("#{@event.id}")
        expect(categories.first[:image_urls][0]).to eq(@poster_creative.photo_v2.url)
      end

      it "should not return events section if user affiliated circle are not match with the event circles" do
        @event = FactoryBot.create(:event)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        expect(@event).not_to be_nil
        expect(@poster_creative).not_to be_nil
        @events_section = @user.get_events_section(@user.circles, @user.is_eligible_for_premium_creatives?)
        expect(@events_section).to be_nil
      end

      it "should return events which are in current time" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        expect(@event).not_to be_nil
        expect(@poster_creative).not_to be_nil
        @events_section = @user.get_events_section(@user.circles, @user.is_eligible_for_premium_creatives?)
        expect(@events_section).not_to be_nil
        expect(@events_section[:title]).to eq("ఈవెంట్స్")
        expect(@events_section[:categories]).not_to be_nil
        categories = @events_section[:categories]
        expect(categories.first[:title]).to eq(@event.name)
        expect(categories.first[:params][:id]).to eq("#{@event.id}")
        expect(categories.first[:image_urls][0]).to eq(@poster_creative.photo_v2.url)
      end

      it "should not return events which are not in current time" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 2.hours, end_time: Time.now + 4.hours)
        @event_1 = FactoryBot.create(:event, start_time: Time.zone.now + 2.hours, end_time: Time.now + 4.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @event_circle_1 = FactoryBot.create(:event_circle, event: @event_1, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event_1, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        expect(@event).not_to be_nil
        expect(@event_1).not_to be_nil
        expect(@poster_creative).not_to be_nil
        @events_section = @user.get_events_section(@user.circles, @user.is_eligible_for_premium_creatives?)
        expect(@events_section).not_to be_nil
        categories = @events_section[:categories]

        expect(categories.length).to eq(1)
        expect(categories.last[:title]).to eq(@event.name)
        expect(categories.last[:params][:id]).to eq("#{@event.id}")
      end

      it "should not return event if event doesn't have at least one poster creative" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        expect(@event).not_to be_nil
        @events_section = @user.get_events_section(@user.circles, @user.is_eligible_for_premium_creatives?)
        expect(@events_section).to be_nil
      end

      it "should not return event if event doesn't have poster creative in current time" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 2.hours, end_time: Time.now + 4.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true, start_time: Time.zone.now + 2.hours,
                                             end_time: Time.zone.now + 4.hours)
        expect(@event).not_to be_nil
        expect(@poster_creative).not_to be_nil
        @events_section = @user.get_events_section(@user.circles, @user.is_eligible_for_premium_creatives?)
        expect(@events_section).to be_nil
      end

      it "should return events in the priority order of political leader, political party and location" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @event_1 = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @event_2 = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @state_level_circle = FactoryBot.create(:circle,
                                                name: Faker::Name.unique.name,
                                                name_en: Faker::Name.unique.name,
                                                active: true,
                                                members_count: 0,
                                                circle_type: :location,
                                                level: :state)
        @political_leader_level_circle = FactoryBot.create(:circle,
                                                           name: Faker::Name.unique.name,
                                                           name_en: Faker::Name.unique.name,
                                                           active: true,
                                                           members_count: 0,
                                                           circle_type: :interest,
                                                           level: :political_leader,
                                                           circle_photos:
                                                             [FactoryBot.build(:circle_photo,
                                                                               photo: FactoryBot.create(:photo),
                                                                               photo_type: :poster,
                                                                               photo_order: 1)])
        @political_party_level_circle = FactoryBot.create(:circle,
                                                          name: Faker::Name.unique.name,
                                                          name_en: Faker::Name.unique.name,
                                                          active: true,
                                                          members_count: 0,
                                                          circle_type: :interest,
                                                          level: :political_party)
        @event_circle = FactoryBot.create(:event_circle, event: @event_1, circle: @state_level_circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @political_leader_level_circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event_2, circle: @political_party_level_circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @state_level_circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @political_leader_level_circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @political_party_level_circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                          photo_v2: @admin_medium_2, primary: true)
        FactoryBot.create(:poster_creative, event: @event_1, photo_v3: @admin_medium_1,
                          photo_v2: @admin_medium_2, primary: true)
        FactoryBot.create(:poster_creative, event: @event_2, photo_v3: @admin_medium_1,
                          photo_v2: @admin_medium_2, primary: true)

        @events_section = @user.get_events_section(@user.circles, @user.is_eligible_for_premium_creatives?)
        expect(@events_section).not_to be_nil
        categories = @events_section[:categories]
        expect(categories.length).to eq(3)
        expect(categories.first[:params][:id]).to eq("#{@event.id}")
        expect(categories.second[:params][:id]).to eq("#{@event_2.id}")
        expect(categories.last[:params][:id]).to eq("#{@event_1.id}")
      end

      it "should return events in the priority order of their priority order - political leader, political party and location" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @event_1 = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @event_2 = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours,
                                     priority: :medium)
        @event_3 = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours,
                                     priority: :high)
        @state_level_circle = FactoryBot.create(:circle,
                                                name: Faker::Name.unique.name,
                                                name_en: Faker::Name.unique.name,
                                                active: true,
                                                members_count: 0,
                                                circle_type: :location,
                                                level: :state)
        @political_leader_level_circle = FactoryBot.create(:circle,
                                                           name: Faker::Name.unique.name,
                                                           name_en: Faker::Name.unique.name,
                                                           active: true,
                                                           members_count: 0,
                                                           circle_type: :interest,
                                                           level: :political_leader,
                                                           circle_photos:
                                                             [FactoryBot.build(:circle_photo,
                                                                               photo: FactoryBot.create(:photo),
                                                                               photo_type: :poster,
                                                                               photo_order: 1)])
        @political_party_level_circle = FactoryBot.create(:circle,
                                                          name: Faker::Name.unique.name,
                                                          name_en: Faker::Name.unique.name,
                                                          active: true,
                                                          members_count: 0,
                                                          circle_type: :interest,
                                                          level: :political_party)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @state_level_circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event_1, circle: @political_leader_level_circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event_2, circle: @political_party_level_circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event_3, circle: @political_party_level_circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @state_level_circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @political_leader_level_circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @political_party_level_circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                          photo_v2: @admin_medium_2, primary: true)
        FactoryBot.create(:poster_creative, event: @event_1, photo_v3: @admin_medium_1,
                          photo_v2: @admin_medium_2, primary: true)
        FactoryBot.create(:poster_creative, event: @event_2, photo_v3: @admin_medium_1,
                          photo_v2: @admin_medium_2, primary: true)
        FactoryBot.create(:poster_creative, event: @event_3, photo_v3: @admin_medium_1,
                          photo_v2: @admin_medium_2, primary: true)

        @events_section = @user.get_events_section(@user.circles, @user.is_eligible_for_premium_creatives?)
        expect(@events_section).not_to be_nil
        categories = @events_section[:categories]
        expect(categories.length).to eq(4)
        expect(categories.first[:params][:id]).to eq("#{@event_3.id}")
        expect(categories.second[:params][:id]).to eq("#{@event_2.id}")
        expect(categories.third[:params][:id]).to eq("#{@event_1.id}")
        expect(categories.last[:params][:id]).to eq("#{@event.id}")
      end

      it "should return an event with two image urls if the event has two poster creatives" do
        @event = FactoryBot.create(:event)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false)
        @events_section = @user.get_events_section(@user.circles, @user.is_eligible_for_premium_creatives?)

        categories = @events_section[:categories]
        expect(categories.first[:title]).to eq(@event.name)
        expect(categories.first[:params][:id]).to eq("#{@event.id}")
        expect(categories.first[:image_urls][0]).to eq(@poster_creative.photo_v2.url)
        expect(categories.first[:image_urls][1]).to eq(@poster_creative_1.photo_v2.url)
      end

      it "should not return an event if it has only premium poster creatives for a normal user" do
        @event = FactoryBot.create(:event)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @free_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                           photo_v2: @admin_medium_2, primary: true, paid: false)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false, paid: true)
        @free_creative.destroy
        @events_section = @user.get_events_section(@user.circles, @user.is_eligible_for_premium_creatives?)
        expect(@events_section).to be_nil
      end

    end
  end

  describe "#get_creative_kinds_sections" do
    context "get the data of creative kinds section" do
      it "should return the data of creative kinds section of schemes of normal user" do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                             photo_v2: @admin_medium_2, primary: true, creative_kind: :schemes)
        @poster_creative_1 = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                               photo_v2: @admin_medium_2, primary: false, creative_kind: :schemes)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative,
                          circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_1,
                          circle: @circle)

        @creative_kind_sections = @user.get_creative_kinds_sections(@user.circles, @user.has_premium_layout?)
        expect(@creative_kind_sections).not_to be_nil
        expect(@creative_kind_sections.first[:title]).to eq(PosterCreative.get_creative_kind_verbose("schemes"))
        categories = @creative_kind_sections.first[:categories]
        expect(categories.length).to eq(1)
        expect(categories.first[:title]).to eq(@circle.name)
        expect(categories.first[:image_urls][0]).to eq(@poster_creative.photo_v2.url)
        expect(categories.first[:image_urls][1]).to eq(@poster_creative_1.photo_v2.url)
      end

      it "should return the data of creative kinds section of schemes of premium user" do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                             photo_v2: @admin_medium_2, primary: true, creative_kind: :schemes)
        @poster_creative_1 = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                               photo_v2: @admin_medium_2, primary: false, creative_kind: :schemes)
        @poster_creative_2 = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                               photo_v2: @admin_medium_2, primary: false, creative_kind: :schemes,
                                               paid: true)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative,
                          circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_1,
                          circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_2,
                          circle: @circle)

        @creative_kind_sections = @user.get_creative_kinds_sections(@user.circles, true)
        expect(@creative_kind_sections).not_to be_nil
        expect(@creative_kind_sections.first[:title]).to eq(PosterCreative.get_creative_kind_verbose("schemes"))
        categories = @creative_kind_sections.first[:categories]
        expect(categories.length).to eq(1)
        expect(categories.first[:title]).to eq(@circle.name)
        expect(categories.first[:image_urls][0]).to eq(@poster_creative.photo_v2.url)
        expect(categories.first[:image_urls][1]).to eq(@poster_creative_1.photo_v2.url)
        expect(categories.first[:image_urls][2]).to eq(@poster_creative_2.photo_v2.url)
      end

      it "should return the data of creative kinds section of schemes of normal user where it returns only free poster
          creatives" do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                             photo_v2: @admin_medium_2, primary: true, creative_kind: :schemes)
        @poster_creative_1 = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                               photo_v2: @admin_medium_2, primary: false, creative_kind: :schemes)
        @poster_creative_2 = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                               photo_v2: @admin_medium_2, primary: false, creative_kind: :schemes,
                                               paid: true)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative,
                          circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_1,
                          circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_2,
                          circle: @circle)

        @creative_kind_sections = @user.get_creative_kinds_sections(@user.circles, false)
        expect(@creative_kind_sections).not_to be_nil
        expect(@creative_kind_sections.first[:title]).to eq(PosterCreative.get_creative_kind_verbose("schemes"))
        categories = @creative_kind_sections.first[:categories]
        expect(categories.length).to eq(1)
        expect(categories.first[:title]).to eq(@circle.name)
        expect(categories.first[:image_urls][0]).to eq(@poster_creative.photo_v2.url)
        expect(categories.first[:image_urls][1]).to eq(@poster_creative_1.photo_v2.url)
      end

      it "should return category kinds sections data in order" do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                             photo_v2: @admin_medium_2, primary: true, creative_kind: :schemes)
        @poster_creative_1 = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                               photo_v2: @admin_medium_2, primary: false, creative_kind: :info)
        @poster_creative_2 = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                               photo_v2: @admin_medium_2, primary: false, creative_kind: :quotes)
        @poster_creative_3 = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                               photo_v2: @admin_medium_2, primary: false, creative_kind: :wishes)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative,
                          circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_1,
                          circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_2,
                          circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_3,
                          circle: @circle)

        @creative_kind_sections = @user.get_creative_kinds_sections(@user.circles, @user.has_premium_layout?)
        expect(@creative_kind_sections).not_to be_nil
        expect(@creative_kind_sections.first[:title]).to eq(PosterCreative.get_creative_kind_verbose("info"))
        expect(@creative_kind_sections.second[:title]).to eq(PosterCreative.get_creative_kind_verbose("schemes"))
        expect(@creative_kind_sections.third[:title]).to eq(PosterCreative.get_creative_kind_verbose("quotes"))
      end
    end
  end

  describe "#get_poster_creatives" do
    context "get poster creatives and layouts of an event of normal user" do
      before :each do
        @user = FactoryBot.create(:user)
      end
      it "should return the poster creatives which are in current time range of an event" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false, paid: true)
        @poster_creatives = @user.get_poster_creatives(@event.id)
        is_layout_locked = !@user.is_poster_subscribed
        expect(@poster_creatives).not_to be_nil
        expect(@poster_creatives.length).to eq(1)
        expect(@poster_creatives.first[:v2_url]).to eq(@poster_creative.photo_v2.url)
        expect(@poster_creatives.first[:v1_url]).to eq(@poster_creative.photo_v2.url)
        expect(@poster_creatives.first[:paid]).to eq(@poster_creative.paid)
        expect(@poster_creatives.first[:h1_background_type]).to eq(@poster_creative.h1_leader_photo_ring_type)
        expect(@poster_creatives.first[:h2_background_type]).to eq(@poster_creative.h2_leader_photo_ring_type)
        expect(@poster_creatives.first[:is_locked]).to eq(is_layout_locked && @poster_creative.paid)
      end

      it "should return the poster creatives in the order of primary and latest to old" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: true)
        @poster_creative_2 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false)
        @poster_creative_3 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false, paid: true)
        @poster_creatives = @user.get_poster_creatives(@event.id)
        expect(@poster_creatives).not_to be_nil
        expect(@poster_creatives.length).to eq(3)
        expect(@poster_creatives.first[:v2_url]).to eq(@poster_creative_1.photo_v2.url)
        expect(@poster_creatives.second[:v2_url]).to eq(@poster_creative.photo_v2.url)
        expect(@poster_creatives.third[:v2_url]).to eq(@poster_creative_2.photo_v2.url)
      end

      it "should not return the poster creatives which are not in current time range of an event" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now + 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true,
                                             start_time: Time.zone.now - 2.hours, end_time: Time.zone.now - 1.hour)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false, paid: true,
                                               start_time: Time.zone.now - 2.hours, end_time: Time.zone.now - 1.hour)
        @poster_creative_2 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false, paid: true, active: false,
                                               start_time: Time.zone.now - 2.hours, end_time: Time.zone.now - 1.hour)
        poster_creatives = @user.get_poster_creatives(@event.id)
        expect(poster_creatives).not_to be_nil
        expect(poster_creatives.length).to eq(0)
      end

      it "should return the poster creatives which are in current time range of a category kind" do
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true, creative_kind: :schemes)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false, paid: true)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative,
                          circle: @circle)
        @poster_creatives = @user.get_poster_creatives(nil, "schemes", "#{@circle.id}")
        is_layout_locked = !@user.is_poster_subscribed
        expect(@poster_creatives).not_to be_nil
        expect(@poster_creatives.length).to eq(1)
        expect(@poster_creatives.first[:v2_url]).to eq(@poster_creative.photo_v2.url)
        expect(@poster_creatives.first[:v1_url]).to eq(@poster_creative.photo_v2.url)
        expect(@poster_creatives.first[:paid]).to eq(@poster_creative.paid)
        expect(@poster_creatives.first[:h1_background_type]).to eq(@poster_creative.h1_leader_photo_ring_type)
        expect(@poster_creatives.first[:h2_background_type]).to eq(@poster_creative.h2_leader_photo_ring_type)
        expect(@poster_creatives.first[:is_locked]).to eq(is_layout_locked && @poster_creative.paid)
      end

      it "should return the poster creatives in the order of primary and latest to old of category kind" do
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: true)
        @poster_creative_2 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false)
        @poster_creative_3 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false, paid: true)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_1, circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_2, circle: @circle)
        @poster_creatives = @user.get_poster_creatives(nil, "schemes", "#{@circle.id}")
        expect(@poster_creatives).not_to be_nil
        expect(@poster_creatives.length).to eq(3)
        expect(@poster_creatives.first[:v2_url]).to eq(@poster_creative_1.photo_v2.url)
        expect(@poster_creatives.second[:v2_url]).to eq(@poster_creative.photo_v2.url)
        expect(@poster_creatives.third[:v2_url]).to eq(@poster_creative_2.photo_v2.url)
      end

      it "should not return the poster creatives which are not in current time range of a category kind" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now + 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true, creative_kind: :schemes,
                                             start_time: Time.zone.now - 2.hours, end_time: Time.zone.now - 1.hour)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false, paid: true,
                                               start_time: Time.zone.now - 2.hours, end_time: Time.zone.now - 1.hour)
        @poster_creative_2 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false, paid: true, active: false,
                                               start_time: Time.zone.now - 2.hours, end_time: Time.zone.now - 1.hour)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative,
                          circle: @circle)
        @poster_creatives = @user.get_poster_creatives(nil, "schemes", "#{@circle.id}")
        expect(@poster_creatives).not_to be_nil
        expect(@poster_creatives.length).to eq(0)
      end
    end
    context "get poster creatives and layouts of an event of poster layout data user" do
      before :each do
        @user = FactoryBot.create(:user)
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user = FactoryBot.create(:user)
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user, h1_count: 1, h2_count: 1)
        FactoryBot.create(:user_leader_photo, user: @user, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                          header_type: :header_1, priority: 1, user_poster_layout: @user_poster_layout)
        FactoryBot.create(:user_leader_photo, user: @user, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                          header_type: :header_2, priority: 1, user_poster_layout: @user_poster_layout)
      end
      it "should return the poster creatives which are in current time range of an event" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false)
        @poster_creatives = @user.get_poster_creatives(@event.id)
        is_layout_locked = !@user.is_poster_subscribed
        expect(@poster_creatives).not_to be_nil
        expect(@poster_creatives.length).to eq(2)
        expect(@poster_creatives.first[:v2_url]).to eq(@poster_creative.photo_v2.url)
        expect(@poster_creatives.first[:v1_url]).to eq(@poster_creative.photo_v2.url)
        expect(@poster_creatives.first[:paid]).to eq(@poster_creative.paid)
        expect(@poster_creatives.first[:h1_background_type]).to eq(@poster_creative.h1_leader_photo_ring_type)
        expect(@poster_creatives.first[:h2_background_type]).to eq(@poster_creative.h2_leader_photo_ring_type)
        expect(@poster_creatives.first[:is_locked]).to eq(is_layout_locked && @poster_creative.paid)
      end

      it "should return the poster creatives in the order of primary and latest to old" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: true)
        @poster_creative_2 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false)
        @poster_creative_3 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false)
        @poster_creatives = @user.get_poster_creatives(@event.id)
        expect(@poster_creatives).not_to be_nil
        expect(@poster_creatives.length).to eq(4)
        expect(@poster_creatives.first[:v2_url]).to eq(@poster_creative_1.photo_v2.url)
        expect(@poster_creatives.second[:v2_url]).to eq(@poster_creative.photo_v2.url)
        expect(@poster_creatives.third[:v2_url]).to eq(@poster_creative_2.photo_v2.url)
      end

      it "should not return the poster creatives which are not in current time range of an event" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now + 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true,
                                             start_time: Time.zone.now - 2.hours, end_time: Time.zone.now - 1.hour)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false, paid: true,
                                               start_time: Time.zone.now - 2.hours, end_time: Time.zone.now - 1.hour)
        @poster_creative_2 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false, paid: true, active: false,
                                               start_time: Time.zone.now - 2.hours, end_time: Time.zone.now - 1.hour)
        @poster_creatives = @user.get_poster_creatives(@event.id)
        expect(@poster_creatives).not_to be_nil
        expect(@poster_creatives.length).to eq(0)
      end

      it "should return the poster creatives which are in current time range of a category kind" do
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true, creative_kind: :schemes)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_1, circle: @circle)
        @poster_creatives = @user.get_poster_creatives(nil, "schemes", "#{@circle.id}")
        is_layout_locked = !@user.is_poster_subscribed
        expect(@poster_creatives).not_to be_nil
        expect(@poster_creatives.length).to eq(2)
        expect(@poster_creatives.first[:v2_url]).to eq(@poster_creative.photo_v2.url)
        expect(@poster_creatives.first[:v1_url]).to eq(@poster_creative.photo_v2.url)
        expect(@poster_creatives.first[:paid]).to eq(@poster_creative.paid)
        expect(@poster_creatives.first[:h1_background_type]).to eq(@poster_creative.h1_leader_photo_ring_type)
        expect(@poster_creatives.first[:h2_background_type]).to eq(@poster_creative.h2_leader_photo_ring_type)
        expect(@poster_creatives.first[:is_locked]).to eq(is_layout_locked && @poster_creative.paid)
      end

      it "should return the poster creatives in the order of primary and latest to old of category kind" do
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: true)
        @poster_creative_2 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false)
        @poster_creative_3 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_1, circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_2, circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_3, circle: @circle)
        @poster_creatives = @user.get_poster_creatives(nil, "schemes", "#{@circle.id}")
        expect(@poster_creatives).not_to be_nil
        expect(@poster_creatives.length).to eq(4)
        expect(@poster_creatives.first[:v2_url]).to eq(@poster_creative_1.photo_v2.url)
        expect(@poster_creatives.second[:v2_url]).to eq(@poster_creative.photo_v2.url)
        expect(@poster_creatives.third[:v2_url]).to eq(@poster_creative_2.photo_v2.url)
      end

      it "should not return the poster creatives which are not in current time range of a category kind" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now + 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true, creative_kind: :schemes,
                                             start_time: Time.zone.now - 2.hours, end_time: Time.zone.now - 1.hour)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false, creative_kind: :schemes,
                                               start_time: Time.zone.now - 2.hours, end_time: Time.zone.now - 1.hour,
                                               paid: true)
        @poster_creative_2 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false, creative_kind: :schemes,
                                               start_time: Time.zone.now - 2.hours, end_time: Time.zone.now - 1.hour,
                                               paid: true, active: false)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_1, circle: @circle)
        @poster_creatives = @user.get_poster_creatives(nil, "schemes", "#{@circle.id}")
        expect(@poster_creatives).not_to be_nil
        expect(@poster_creatives.length).to eq(0)
      end
    end
    context "get poster creatives and layouts of an event for user poster layout id present" do
      before :each do
        @user = FactoryBot.create(:user)
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user = FactoryBot.create(:user)
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user, h1_count: 1, h2_count: 1, active: false)
        FactoryBot.create(:user_leader_photo, user: @user, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                          header_type: :header_1, priority: 1, user_poster_layout: @user_poster_layout)
        FactoryBot.create(:user_leader_photo, user: @user, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                          header_type: :header_2, priority: 1, user_poster_layout: @user_poster_layout)
      end
      it "should return the poster creatives which are in current time range of an event" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false)
        @poster_creatives = @user.get_poster_creatives(@event.id, nil, nil)
        is_layout_locked = !@user.is_poster_subscribed
        expect(@poster_creatives).not_to be_nil
        expect(@poster_creatives.length).to eq(2)
        expect(@poster_creatives.first[:v2_url]).to eq(@poster_creative.photo_v2.url)
        expect(@poster_creatives.first[:v1_url]).to eq(@poster_creative.photo_v2.url)
        expect(@poster_creatives.first[:paid]).to eq(@poster_creative.paid)
        expect(@poster_creatives.first[:h1_background_type]).to eq(@poster_creative.h1_leader_photo_ring_type)
        expect(@poster_creatives.first[:h2_background_type]).to eq(@poster_creative.h2_leader_photo_ring_type)
        expect(@poster_creatives.first[:is_locked]).to eq(is_layout_locked && @poster_creative.paid)
      end

      it "should return the poster creatives in the order of primary and latest to old" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: true)
        @poster_creative_2 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false)
        @poster_creative_3 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false)
        @poster_creatives = @user.get_poster_creatives(@event.id, nil, nil)
        expect(@poster_creatives).not_to be_nil
        expect(@poster_creatives.length).to eq(4)
        expect(@poster_creatives.first[:v2_url]).to eq(@poster_creative_1.photo_v2.url)
        expect(@poster_creatives.second[:v2_url]).to eq(@poster_creative.photo_v2.url)
        expect(@poster_creatives.third[:v2_url]).to eq(@poster_creative_2.photo_v2.url)
      end

      it "should not return the poster creatives which are not in current time range of an event" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now + 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true,
                                             start_time: Time.zone.now - 2.hours, end_time: Time.zone.now - 1.hour)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false, paid: true,
                                               start_time: Time.zone.now - 2.hours, end_time: Time.zone.now - 1.hour)
        @poster_creative_2 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false, paid: true, active: false,
                                               start_time: Time.zone.now - 2.hours, end_time: Time.zone.now - 1.hour)
        @poster_creatives = @user.get_poster_creatives(@event.id, nil, nil)
        expect(@poster_creatives).not_to be_nil
        expect(@poster_creatives.length).to eq(0)
      end

      it "should return the poster creatives which are in current time range of a category kind" do
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true, creative_kind: :schemes)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_1, circle: @circle)
        @poster_creatives = @user.get_poster_creatives(nil, "schemes", "#{@circle.id}")
        is_layout_locked = !@user.is_poster_subscribed
        expect(@poster_creatives).not_to be_nil
        expect(@poster_creatives.length).to eq(2)
        expect(@poster_creatives.first[:v2_url]).to eq(@poster_creative.photo_v2.url)
        expect(@poster_creatives.first[:v1_url]).to eq(@poster_creative.photo_v2.url)
        expect(@poster_creatives.first[:paid]).to eq(@poster_creative.paid)
        expect(@poster_creatives.first[:h1_background_type]).to eq(@poster_creative.h1_leader_photo_ring_type)
        expect(@poster_creatives.first[:h2_background_type]).to eq(@poster_creative.h2_leader_photo_ring_type)
        expect(@poster_creatives.first[:is_locked]).to eq(is_layout_locked && @poster_creative.paid)
      end

      it "should return the poster creatives in the order of primary and latest to old of category kind" do
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: true)
        @poster_creative_2 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false)
        @poster_creative_3 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_1, circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_2, circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_3, circle: @circle)
        @poster_creatives = @user.get_poster_creatives(nil, "schemes", "#{@circle.id}")
        expect(@poster_creatives).not_to be_nil
        expect(@poster_creatives.length).to eq(4)
        expect(@poster_creatives.first[:v2_url]).to eq(@poster_creative_1.photo_v2.url)
        expect(@poster_creatives.second[:v2_url]).to eq(@poster_creative.photo_v2.url)
        expect(@poster_creatives.third[:v2_url]).to eq(@poster_creative_2.photo_v2.url)
      end

      it "should not return the poster creatives which are not in current time range of a category kind" do
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true, creative_kind: :schemes,
                                             start_time: Time.zone.now - 2.hours, end_time: Time.zone.now - 1.hour)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: true, creative_kind: :schemes,
                                               start_time: Time.zone.now - 2.hours, end_time: Time.zone.now - 1.hour,
                                               paid: true)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: true, creative_kind: :schemes,
                                               start_time: Time.zone.now - 2.hours, end_time: Time.zone.now - 1.hour,
                                               paid: true, active: false)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_1, circle: @circle)
        @poster_creatives = @user.get_poster_creatives(nil, "schemes", "#{@circle.id}")
        expect(@poster_creatives).not_to be_nil
        expect(@poster_creatives.length).to eq(0)
      end
    end
  end

  describe "#get_user_layouts for posters tab v1" do
    context "when user is not subscribed but has poster layouts data" do
      before :each do
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user = FactoryBot.create(:user, id: 100, name: "testing")
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)
        AppVersionSupport.new('2408.05.00')

        @user_poster_layout =
          FactoryBot.create(:user_poster_layout, entity: @user,
                            h1_count: 1,
                            h2_count: 1,
                            user_leader_photos: [
                              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                header_type: :header_1, priority: 1),
                              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                header_type: :header_2, priority: 1)])
      end
      it "should return only basic frames if no frames are given" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)

        @user_layouts = @user.get_user_layouts(category_id: @event.id)
        expect(@user_layouts).not_to be_nil
        expect(@user_layouts.length).to eq(3)
      end

      it "should return the user frames if frames are given" do
        # for party icon url testing
        @admin_user = FactoryBot.create(:admin_user)
        data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: @admin_user.id)
        @badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :WHITE)
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = @badge_icon.badge_icon_group.circle
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        @user.update(affiliated_party_circle_id: @circle.id)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @frame1 = FactoryBot.create(:frame, identifier: :party_curved_identity, frame_type: :premium, gold_border: false,
                                    has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                    identity_type: :curved)
        @frame2 = FactoryBot.create(:frame, identifier: :party_neon_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: true, is_neutral_frame: false,
                                    has_footer_party_icon: true, identity_type: :flat_user)
        @frame3 = FactoryBot.create(:frame, identifier: :gold_white_flat_identity, frame_type: :status,
                                    gold_border: true, has_shadow_color: false, is_neutral_frame: false,
                                    has_footer_party_icon: true, identity_type: :gold_lettered_user)
        @frame4 = FactoryBot.create(:frame, identifier: :neutral_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: false, is_neutral_frame: true,
                                    has_footer_party_icon: false, identity_type: :flat_user)

        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame1.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame2.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame3.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame4.id)

        # the date is of 263rd day of the year
        travel_to(Time.parse("2023-09-20")) do
          @user_layouts = @user.get_user_layouts(category_id: @event.id)
          expect(@user_layouts).not_to be_nil
          expect(@user_layouts.length).to eq(7)
          expect(@user_layouts[0][:identity][:type]).to eq("gold_lettered_user")
          expect(@user_layouts[4][:identity][:type]).to eq("curved")
          expect(@user_layouts[5][:identity][:type]).to eq("flat_user")
          expect(@user_layouts[6][:identity][:type]).to eq("flat_user")
          expect(@user_layouts[1][:layout_type]).to eq("basic")
          expect(@user_layouts[1][:share_text]).not_to be_nil
          expect(@user_layouts[1][:share_text].strip).not_to be_empty
          expect(@user_layouts[1][:identity][:type]).to eq("flat_user")
          expect(@user_layouts[2][:layout_type]).to eq("basic")
          expect(@user_layouts[2][:identity][:type]).to eq("basic_transparent_identity")
          expect(@user_layouts[2][:share_text]).not_to be_nil
          expect(@user_layouts[2][:share_text].strip).not_to be_empty
          expect(@user_layouts[3][:layout_type]).to eq("basic")
          expect(@user_layouts[3][:identity][:type]).to eq("basic_no_profile_pic_identity")
          expect(@user_layouts[3][:share_text]).not_to be_nil
          expect(@user_layouts[3][:share_text].strip).not_to be_empty
          expect(@user_layouts[0][:text_color]).to eq(nil)
          # check header_1_photos and header_2_photos for each frame
          expect(@user_layouts[0][:header_1_photos].length).to eq(1)
          expect(@user_layouts[0][:header_2_photos].length).to eq(1)
          expect(@user_layouts[4][:header_1_photos].length).to eq(1)
          expect(@user_layouts[4][:header_2_photos].length).to eq(1)
          expect(@user_layouts[5][:header_1_photos].length).to eq(1)
          expect(@user_layouts[5][:header_2_photos].length).to eq(1)
          expect(@user_layouts[6][:header_1_photos].length).to eq(0)
          expect(@user_layouts[6][:header_2_photos].length).to eq(0)

          # check party icon url
          expect(@user_layouts[0][:identity][:party_icon_url]).to eq(@user.get_affiliated_party_icon(@circle.id))
        end
      end
    end

    context "when user is subscribed and has poster layouts data" do
      before :each do
        AppVersionSupport.new('2408.05.00')
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user = FactoryBot.create(:user, id: 100, name: "testing")
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 1,
                                                h2_count: 1,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                                    header_type: :header_2, priority: 1)])

        @time = Time.parse("2023-09-20")
        FactoryBot.create(:user_plan, user_id: @user.id, end_date: @time + 1.month)

        @frame1 = FactoryBot.create(:frame, identifier: :party_curved_identity, frame_type: :premium, gold_border: false,
                                    has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                    identity_type: :curved)
        @frame2 = FactoryBot.create(:frame, identifier: :party_neon_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: true, is_neutral_frame: false,
                                    has_footer_party_icon: true, identity_type: :flat_user)
        @frame3 = FactoryBot.create(:frame, identifier: :party_flat_circle_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: false, is_neutral_frame: false,
                                    has_footer_party_icon: false, identity_type: :flat_user_badge_circle)

        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame1.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame2.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame3.id)
      end
      it "should return the user layouts" do
        # for party icon url testing
        @admin_user = FactoryBot.create(:admin_user)
        data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: @admin_user.id)
        @badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :WHITE)
        @event = FactoryBot.create(:event, start_time: @time - 1.hour, end_time: @time + 2.hours)
        @circle = @badge_icon.badge_icon_group.circle
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user.update(affiliated_party_circle_id: @circle.id)
        @role = FactoryBot.create(:role,
                                  name: "testing",
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :absolute,
                                  quota_value: 2,
                                  grade_level: :grade_1,
                                  parent_circle_level: :private,
                                  active: true)
        @private_circle = FactoryBot.create(:circle, level: :private, circle_type: :my_circle)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @private_circle.id)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        @user.affiliated_party_circle_id = @circle.id
        @user.save

        travel_to(@time) do
          @user_layouts = @user.get_user_layouts(category_id: @event.id)
          expect(@user_layouts).not_to be_nil
          expect(@user_layouts.length).to eq(3)
          expect(@user_layouts[0][:identity][:type]).to eq("curved")
          expect(@user_layouts[1][:identity][:type]).to eq("flat_user")
          expect(@user_layouts[2][:identity][:type]).to eq("flat_user_badge_circle")

          expect(@user_layouts[0][:identity][:user][:photo_url]).to eq(@user.poster_photo_with_background.url)
          # for party icon
          expect(@user_layouts[2][:identity][:party_icon_url]).to eq(nil)
        end
      end
    end

    context "when user is not subscribed and does not have poster layouts data" do
      before :each do
        @user = FactoryBot.create(:user, id: 100)
        AppVersionSupport.new('2408.05.00')
      end
      it "should return the following user layouts when user has affiliated circle id" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        @user.affiliated_party_circle_id = @circle.id
        @user.save
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)

        @user_layouts = @user.get_user_layouts(category_id: @event.id)
        expect(@user_layouts).not_to be_nil
        expect(@user_layouts.length).to eq(3)
      end
    end

    context "when user poster layout is present and need to check preview of it" do
      before :each do
        AppVersionSupport.new('2408.05.00')
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user = FactoryBot.create(:user, id: 100)
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 1,
                                                h2_count: 1,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                                    header_type: :header_2, priority: 1)])
      end
      it "should return the user layouts" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)

        @frame1 = FactoryBot.create(:frame, identifier: :party_curved_identity, frame_type: :premium, gold_border: false,
                                    has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                    identity_type: :curved)
        @frame2 = FactoryBot.create(:frame, identifier: :party_neon_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: true, is_neutral_frame: false,
                                    has_footer_party_icon: true, identity_type: :flat_user)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame1.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame2.id)

        travel_to(Time.parse("2023-09-20")) do
          @user_layouts = @user.get_user_layouts(category_id: @event.id, user_poster_layout_id: @user_poster_layout.id,
                                                 is_from_edit_layouts: true)
          expect(@user_layouts).not_to be_nil
          expect(@user_layouts.length).to eq(2)
          expect(@user_layouts[0][:identity][:type]).to eq("flat_user")
          expect(@user_layouts[1][:identity][:type]).to eq("curved")
        end
      end
    end

    context "rotate the user poster layouts based on the no of posters and current day" do
      before :each do
        AppVersionSupport.new('2408.05.00')
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user = FactoryBot.create(:user, id: 100)
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 1,
                                                h2_count: 1,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                                    header_type: :header_2, priority: 1)])

        @frame1 = FactoryBot.create(:frame, identifier: :party_curved_identity, frame_type: :premium, gold_border: false,
                                    has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                    identity_type: :curved)
        @frame2 = FactoryBot.create(:frame, identifier: :party_neon_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: true, is_neutral_frame: false,
                                    has_footer_party_icon: true)
        @frame3 = FactoryBot.create(:frame, identifier: :gold_white_flat_identity, frame_type: :status,
                                    gold_border: true, has_shadow_color: false, is_neutral_frame: false,
                                    has_footer_party_icon: true, identity_type: :flat_user_badge_circle)
        @frame4 = FactoryBot.create(:frame, identifier: :neutral_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: false, is_neutral_frame: true,
                                    has_footer_party_icon: false, identity_type: :flat_user)

        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame1.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame2.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame3.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame4.id)
      end

      it "check the rotation where basic at second and neutral premium poster at last for not yet paid user" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        @user.affiliated_party_circle_id = @circle.id
        @user.save

        # 3 premium layouts and 2 basic layouts
        # premium layouts will be shifted based on the current day
        travel_to(Time.parse("2023-08-28")) do
          user_layouts = @user.get_user_layouts(category_id: @event.id)
          expect(user_layouts[0][:identity][:type]).to eq("curved")
          expect(user_layouts[1][:layout_type]).to eq("basic")
          expect(user_layouts[2][:identity][:type]).to eq("basic_transparent_identity")
          expect(user_layouts[3][:identity][:type]).to eq("basic_no_profile_pic_identity")
          expect(user_layouts[4][:identity][:type]).to eq("flat_user_badge_circle")
          expect(user_layouts[5][:identity][:type]).to eq("curved_with_depth")
          expect(user_layouts[6][:identity][:type]).to eq("flat_user")
        end

        travel_to(Time.parse("2023-08-29")) do
          user_layouts = @user.get_user_layouts(category_id: @event.id)
          expect(user_layouts[0][:identity][:type]).to eq("curved_with_depth")
          expect(user_layouts[4][:identity][:type]).to eq("flat_user_badge_circle")
          expect(user_layouts[5][:identity][:type]).to eq("curved")
          expect(user_layouts[6][:identity][:type]).to eq("flat_user")
          expect(user_layouts[1][:layout_type]).to eq("basic")
          expect(user_layouts[2][:identity][:type]).to eq("basic_transparent_identity")
          expect(user_layouts[3][:identity][:type]).to eq("basic_no_profile_pic_identity")
        end

        travel_to(Time.parse("2023-08-30")) do
          user_layouts = @user.get_user_layouts(category_id: @event.id)
          expect(user_layouts[0][:identity][:type]).to eq("flat_user_badge_circle")
          expect(user_layouts[4][:identity][:type]).to eq("curved")
          expect(user_layouts[5][:identity][:type]).to eq("curved_with_depth")
          expect(user_layouts[6][:identity][:type]).to eq("flat_user")
          expect(user_layouts[1][:layout_type]).to eq("basic")
          expect(user_layouts[2][:identity][:type]).to eq("basic_transparent_identity")
          expect(user_layouts[3][:identity][:type]).to eq("basic_no_profile_pic_identity")
        end

        travel_to(Time.parse("2023-08-31")) do
          user_layouts = @user.get_user_layouts(category_id: @event.id)
          expect(user_layouts[0][:identity][:type]).to eq("curved")
          expect(user_layouts[4][:identity][:type]).to eq("flat_user_badge_circle")
          expect(user_layouts[5][:identity][:type]).to eq("curved_with_depth")
          expect(user_layouts[6][:identity][:type]).to eq("flat_user")
          expect(user_layouts[1][:layout_type]).to eq("basic")
          expect(user_layouts[2][:identity][:type]).to eq("basic_transparent_identity")
          expect(user_layouts[3][:identity][:type]).to eq("basic_no_profile_pic_identity")
        end
      end

      it "check the rotation where neutral posters at last for paid user" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        @user.affiliated_party_circle_id = @circle.id
        @user.save

        FactoryBot.create(:user_plan, user_id: @user.id, end_date: Time.parse("2023-09-01"))

        # 3 premium layouts and 1 basic layout
        # premium layouts will be shifted based on the current day
        # basic layouts will be at last
        @user.reload
        @is_poster_subscribed = @user.is_poster_subscribed
        travel_to(Time.parse("2023-08-28")) do
          user_layouts = @user.get_user_layouts(category_id: @event.id)
          expect(user_layouts[0][:identity][:type]).to eq("curved")
          expect(user_layouts[1][:identity][:type]).to eq("flat_user_badge_circle")
          expect(user_layouts[2][:identity][:type]).to eq("curved_with_depth")
          expect(user_layouts[3][:identity][:type]).to eq("flat_user")
        end

        travel_to(Time.parse("2023-08-29")) do
          user_layouts = @user.get_user_layouts(category_id: @event.id)
          expect(user_layouts[0][:identity][:type]).to eq("curved_with_depth")
          expect(user_layouts[1][:identity][:type]).to eq("flat_user_badge_circle")
          expect(user_layouts[2][:identity][:type]).to eq("curved")
          expect(user_layouts[3][:identity][:type]).to eq("flat_user")
        end

        travel_to(Time.parse("2023-08-30")) do
          user_layouts = @user.get_user_layouts(category_id: @event.id)
          expect(user_layouts[0][:identity][:type]).to eq("flat_user_badge_circle")
          expect(user_layouts[1][:identity][:type]).to eq("curved")
          expect(user_layouts[2][:identity][:type]).to eq("curved_with_depth")
          expect(user_layouts[3][:identity][:type]).to eq("flat_user")
        end

        travel_to(Time.parse("2023-08-31")) do
          user_layouts = @user.get_user_layouts(category_id: @event.id)
          expect(user_layouts[0][:identity][:type]).to eq("curved")
          expect(user_layouts[1][:identity][:type]).to eq("flat_user_badge_circle")
          expect(user_layouts[2][:identity][:type]).to eq("curved_with_depth")
          expect(user_layouts[3][:identity][:type]).to eq("flat_user")
        end
      end
    end
  end

  describe "#get_user_layouts for posters tab v2" do
    context "when user is not subscribed but has poster layouts data" do
      before :each do
        @photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @image = fixture_file_upload("app/assets/images/logo.png", "image/png")
        @user = FactoryBot.create(:user, id: 100, name: "testing")
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: @photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: @photo)
        @user.family_frame_photo = FactoryBot.create(:admin_medium, blob_data: @photo)
        @user.family_frame_name = "Family Frame"
        @user.hero_frame_photo = FactoryBot.create(:admin_medium, blob_data: @photo)
        AppVersionSupport.new('2408.05.00')
        @user_poster_layout =
          FactoryBot.create(:user_poster_layout, entity: @user,
                            h1_count: 1,
                            h2_count: 1,
                            user_leader_photos: [
                              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                header_type: :header_1, priority: 1),
                              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                header_type: :header_2, priority: 1)])
      end
      it "should return only basic frames if no frames are given" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)

        @user_layouts = @user.get_user_layouts(category_id: @event.id)
        expect(@user_layouts).not_to be_nil
        expect(@user_layouts.length).to eq(3)
      end

      it "should return the user frames if frames are given" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @frame1 = FactoryBot.create(:frame, identifier: :party_curved_identity, frame_type: :premium, gold_border: false,
                                    has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                    identity_type: :curved)
        @frame2 = FactoryBot.create(:frame, identifier: :party_neon_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: true, is_neutral_frame: false,
                                    has_footer_party_icon: true, identity_type: :flat_user)
        @frame3 = FactoryBot.create(:frame, identifier: :gold_white_flat_identity, frame_type: :status,
                                    gold_border: true, has_shadow_color: false, is_neutral_frame: false,
                                    has_footer_party_icon: true, identity_type: :gold_lettered_user)
        @frame4 = FactoryBot.create(:frame, identifier: :neutral_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: false, is_neutral_frame: true,
                                    has_footer_party_icon: false, identity_type: :flat_user)

        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame1.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame2.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame3.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame4.id)
        time = Time.parse("2023-09-20")
        travel_to(time) do
          @user_layouts = @user.get_user_layouts(category_id: @event.id)
          expect(@user_layouts).not_to be_nil
          expect(@user_layouts.length).to eq(7)
          expect(@user_layouts[0][:identity][:type]).to eq("gold_lettered_user")
          expect(@user_layouts[4][:identity][:type]).to eq("curved")
          expect(@user_layouts[5][:identity][:type]).to eq("flat_user")
          expect(@user_layouts[6][:identity][:type]).to eq("flat_user")
          expect(@user_layouts[1][:layout_type]).to eq("basic")
          expect(@user_layouts[2][:identity][:type]).to eq("basic_transparent_identity")
          expect(@user_layouts[3][:identity][:type]).to eq("basic_no_profile_pic_identity")
          expect(@user_layouts[0][:text_color]).to eq(nil)
          # check header_1_photos and header_2_photos for each frame
          expect(@user_layouts[0][:header_1_photos].length).to eq(1)
          expect(@user_layouts[0][:header_2_photos].length).to eq(1)
          expect(@user_layouts[4][:header_1_photos].length).to eq(1)
          expect(@user_layouts[4][:header_2_photos].length).to eq(1)
          expect(@user_layouts[5][:header_1_photos].length).to eq(1)
          expect(@user_layouts[5][:header_2_photos].length).to eq(1)
          expect(@user_layouts[6][:header_1_photos].length).to eq(0)
          expect(@user_layouts[6][:header_2_photos].length).to eq(0)
        end
      end

      # it "return user and circle frames if circle frames and it is creative kind section" do
      #   allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 201, body: { success: true }.to_json))
      #
      #   @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
      #   @circle = FactoryBot.create(:circle, photo: FactoryBot.create(:photo))
      #   @circle.conversation_type = :channel
      #   @circle.save!
      #   @circle_package = FactoryBot.create(:circle_package)
      #   FactoryBot.create(:circle_package_mapping, circle_package: @circle_package, circle: @circle)
      #   FactoryBot.create(:circle_monthly_usage, circle: @circle)
      #   @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
      #   @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
      #   image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
      #   image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
      #   @admin_medium_1 = FactoryBot.create(:admin_medium, data: image_600x750)
      #   @admin_medium_2 = FactoryBot.create(:admin_medium, data: image_630x940)
      #   @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
      #                                        photo_v2: @admin_medium_2, primary: true, creative_kind: :info)
      #   @frame1 = FactoryBot.create(:frame, identifier: :party_curved_identity, frame_type: :premium, gold_border: false,
      #                               has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
      #                               identity_type: :curved)
      #   @frame2 = FactoryBot.create(:frame, identifier: :party_neon_flat_identity, frame_type: :premium,
      #                               gold_border: false, has_shadow_color: true, is_neutral_frame: false,
      #                               has_footer_party_icon: true, identity_type: :flat_user)
      #   @frame3 = FactoryBot.create(:frame, identifier: :gold_white_flat_identity, frame_type: :status,
      #                               gold_border: true, has_shadow_color: false, is_neutral_frame: false,
      #                               has_footer_party_icon: true, identity_type: :gold_lettered_user)
      #   @frame4 = FactoryBot.create(:frame, identifier: :neutral_flat_identity, frame_type: :premium,
      #                               gold_border: false, has_shadow_color: false, is_neutral_frame: true,
      #                               has_footer_party_icon: false, identity_type: :flat_user)
      #   # create circle layout
      #   @circle_poster_layout =
      #     UserPosterLayout.find_or_create_by(entity: @circle) do |upl|
      #       upl.h1_count = 1,
      #       upl.h2_count = 1,
      #       upl.user_leader_photos = [
      #         FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, data: @photo),
      #                           header_type: :header_1, priority: 1),
      #         FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, data: @photo),
      #                           header_type: :header_2, priority: 1)]
      #     end
      #   FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame1.id)
      #   FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame2.id)
      #   FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame3.id)
      #   FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame4.id)
      #   time = Time.parse("2023-09-20")
      #   travel_to(time) do
      #     @user_layouts = @user.get_user_layouts(circle_id: @circle.id, category_kind: "info")
      #     expect(@user_layouts).not_to be_nil
      #     expect(@user_layouts.length).to eq(8)
      #     expect(@user_layouts[0][:identity][:type]).to eq("flat_user")
      #     expect(@user_layouts[1][:identity][:type]).to eq("gold_lettered_user")
      #     expect(@user_layouts[2][:identity][:type]).to eq("flat_user")
      #     expect(@user_layouts[3][:identity][:type]).to eq("basic_transparent_identity")
      #     expect(@user_layouts[4][:identity][:type]).to eq("basic_no_profile_pic_identity")
      #     expect(@user_layouts[5][:identity][:type]).to eq("curved")
      #     expect(@user_layouts[6][:identity][:type]).to eq("flat_user")
      #     expect(@user_layouts[7][:identity][:type]).to eq("flat_user")
      #     expect(@user_layouts[1][:text_color]).to eq(nil)
      #     # check header_1_photos and header_2_photos for each frame
      #     expect(@user_layouts[0][:header_1_photos].length).to eq(0)
      #     expect(@user_layouts[0][:header_2_photos].length).to eq(0)
      #     expect(@user_layouts[1][:header_1_photos].length).to eq(1)
      #     expect(@user_layouts[1][:header_2_photos].length).to eq(1)
      #     expect(@user_layouts[5][:header_1_photos].length).to eq(1)
      #     expect(@user_layouts[5][:header_2_photos].length).to eq(1)
      #     expect(@user_layouts[6][:header_1_photos].length).to eq(1)
      #     expect(@user_layouts[6][:header_2_photos].length).to eq(1)
      #     expect(@user_layouts[7][:header_1_photos].length).to eq(0)
      #     expect(@user_layouts[7][:header_2_photos].length).to eq(0)
      #   end
      # end
    end

    context "when user is subscribed and has poster layouts data" do
      before :each do
        AppVersionSupport.new('2408.05.00')
        @photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user = FactoryBot.create(:user, id: 100, name: "testing")
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: @photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: @photo)
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 1,
                                                h2_count: 1,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1)])

        @time = Time.parse("2023-09-20")
        FactoryBot.create(:user_plan, user_id: @user.id, end_date: @time + 1.month)

        @frame1 = FactoryBot.create(:frame, identifier: :party_curved_identity, frame_type: :premium, gold_border: false,
                                    has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                    identity_type: :curved)
        @frame2 = FactoryBot.create(:frame, identifier: :party_neon_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: true, is_neutral_frame: false,
                                    has_footer_party_icon: true, identity_type: :flat_user)
        @frame3 = FactoryBot.create(:frame, identifier: :neutral_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: false, is_neutral_frame: true,
                                    has_footer_party_icon: false, identity_type: :flat_user)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame1.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame2.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame3.id)

      end
      it "should return the user layouts" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        @user.affiliated_party_circle_id = @circle.id
        @user.save

        travel_to(@time) do
          @user_layouts = @user.get_user_layouts(category_id: @event.id)
          expect(@user_layouts).not_to be_nil
          expect(@user_layouts.length).to eq(3)
          expect(@user_layouts[0][:identity][:type]).to eq("flat_user")
          expect(@user_layouts[1][:identity][:type]).to eq("curved")
          expect(@user_layouts[2][:identity][:type]).to eq("flat_user")

          expect(@user_layouts[0][:party_icon]).to eq(nil)
          expect(@user_layouts[2][:identity][:user][:photo_url]).to eq(@user.poster_photo_with_background.url)
        end
      end
    end

    context "when user is not subscribed and does not have poster layouts data" do
      before :each do
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('2408.05.00')
      end
      it "should return the following user layouts when user has affiliated circle id" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        @user.affiliated_party_circle_id = @circle.id
        @user.save
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)

        @user_layouts = @user.get_user_layouts(category_id: @event.id)
        expect(@user_layouts).not_to be_nil
        expect(@user_layouts.length).to eq(3)
      end
      # it "should return user layouts and circle layouts when user has affiliated circle id" do
      #   allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 201, body: { success: true }.to_json))
      #   @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
      #   @circle = FactoryBot.create(:circle, photo: FactoryBot.create(:photo))
      #   @circle.conversation_type = :channel
      #   @circle.save!
      #   @circle_package = FactoryBot.create(:circle_package)
      #   FactoryBot.create(:circle_package_mapping, circle_package: @circle_package, circle: @circle)
      #   FactoryBot.create(:circle_monthly_usage, circle: @circle)
      #   photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
      #   @user_poster_layout = UserPosterLayout.find_or_create_by(entity: @circle) do |upl|
      #     upl.h1_count = 1,
      #       upl.h2_count = 1,
      #       upl.user_leader_photos = [
      #         FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, data: photo),
      #                           header_type: :header_1, priority: 1),
      #         FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, data: photo),
      #                           header_type: :header_2, priority: 1)]
      #   end
      #   @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
      #   @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
      #   @user.affiliated_party_circle_id = @circle.id
      #   @user.save
      #   image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
      #   image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
      #   @admin_medium_1 = FactoryBot.create(:admin_medium, data: image_600x750)
      #   @admin_medium_2 = FactoryBot.create(:admin_medium, data: image_630x940)
      #   @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
      #                                        photo_v2: @admin_medium_2, primary: true, creative_kind: :info)
      #
      #   @user_layouts = @user.get_user_layouts(circle_id: @circle.id, category_kind: "info")
      #   expect(@user_layouts).not_to be_nil
      #   expect(@user_layouts.length).to eq(3)
      #   expect(@user_layouts[0][:identity][:type]).to eq("flat_user")
      #   expect(@user_layouts[0][:party_icon]).to eq(nil)
      #   expect(@user_layouts[0][:identity][:user][:photo_url]).to eq("https://az-cdn.thecircleapp.in/production/photos/41/08b3490dbb2b7f7e38f3036b92358fd8.jpg")
      #   expect(@user_layouts[1][:identity][:type]).to eq("basic_transparent_identity")
      #   expect(@user_layouts[2][:identity][:type]).to eq("basic_no_profile_pic_identity")
      #
      #   profile_photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
      #   @photo = FactoryBot.create(:photo, data: profile_photo, service: :aws)
      #   @user.photo_id = @photo.id
      #   @user.save
      #   @user_layouts = @user.get_user_layouts(nil, @circle.id, "info")
      #   expect(@user_layouts).not_to be_nil
      #   expect(@user_layouts.length).to eq(3)
      #   expect(@user_layouts[0][:identity][:type]).to eq("flat_user")
      #   expect(@user_layouts[0][:party_icon]).to eq(nil)
      #   expect(@user_layouts[0][:identity][:user][:photo_url]).to eq(@user.photo.url)
      #   expect(@user_layouts[1][:identity][:type]).to eq("basic_transparent_identity")
      #   expect(@user_layouts[2][:identity][:type]).to eq("basic_no_profile_pic_identity")
      #   # check circle layout sponsorship json
      #   expect(@user_layouts[0][:sponsorship]).not_to be_nil
      #   expect(@user_layouts[0][:sponsorship]).to eq(@circle.sponsorship_json)
      # end
    end

    context "when glassy frame is given for posters tab v2 supported users" do
      before :each do
        AppVersionSupport.new('2408.05.00')
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user = FactoryBot.create(:user, id: 100, name: "testing")
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 1,
                                                h2_count: 1,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                                    header_type: :header_2, priority: 1)])

        @time = Time.parse("2023-09-20")
        FactoryBot.create(:user_plan, user_id: @user.id, end_date: @time + 1.month)

        @frame1 = FactoryBot.create(:frame, identifier: :party_curved_identity, frame_type: :premium, gold_border: false,
                                    has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                    identity_type: :curved)
        @frame2 = FactoryBot.create(:frame, identifier: :party_neon_flat_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: true, is_neutral_frame: false,
                                    has_footer_party_icon: true, identity_type: :flat_user)
        @frame3 = FactoryBot.create(:frame, identifier: :party_flat_circle_identity, frame_type: :premium,
                                    gold_border: false, has_shadow_color: false, is_neutral_frame: false,
                                    has_footer_party_icon: false, identity_type: :flat_user_badge_circle)
        @frame4 = FactoryBot.create(:frame, identifier: :party_white_overlay_flat_identity, frame_type: :premium,
                                    identity_type: :glassy_user, gold_border: false, has_shadow_color: false,
                                    is_neutral_frame: false, has_footer_party_icon: false)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame1.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame2.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame3.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame4.id)
      end
      it "should include glassy frame in posters tab v2" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        @user.affiliated_party_circle_id = @circle.id
        @user.save

        travel_to(@time) do
          @user_layouts = @user.get_user_layouts(category_id: @event.id)
          expect(@user_layouts).not_to be_nil
          expect(@user_layouts.length).to eq(4)
          expect(@user_layouts[0][:identity][:type]).to eq("glassy_user")
          expect(@user_layouts[1][:identity][:type]).to eq("curved")
          expect(@user_layouts[2][:identity][:type]).to eq("flat_user")
          expect(@user_layouts[3][:identity][:type]).to eq("flat_user_badge_circle")

          expect(@user_layouts[3][:party_icon]).to eq(nil)
          expect(@user_layouts[3][:identity][:user][:photo_url]).to eq(@user.poster_photo_with_background.url)
        end
      end
    end
  end

  describe "#get_user_layouts for posters 3.0" do
    context "check logic of party icon or party tag based on has_footer_party_icon flag on frame" do
      before :each do
        AppVersionSupport.new('2408.05.00')
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        image = fixture_file_upload("app/assets/images/logo.png", "image/png")
        @user = FactoryBot.create(:user, id: 100, name: "testing")
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)
        @user.family_frame_photo = FactoryBot.create(:admin_medium, blob_data: image)
        @user.family_frame_name = "Family Frame"
        @user.hero_frame_photo = FactoryBot.create(:admin_medium, blob_data: image)
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 1,
                                                h2_count: 1,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                                    header_type: :header_2, priority: 1)])

        @time = Time.parse("2023-09-20")
        FactoryBot.create(:user_plan, user_id: @user.id, end_date: @time + 1.month)

        @frame1 = FactoryBot.create(:frame, identifier: :gold_sticker, frame_type: :status, gold_border: true,
                                    has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                    identity_type: :polygonal_profile_identity)
        @frame2 = FactoryBot.create(:frame, identifier: :cornered_gold_border, frame_type: :status, gold_border: true,
                                    has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: true,
                                    identity_type: :premium_cornered_party_icon_shiny_identity)
        @frame3 = FactoryBot.create(:frame, identifier: :semi_circle, frame_type: :premium, gold_border: false,
                                    has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                    identity_type: :semi_circular_identity)
        @frame4 = FactoryBot.create(:frame, identifier: :multi_color, frame_type: :premium, gold_border: false,
                                    has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                    identity_type: :multi_color_identity)
        @frame5 = FactoryBot.create(:frame, frame_type: :family_frame_premium, gold_border: false,
                                    has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                    identity_type: :trapezoidal_identity)
        @frame6 = FactoryBot.create(:frame, frame_type: :hero_frame_premium, gold_border: false,
                                    has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                    identity_type: :top_trapezoidal_identity)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame1.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame2.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame3.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame4.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame5.id)
        FactoryBot.create(:user_frame, user_id: @user.id, frame_id: @frame6.id)
      end
      it "test party_icon and party_tag logic" do
        @event = FactoryBot.create(:event, start_time: @time - 1.hour, end_time: @time + 2.hours)
        # for party icon hash testing
        @admin_user = FactoryBot.create(:admin_user)
        data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: @admin_user.id)
        @badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :WHITE)
        @circle = @badge_icon.badge_icon_group.circle
        @party_icon_url = @user.get_affiliated_party_icon(@circle.id)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        @user.affiliated_party_circle_id = @circle.id
        @user.save
        is_user_position_back = @user.get_is_user_position_back_for_posters_tab_v2
        travel_to(@time) do
          @user_layouts = @user.get_user_layouts(category_id: @event.id)
          expect(@user_layouts).not_to be_nil
          expect(@user_layouts.length).to eq(6)
          expect(@user_layouts[0][:identity][:type]).to eq("multi_color_identity")
          expect(@user_layouts[1][:identity][:type]).to eq("trapezoidal_identity")
          expect(@user_layouts[2][:identity][:type]).to eq("top_trapezoidal_identity")
          expect(@user_layouts[3][:identity][:type]).to eq("polygonal_profile_identity")
          expect(@user_layouts[4][:identity][:type]).to eq("premium_cornered_party_icon_shiny_identity")
          expect(@user_layouts[5][:identity][:type]).to eq("semi_circular_identity")
          # check logic of party tag based on has_footer_party_icon flag on frame
          expect(@user_layouts[0][:party_icon][:url]).to eq(@party_icon_url)
          expect(@user_layouts[1][:party_icon]).to eq(nil)
          expect(@user_layouts[2][:party_icon][:url]).to eq(@party_icon_url)
          expect(@user_layouts[3][:party_icon][:url]).to eq(@party_icon_url)
          expect(@user_layouts[4][:party_icon]).to eq(nil)
          expect(@user_layouts[5][:party_icon][:url]).to eq(@party_icon_url)
          # check logic of party icon based on has_footer_party_icon flag on frame
          expect(@user_layouts[0][:identity][:party_icon_url]).to eq(nil)
          expect(@user_layouts[1][:identity][:party_icon_url]).to eq(nil)
          expect(@user_layouts[2][:identity][:party_icon_url]).to eq(nil)
          expect(@user_layouts[3][:identity][:party_icon_url]).to eq(nil)
          expect(@user_layouts[4][:identity][:party_icon_url]).to eq(@party_icon_url)
          expect(@user_layouts[5][:identity][:party_icon_url]).to eq(nil)

          # check logic of font config
          expect(@user_layouts[0][:fonts_config]).to eq(@user.default_font_config(is_user_position_back))
          expect(@user_layouts[1][:fonts_config]).to eq(@user.default_font_config(is_user_position_back))
          expect(@user_layouts[2][:fonts_config]).to eq(@user.default_font_config(is_user_position_back))
          expect(@user_layouts[3][:fonts_config]).to eq(@user.default_font_config(is_user_position_back))
          expect(@user_layouts[4][:fonts_config]).to eq(@user.default_font_config(is_user_position_back))
          expect(@user_layouts[5][:fonts_config]).to eq(@user.default_font_config(is_user_position_back))

          # for family frame protocol should not be sent
          # expect header_1_photos to be empty
          expect(@user_layouts[0][:header_1_photos].length).to eq(1)
          expect(@user_layouts[1][:header_1_photos].length).to eq(0)
          expect(@user_layouts[2][:header_1_photos].length).to eq(1)
          expect(@user_layouts[3][:header_1_photos].length).to eq(1)
          expect(@user_layouts[4][:header_1_photos].length).to eq(1)
          expect(@user_layouts[5][:header_1_photos].length).to eq(1)

          expect(@user_layouts[0][:header_2_photos].length).to eq(1)
          expect(@user_layouts[1][:header_2_photos].length).to eq(0)
          expect(@user_layouts[2][:header_2_photos].length).to eq(1)
          expect(@user_layouts[3][:header_2_photos].length).to eq(1)
          expect(@user_layouts[4][:header_2_photos].length).to eq(1)
          expect(@user_layouts[5][:header_2_photos].length).to eq(1)

          # for family frame, photo and name should be different
          expect(@user_layouts[1][:identity][:user][:photo_url]).to eq(@user.family_frame_photo.url)
          expect(@user_layouts[1][:identity][:user][:name]).to eq(@user.family_frame_name)
          # for hero frame, photo should be different
          expect(@user_layouts[2][:identity][:user][:photo_url]).to eq(@user.hero_frame_photo.url)
        end
      end

      it 'send custom_role_name for user if user has custom role name' do
        @event = FactoryBot.create(:event, start_time: @time - 1.hour, end_time: @time + 2.hours)
        # for party icon hash testing
        @admin_user = FactoryBot.create(:admin_user)
        data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: @admin_user.id)
        @badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :WHITE)
        @circle = @badge_icon.badge_icon_group.circle
        @party_icon_url = @user.get_affiliated_party_icon(@circle.id)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)

        @role = FactoryBot.create(:role,
                                  name: Faker::Name.unique.name,
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  active: true,
                                  badge_icon_ribbon: true)

        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id)
        @user.affiliated_party_circle_id = @circle.id
        @user.custom_role_name = "Custom Role Name"
        @user.save
        travel_to(@time) do
          @user_layouts = @user.get_user_layouts(category_id: @event.id)
          expect(@user_layouts).not_to be_nil
          expect(@user_layouts.length).to eq(6)
          expect(@user_layouts[0][:identity][:type]).to eq("multi_color_identity")
          expect(@user_layouts[1][:identity][:type]).to eq("trapezoidal_identity")
          expect(@user_layouts[2][:identity][:type]).to eq("top_trapezoidal_identity")
          expect(@user_layouts[3][:identity][:type]).to eq("polygonal_profile_identity")
          expect(@user_layouts[4][:identity][:type]).to eq("premium_cornered_party_icon_shiny_identity")
          expect(@user_layouts[5][:identity][:type]).to eq("semi_circular_identity")

          # if custom role name is present for user and user has badge then custom role name should be displayed
          expect(@user_layouts[0][:identity][:user][:badge]['description']).to eq(@user.custom_role_name)
          expect(@user_layouts[2][:identity][:user][:badge]['description']).to eq(@user.custom_role_name)
          expect(@user_layouts[3][:identity][:user][:badge]['description']).to eq(@user.custom_role_name)
          expect(@user_layouts[4][:identity][:user][:badge]['description']).to eq(@user.custom_role_name)
          expect(@user_layouts[5][:identity][:user][:badge]['description']).to eq(@user.custom_role_name)

          # for family frame, photo and name should be different
          expect(@user_layouts[1][:identity][:user][:photo_url]).to eq(@user.family_frame_photo.url)
          expect(@user_layouts[1][:identity][:user][:name]).to eq(@user.family_frame_name)
          # for hero frame, photo should be different
          expect(@user_layouts[2][:identity][:user][:photo_url]).to eq(@user.hero_frame_photo.url)
        end
      end
    end
  end
  describe "#get_identity_type_based_on_name_size" do
    context "get identity type based on gold_border_presence, name size and user role description size" do
      it "when gold_border_presence is true and name size is <= 10 and user role description size is <=14" do
        @user = FactoryBot.create(:user, name: "Test User")
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        @role = FactoryBot.create(:role,
                                  name: "sarpanch",
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  display_name_order: "role",
                                  active: true,
                                  badge_icon_ribbon: true)

        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
        )
        identity_type = @user.get_identity_type_based_on_name_size(false, "gold_lettered_user", @user_role)
        expect(identity_type).to eq("gold_lettered_user_front")
      end
      it "when gold_border_presence is true and name size is > 10 and user role description size is >14" do
        @user = FactoryBot.create(:user, name: "Test User Test User")
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        @role = FactoryBot.create(:role,
                                  name: "sarpanch sarpanch",
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  display_name_order: "role,parent",
                                  active: true,
                                  badge_icon_ribbon: true)

        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
        )
        identity_type = @user.get_identity_type_based_on_name_size(false, "gold_lettered_user", @user_role)
        expect(identity_type).to eq("gold_lettered_user_back")
      end
      it "when gold_border_presence is true and name size is > 10 and user role description size is <14" do
        @user = FactoryBot.create(:user, name: "Test User Test User")
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        @role = FactoryBot.create(:role,
                                  name: "sarpanch",
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  display_name_order: "role,parent",
                                  active: true,
                                  badge_icon_ribbon: true)

        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
        )
        identity_type = @user.get_identity_type_based_on_name_size(false, "gold_lettered_user", @user_role)
        expect(identity_type).to eq("gold_lettered_user_back")
      end
      it "when gold_border_presence is true and name size is < 10 and user role description size is > 14" do
        @user = FactoryBot.create(:user, name: "Test User")
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        @role = FactoryBot.create(:role,
                                  name: "sarpanch sarpanch",
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  display_name_order: "role,parent",
                                  active: true,
                                  badge_icon_ribbon: true)

        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
        )
        identity_type = @user.get_identity_type_based_on_name_size(false, "gold_lettered_user", @user_role)
        expect(identity_type).to eq("gold_lettered_user_back")
      end
      it "when gold_border_presence is false and name size is <= 10 and user role description size is <=14" do
        @user = FactoryBot.create(:user, name: "Test User")
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        @role = FactoryBot.create(:role,
                                  name: "sarpanch",
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  display_name_order: "role",
                                  active: true,
                                  badge_icon_ribbon: true)

        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
        )
        identity_type = @user.get_identity_type_based_on_name_size(false, "flat_user", @user_role)
        expect(identity_type).to eq("flat_user_front")
      end
      it "when gold_border_presence is false and name size is > 10 and user role description size is >14" do
        @user = FactoryBot.create(:user, name: "Test User Test User")
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        @role = FactoryBot.create(:role,
                                  name: "sarpanch sarpanch",
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  display_name_order: "role,parent",
                                  active: true,
                                  badge_icon_ribbon: true)

        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id)
        identity_type = @user.get_identity_type_based_on_name_size(false, "flat_user", @user_role)
        expect(identity_type).to eq("flat_user_back")
      end
      it "when gold_border_presence is false and name size is > 10 and user role description size is <14" do
        @user = FactoryBot.create(:user, name: "Test User Test User")
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        @role = FactoryBot.create(:role,
                                  name: "sarpanch",
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  display_name_order: "role,parent",
                                  active: true,
                                  badge_icon_ribbon: true)

        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id,
        )
        identity_type = @user.get_identity_type_based_on_name_size(false, "flat_user", @user_role)
        expect(identity_type).to eq("flat_user_back")
      end
      it "when gold_border_presence is false and name size is < 10 and user role description size is > 14" do
        @user = FactoryBot.create(:user, name: "Test User")
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)

        @role = FactoryBot.create(:role,
                                  name: "sarpanch sarpanch",
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :no_limit,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  purview_level: nil,
                                  display_name_order: "role,parent",
                                  active: true,
                                  badge_icon_ribbon: true)

        @user_role = FactoryBot.create(:user_role, user: @user, role: @role, parent_circle_id: @circle.id)
        identity_type = @user.get_identity_type_based_on_name_size(false, "flat_user", @user_role)
        expect(identity_type).to eq("flat_user_back")
      end
    end
  end

  describe "#get_common_hash_for_layout_and_layout_variation" do
    context "get common hash for layout and layout variation" do
      before :each do
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user = FactoryBot.create(:user)
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 1,
                                                h2_count: 1,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                                    header_type: :header_2, priority: 1)])

      end
      it "for poster unsubscribed user" do
        @order = FactoryBot.create(:order, user_id: @user.id, status: :opened)
        layout, common_hash = @user.get_common_hash_for_layout_and_layout_variation(entity: @user)
        expect(layout).to eq("layout_1_1")
        expect(common_hash[:header_1_photos].count).to eq(1)
        expect(common_hash[:header_2_photos].count).to eq(1)
      end
      it "for poster subscribed user" do
        @order = FactoryBot.create(:order, user_id: @user.id, status: :opened, order_items: [
          FactoryBot.build(:order_item, item_type: "Product", item_id: Constants.get_poster_product_id, duration_in_months: 1)
        ])

        @order.update(status: :successful)
        FactoryBot.create(
          :user_product_subscription,
          user_id: @user.id,
          item_type: "Product",
          item_id: Constants.get_poster_product_id,
          order_id: @order.id,
          start_date: Time.zone.now - 1.month,
          end_date: Time.zone.now + 1.month
        )
        layout, common_hash = @user.get_common_hash_for_layout_and_layout_variation(entity: @user)
        expect(layout).to eq("layout_1_1")
        expect(common_hash[:header_1_photos].count).to eq(1)
        expect(common_hash[:header_2_photos].count).to eq(1)
      end
    end
  end

  describe "#get_layout_and_header_photos" do
    context "get layout and header photos for common hash layout" do
      before :each do
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user = FactoryBot.create(:user)
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 1,
                                                h2_count: 1,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                                    header_type: :header_2, priority: 1)])

      end
      it "returns layout and header photos for common hash layout" do
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        expect(layout).to eq("layout_1_1")
        expect(header_1_photos.count).to eq(1)
        expect(header_2_photos.count).to eq(1)
      end
    end
  end

  describe "#get_affiliated_party_icon" do
    context "get affiliated party icon for layout" do
      it "returns party icon url" do
        @user = FactoryBot.create(:user)
        @photo = FactoryBot.create(:photo)
        @admin_user = FactoryBot.create(:admin_user)
        data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: @admin_user.id)
        @badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :WHITE)
        circle_id = @badge_icon.badge_icon_group.circle.id
        party_icon_url = @user.get_affiliated_party_icon(circle_id)
        expect(party_icon_url).to be_present
      end
      it "returns nil if party icon not present" do
        @user = FactoryBot.create(:user)
        circle_id = -1
        party_icon_url = @user.get_affiliated_party_icon(circle_id)
        expect(party_icon_url).to eq(nil)
      end
    end
  end

  describe "#get_layout for users" do
    context "get layout for user" do
      before :each do
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user = FactoryBot.create(:user)
        AppVersionSupport.new('2308.32.19')
        @user_poster_layout = UserPosterLayout.find_or_create_by(entity: @user) do |upl|
          upl.h1_count = 1,
            upl.h2_count = 1,
            upl.user_leader_photos = [
              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                header_type: :header_1, priority: 1),
              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                header_type: :header_2, priority: 1)]
        end
        @circle = FactoryBot.create(:circle)
      end
      it "returns basic layout with default badge text color for older poster json data" do
        identity_type = "flat_user"
        layout = @user.get_layout('basic', false, false, @circle, nil, nil, nil, original_layout_type: 'basic')
        expect(layout[:layout_type]).to eq("basic")
        expect(layout[:golden_frame]).to eq(false)
        expect(layout[:shadow_color]).to eq(nil)
        expect(layout[:v1]).to be_present
        expect(layout[:identity]).to be_present
        expect(layout[:party_icon]).to be_nil
        expect(layout[:badge_text_color]).to eq(@circle.get_badge_text_color(identity_type))
      end

      it "returns basic layout with black badge text color for app version 2409.23.01 or newer" do
        AppVersionSupport.new('2409.23.01')
        identity_type = "flat_user"
        user_poster_layout_id = 1
        @circle.circle_type != :interest
        layout = @user.get_layout('basic', false, false, @circle, nil, nil, nil, identity_type, user_poster_layout_id, original_layout_type: 'basic')
        expect(layout[:layout_type]).to eq("basic")
        expect(layout[:golden_frame]).to eq(false)
        expect(layout[:shadow_color]).to eq(nil)
        expect(layout[:v1]).to be_present
        expect(layout[:identity]).to be_present
        expect(layout[:badge_text_color]).to eq(@user.get_overrided_badge_text_color_for_basic_frame(@circle, identity_type))
      end

      it "returns premium layout for user" do
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user_poster_layout = UserPosterLayout.find_or_create_by(entity: @user) do |upl|
          upl.h1_count = 1,
            upl.h2_count = 1,
            upl.user_leader_photos = [
              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                header_type: :header_1, priority: 1),
              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                header_type: :header_2, priority: 1)]
        end
        layout = @user.get_layout('premium', false, false, @circle, nil, "layout_1_1", nil,
                                  :party_white_flat_identity, original_layout_type: 'premium')
        expect(layout[:layout_type]).to eq("premium")
        expect(layout[:golden_frame]).to eq(false)
        expect(layout[:shadow_color]).to eq(nil)
        expect(layout[:v1]).to be_nil
        expect(layout[:identity]).to be_present
        expect(layout[:party_icon]).to be_nil
      end

      it "returns premium neon layout for user" do
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user_poster_layout = UserPosterLayout.find_or_create_by(entity: @user) do |upl|
          upl.h1_count = 1,
            upl.h2_count = 1,
            upl.user_leader_photos = [
              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                header_type: :header_1, priority: 1),
              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                header_type: :header_2, priority: 1)]
        end
        layout = @user.get_layout('premium', false, true, @circle, nil, "layout_1_1", nil, original_layout_type: 'premium')
        expect(layout[:layout_type]).to eq("premium")
        expect(layout[:golden_frame]).to eq(false)
        expect(layout[:shadow_color]).to be_present
        expect(layout[:v1]).to be_nil
        expect(layout[:identity]).to be_present
        expect(layout[:party_icon]).to be_nil
      end

      it "returns premium gold layout for user" do
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user_poster_layout = UserPosterLayout.find_or_create_by(entity: @user) do |upl|
          upl.h1_count = 1,
            upl.h2_count = 1,
            upl.user_leader_photos = [
              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                header_type: :header_1, priority: 1),
              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                header_type: :header_2, priority: 1)]
        end
        layout = @user.get_layout('premium', true, false, @circle, nil, "layout_1_1", nil, original_layout_type: 'premium')
        expect(layout[:layout_type]).to eq("premium")
        expect(layout[:golden_frame]).to eq(true)
        expect(layout[:shadow_color]).to be_nil
        expect(layout[:text_color]).to eq(4279519637)
        expect(layout[:v1]).to be_nil
        expect(layout[:identity]).to be_present
        expect(layout[:party_icon]).to be_nil
      end

      it "returns premium pure gold layout for user" do
        # get_layout(layout_type, gold_border, has_shadow_color, app_version, circle, user_role,
        #                  layout_variation, party_icon_url)
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user_poster_layout = UserPosterLayout.find_or_create_by(entity: @user) do |upl|
          upl.h1_count = 1,
            upl.h2_count = 1,
            upl.user_leader_photos = [
              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                header_type: :header_1, priority: 1),
              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                header_type: :header_2, priority: 1)]
        end
        layout = @user.get_layout('premium', false, false, @circle, nil, "layout_1_1", nil, "gold_lettered_user",
                                  original_layout_type: 'premium')
        expect(layout[:layout_type]).to eq("premium")
        expect(layout[:golden_frame]).to eq(false)
        expect(layout[:shadow_color]).to be_nil
        expect(layout[:text_color]).to be_nil
        expect(layout[:v1]).to be_nil
        expect(layout[:identity]).to be_present
        expect(layout[:party_icon]).to be_nil
      end
    end
  end

  describe "#get_layout_and_header_photos for all layout types" do
    context "get layout and header photos for each layout" do
      before :each do
        AppVersionSupport.new('2311.30.01')
        @photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user = FactoryBot.create(:user)
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: @photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: @photo)
        # for party icon hash testing
        @admin_user = FactoryBot.create(:admin_user)
        data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: @admin_user.id)
        @badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id, color: :WHITE)
        circle_id = @badge_icon.badge_icon_group.circle.id
        @party_icon_url = @user.get_affiliated_party_icon(circle_id)
      end
      it "returns layout and header photos for layout_0_0" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 0,
                                                h2_count: 0,
                                                user_leader_photos: [])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_0_0")
        expect(header_1_photos.count).to eq(0)
        expect(header_2_photos.count).to eq(0)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("top")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_0_1" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 0,
                                                h2_count: 1,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_0_1")
        expect(header_1_photos.count).to eq(0)
        expect(header_2_photos.count).to eq(1)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("top")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_0_2" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 0,
                                                h2_count: 2,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_0_2")
        expect(header_1_photos.count).to eq(0)
        expect(header_2_photos.count).to eq(2)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("top")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_0_3" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 0,
                                                h2_count: 3,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 3)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_0_3")
        expect(header_1_photos.count).to eq(0)
        expect(header_2_photos.count).to eq(3)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("top")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_0_4" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 0,
                                                h2_count: 4,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 3),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 4)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_0_4")
        expect(header_1_photos.count).to eq(0)
        expect(header_2_photos.count).to eq(4)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("top")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_0_5" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 0,
                                                h2_count: 5,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 3),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 4),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 5)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_0_5")
        expect(header_1_photos.count).to eq(0)
        expect(header_2_photos.count).to eq(5)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("top")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_0_6" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 0,
                                                h2_count: 6,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 3),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 4),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 5),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 6)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_0_6")
        expect(header_1_photos.count).to eq(0)
        expect(header_2_photos.count).to eq(6)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("top")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_0_7" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 0,
                                                h2_count: 7,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 3),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 4),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 5),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 6),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 7)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_0_7")
        expect(header_1_photos.count).to eq(0)
        expect(header_2_photos.count).to eq(7)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("top")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_0_8" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 0,
                                                h2_count: 8,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 3),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 4),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 5),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 6),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 7),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 8)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_0_8")
        expect(header_1_photos.count).to eq(0)
        expect(header_2_photos.count).to eq(8)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("top")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_0_9" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 0,
                                                h2_count: 9,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 3),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 4),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 5),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 6),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 7),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 8),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 9)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_0_9")
        expect(header_1_photos.count).to eq(0)
        expect(header_2_photos.count).to eq(9)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("top")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_0_10" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 0,
                                                h2_count: 10,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 3),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 4),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 5),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 6),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 7),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 8),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 9),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 10)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_0_10")
        expect(header_1_photos.count).to eq(0)
        expect(header_2_photos.count).to eq(10)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("top")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_1_0" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 1,
                                                h2_count: 0,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 1)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_1_0")
        expect(header_1_photos.count).to eq(1)
        expect(header_2_photos.count).to eq(0)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("top")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_1_1" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 1,
                                                h2_count: 1,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_1_1")
        expect(header_1_photos.count).to eq(1)
        expect(header_2_photos.count).to eq(1)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("top")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_1_2" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 1,
                                                h2_count: 2,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_1_2")
        expect(header_1_photos.count).to eq(1)
        expect(header_2_photos.count).to eq(2)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("top")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_1_3" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 1,
                                                h2_count: 3,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 3)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_1_3")
        expect(header_1_photos.count).to eq(1)
        expect(header_2_photos.count).to eq(3)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("top")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_1_4" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 1,
                                                h2_count: 4,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 3),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 4)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_1_4")
        expect(header_1_photos.count).to eq(1)
        expect(header_2_photos.count).to eq(4)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("left")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_1_5" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 1,
                                                h2_count: 5,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 3),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 4),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 5)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_1_5")
        expect(header_1_photos.count).to eq(1)
        expect(header_2_photos.count).to eq(5)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("top")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_1_6" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 1,
                                                h2_count: 6,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 3),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 4),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 5),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 6)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_1_6")
        expect(header_1_photos.count).to eq(1)
        expect(header_2_photos.count).to eq(6)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("top")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_2_0" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 2,
                                                h2_count: 0,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 2)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_2_0")
        expect(header_1_photos.count).to eq(2)
        expect(header_2_photos.count).to eq(0)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("left")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_2_1" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 2,
                                                h2_count: 1,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_2_1")
        expect(header_1_photos.count).to eq(2)
        expect(header_2_photos.count).to eq(1)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("left")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_2_2" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 2,
                                                h2_count: 2,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_2_2")
        expect(header_1_photos.count).to eq(2)
        expect(header_2_photos.count).to eq(2)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("left")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_2_3" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 2,
                                                h2_count: 3,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 3)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_2_3")
        expect(header_1_photos.count).to eq(2)
        expect(header_2_photos.count).to eq(3)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("left")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_2_4" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 2,
                                                h2_count: 4,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 3),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 4)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_2_4")
        expect(header_1_photos.count).to eq(2)
        expect(header_2_photos.count).to eq(4)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("left")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_2_5" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 2,
                                                h2_count: 5,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 3),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 4),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 5)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_2_5")
        expect(header_1_photos.count).to eq(2)
        expect(header_2_photos.count).to eq(5)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("left")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_2_6" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 2,
                                                h2_count: 6,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 3),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 4),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 5),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 6)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_2_6")
        expect(header_1_photos.count).to eq(2)
        expect(header_2_photos.count).to eq(6)
        expect(party_icon).to be_present
      end
      it "returns layout and header photos for layout_2_7" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 2,
                                                h2_count: 7,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 3),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 4),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 5),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 6),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 7)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_2_7")
        expect(header_1_photos.count).to eq(2)
        expect(header_2_photos.count).to eq(7)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("left")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_2_8" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 2,
                                                h2_count: 8,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 3),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 4),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 5),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 6),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 7),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 8)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_2_8")
        expect(header_1_photos.count).to eq(2)
        expect(header_2_photos.count).to eq(8)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("left")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_2_9" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 2,
                                                h2_count: 9,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 3),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 4),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 5),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 6),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 7),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 8),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 9)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_2_9")
        expect(header_1_photos.count).to eq(2)
        expect(header_2_photos.count).to eq(9)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("left")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_2_10" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 2,
                                                h2_count: 10,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 3),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 4),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 5),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 6),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 7),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 8),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 9),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_2, priority: 10)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_2_10")
        expect(header_1_photos.count).to eq(2)
        expect(header_2_photos.count).to eq(10)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("left")
        expect(party_icon[:gradients]).to be_present
      end
      it "returns layout and header photos for layout_3_0" do
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user,
                                                h1_count: 3,
                                                h2_count: 0,
                                                user_leader_photos: [
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 1),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 2),
                                                  FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: @photo),
                                                                    header_type: :header_1, priority: 3)])
        layout, header_1_photos, header_2_photos = @user.get_layout_and_header_photos(entity: @user)
        party_icon = @user.get_party_icon_hash(layout, @badge_icon.badge_icon_group.circle, @party_icon_url, false)
        expect(layout).to eq("layout_3_0")
        expect(header_1_photos.count).to eq(3)
        expect(header_2_photos.count).to eq(0)
        expect(party_icon).to be_present
        expect(party_icon[:url]).to eq(@party_icon_url)
        expect(party_icon[:position]).to eq("left")
        expect(party_icon[:gradients]).to be_present
      end
    end
  end

  describe "#get_subscriptions_status_for_frames" do
    context "get subscriptions status for frames" do
      it "returns subscriptions status for frames" do
        user = FactoryBot.create(:user)
        frame = FactoryBot.create(:frame, identifier: :party_curved_identity, frame_type: :premium, gold_border: false,
                                  has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                  identity_type: :curved)

        order = FactoryBot.create(:order, user_id: user.id, status: :opened, order_items: [
          FactoryBot.build(:order_item, item_type: "Frame", item_id: frame.id, duration_in_months: 1)
        ])

        order.update(status: :successful)

        FactoryBot.create(
          :user_product_subscription,
          user_id: user.id,
          order_id: order.id,
          start_date: 1.day.ago,
          end_date: 1.days.from_now,
          item_type: 'Frame',
          item_id: frame.id
        )
        status = user.get_subscriptions_status_for_frames([frame.id])
        expect(status).to eq(:subscribed)
      end
    end
  end

  describe "#is_subscribed_to_frame?" do
    context "is subscribed to frame" do
      it "returns true if user is subscribed to frame" do
        user = FactoryBot.create(:user)
        frame = FactoryBot.create(:frame, identifier: :party_curved_identity, frame_type: :premium, gold_border: false,
                                  has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                  identity_type: :curved)
        order = FactoryBot.create(:order, user_id: user.id, status: :opened, order_items: [
          FactoryBot.build(:order_item, item_type: "Frame", item_id: frame.id, duration_in_months: 1)
        ])

        order.update(status: :successful)

        FactoryBot.create(
          :user_product_subscription,
          user_id: user.id,
          order_id: order.id,
          start_date: 1.day.ago,
          end_date: 1.days.from_now,
          item_type: 'Frame',
          item_id: frame.id
        )
        is_subscribed = user.is_subscribed_to_frame?([frame.id])
        expect(is_subscribed).to eq(true)
      end
    end
  end

  describe "#has_open_order_on_frame?" do
    context "has open order on frame" do
      it "returns true if user has open order on frame" do
        user = FactoryBot.create(:user)
        frame = FactoryBot.create(:frame, identifier: :party_curved_identity, frame_type: :premium, gold_border: false,
                                  has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                  identity_type: :curved)
        order = FactoryBot.create(:order, user_id: user.id, status: :opened)
        FactoryBot.create(:order_item, order_id: order.id, item_type: "Frame", item_id: frame.id, duration_in_months: 1)
        has_open_order = user.has_open_order_on_frame?([frame.id])
        expect(has_open_order).to eq(true)
      end
    end
  end

  describe "#my_poster_sections_v2" do
    context "it returns subscription toast, subscription banner,events and category sections" do
      before :each do
        @state_level_circle = FactoryBot.create(:circle,
                                                name: Faker::Name.unique.name,
                                                name_en: Faker::Name.unique.name,
                                                active: true,
                                                members_count: 0,
                                                circle_type: :location,
                                                level: :state)
        @user = FactoryBot.create(:user, state_id: @state_level_circle.id)
      end
      it "should return if subscription toast, subscription banner,events and category sections are present
          for party affiliated user" do
        @circle = FactoryBot.create(:circle) # interest circle
        @user.affiliated_party_circle_id = @circle.id
        @user.save
        # user role
        @role = FactoryBot.create(:role,
                                  name: "testing",
                                  has_badge: true,
                                  badge_ring: true,
                                  badge_color: :GOLD,
                                  quota_type: :absolute,
                                  quota_value: 2,
                                  grade_level: :grade_1,
                                  parent_circle_level: :political_party,
                                  has_purview: false,
                                  active: true)
        @private_circle = FactoryBot.create(:circle, level: :private, circle_type: :my_circle)
        @user_role = FactoryBot.create(:user_role, user: @user, role: @role,
                                       parent_circle_id: @circle.id)

        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true, creative_kind: :info)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative, circle: @circle)
        @poster_creative1 = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1,
                                              photo_v2: @admin_medium_2, primary: true, creative_kind: :info)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative1, circle: @private_circle)
        @user_private_circle = FactoryBot.create(:user_circle, user: @user, circle: @private_circle)
        @poster_sections = @user.my_poster_sections_v2
        expect(@poster_sections).not_to be_nil
        expect(@poster_sections[0][:title]).to eq("సమాచారం-#{@circle.name}")
        expect(@poster_sections[0][:items].length).to eq(1)
      end

      it "should return if subscription toast, subscription banner,events and category sections are present
          for non party affiliated user" do
        @circle = FactoryBot.create(:circle) # interest circle
        @circle_1 = FactoryBot.create(:circle) # interest circle
        @poster_sections = @user.my_poster_sections_v2
        expect(@poster_sections).not_to be_nil
      end
    end
  end

  describe "#get_events_section_v2" do
    context "get the data of events section" do
      before :each do
        @user = FactoryBot.create(:user)
      end
      it "should return the events section data nil if no events are there" do
        @events_section = @user.get_events_section_v2(
          user_affiliated_circle_ids: [], is_eligible_for_premium_creatives: @user.is_eligible_for_premium_creatives?)
        expect(@events_section).to eq([])
      end
      it "should return the events section data if events are there" do
        @event = FactoryBot.create(:event)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        expect(@event).not_to be_nil
        expect(@poster_creative).not_to be_nil
        @events_section = @user.get_events_section_v2(
          user_affiliated_circle_ids: @user.circles,
          is_eligible_for_premium_creatives: @user.is_eligible_for_premium_creatives?)
        expect(@events_section).not_to be_nil
        items = @events_section.first[:items]
        expect(@events_section.first[:title]).to eq(@event.name)
        expect(items.first[:create_poster_params][:id]).to eq("#{@event.id}")
        expect(items.first[:create_poster_params][:creative_id]).to eq("#{@poster_creative.id}")
        expect(items.first[:creative_photo]).to eq(@poster_creative.photo_v3)
      end

      it "should not return events section if user affiliated circle are not match with the event circles" do
        @event = FactoryBot.create(:event)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        expect(@event).not_to be_nil
        expect(@poster_creative).not_to be_nil
        @events_section = @user.get_events_section_v2(
          user_affiliated_circle_ids: @user.circles,
          is_eligible_for_premium_creatives: @user.is_eligible_for_premium_creatives?)
        expect(@events_section).to eq([])
      end

      it "should return events which are in current time" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        expect(@event).not_to be_nil
        expect(@poster_creative).not_to be_nil
        @events_section = @user.get_events_section_v2(
          user_affiliated_circle_ids: @user.circles,
          is_eligible_for_premium_creatives: @user.is_eligible_for_premium_creatives?)
        expect(@events_section).not_to be_nil
        items = @events_section.first[:items]
        expect(@events_section.first[:title]).to eq(@event.name)
        expect(items.first[:create_poster_params][:id]).to eq("#{@event.id}")
        expect(items.first[:create_poster_params][:creative_id]).to eq("#{@poster_creative.id}")
        expect(items.first[:creative_photo]).to eq(@poster_creative.photo_v3)
      end

      it "should not return events which are not in current time" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 2.hours, end_time: Time.now + 4.hours)
        @event_1 = FactoryBot.create(:event, start_time: Time.zone.now + 2.hours, end_time: Time.now + 4.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @event_circle_1 = FactoryBot.create(:event_circle, event: @event_1, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event_1, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: true)
        expect(@event).not_to be_nil
        expect(@event_1).not_to be_nil
        expect(@poster_creative_1).not_to be_nil
        @events_section = @user.get_events_section_v2(
          user_affiliated_circle_ids: @user.circles,
          is_eligible_for_premium_creatives: @user.is_eligible_for_premium_creatives?)

        expect(@events_section).not_to be_nil
        items = @events_section.first[:items]
        expect(@events_section.first[:title]).to eq(@event.name)
        expect(items.length).to eq(1)
        expect(items.first[:create_poster_params][:id]).to eq("#{@event.id}")
        expect(items.first[:create_poster_params][:creative_id]).to eq("#{@poster_creative.id}")
        expect(items.first[:creative_photo]).to eq(@poster_creative.photo_v3)
      end

      it "should not return event if event doesn't have at least one poster creative" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        expect(@event).not_to be_nil
        @events_section = @user.get_events_section_v2(
          user_affiliated_circle_ids: @user.circles,
          is_eligible_for_premium_creatives: @user.is_eligible_for_premium_creatives?)
        expect(@events_section).to eq([])
      end

      it "should not return event if event doesn't have poster creative in current time" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 2.hours, end_time: Time.now + 4.hours)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true, start_time: Time.zone.now + 2.hours,
                                             end_time: Time.zone.now + 4.hours)
        expect(@event).not_to be_nil
        expect(@poster_creative).not_to be_nil
        @events_section = @user.get_events_section_v2(
          user_affiliated_circle_ids: @user.circles,
          is_eligible_for_premium_creatives: @user.is_eligible_for_premium_creatives?)
        expect(@events_section).to eq([])
      end

      it "should return events in the priority order of political leader, political party and location" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @event_1 = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @event_2 = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @state_level_circle = FactoryBot.create(:circle,
                                                name: Faker::Name.unique.name,
                                                name_en: Faker::Name.unique.name,
                                                active: true,
                                                members_count: 0,
                                                circle_type: :location,
                                                level: :state)
        @political_leader_level_circle = FactoryBot.create(:circle,
                                                           name: Faker::Name.unique.name,
                                                           name_en: Faker::Name.unique.name,
                                                           active: true,
                                                           members_count: 0,
                                                           circle_type: :interest,
                                                           level: :political_leader,
                                                           circle_photos:
                                                             [FactoryBot.build(:circle_photo,
                                                                               photo: FactoryBot.create(:photo),
                                                                               photo_type: :poster,
                                                                               photo_order: 1)])
        @political_party_level_circle = FactoryBot.create(:circle,
                                                          name: Faker::Name.unique.name,
                                                          name_en: Faker::Name.unique.name,
                                                          active: true,
                                                          members_count: 0,
                                                          circle_type: :interest,
                                                          level: :political_party)
        @event_circle = FactoryBot.create(:event_circle, event: @event_1, circle: @state_level_circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @political_leader_level_circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event_2, circle: @political_party_level_circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @state_level_circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @political_leader_level_circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @political_party_level_circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                          photo_v2: @admin_medium_2, primary: true)
        FactoryBot.create(:poster_creative, event: @event_1, photo_v3: @admin_medium_1,
                          photo_v2: @admin_medium_2, primary: true)
        FactoryBot.create(:poster_creative, event: @event_2, photo_v3: @admin_medium_1,
                          photo_v2: @admin_medium_2, primary: true)

        @events_section = @user.get_events_section_v2(
          user_affiliated_circle_ids: @user.circles,
          is_eligible_for_premium_creatives: @user.is_eligible_for_premium_creatives?)
        expect(@events_section).not_to be_nil

        events = @events_section
        expect(events.length).to eq(3)
        expect(events.first[:items].first[:create_poster_params][:id]).to eq("#{@event.id}")
        expect(events.second[:items].first[:create_poster_params][:id]).to eq("#{@event_2.id}")
        expect(events.last[:items].first[:create_poster_params][:id]).to eq("#{@event_1.id}")
      end

      it "should return events in the priority order of their priority order and political leader, political party and location" do
        @event = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @event_1 = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours)
        @event_2 = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours,
                                     priority: :medium)
        @event_3 = FactoryBot.create(:event, start_time: Time.zone.now - 1.hour, end_time: Time.now + 2.hours,
                                     priority: :high)
        @state_level_circle = FactoryBot.create(:circle,
                                                name: Faker::Name.unique.name,
                                                name_en: Faker::Name.unique.name,
                                                active: true,
                                                members_count: 0,
                                                circle_type: :location,
                                                level: :state)
        @political_leader_level_circle = FactoryBot.create(:circle,
                                                           name: Faker::Name.unique.name,
                                                           name_en: Faker::Name.unique.name,
                                                           active: true,
                                                           members_count: 0,
                                                           circle_type: :interest,
                                                           level: :political_leader,
                                                           circle_photos:
                                                             [FactoryBot.build(:circle_photo,
                                                                               photo: FactoryBot.create(:photo),
                                                                               photo_type: :poster,
                                                                               photo_order: 1)])
        @political_party_level_circle = FactoryBot.create(:circle,
                                                          name: Faker::Name.unique.name,
                                                          name_en: Faker::Name.unique.name,
                                                          active: true,
                                                          members_count: 0,
                                                          circle_type: :interest,
                                                          level: :political_party)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @state_level_circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event_1, circle: @political_leader_level_circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event_2, circle: @political_party_level_circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event_3, circle: @political_party_level_circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @state_level_circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @political_leader_level_circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @political_party_level_circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                          photo_v2: @admin_medium_2, primary: true)
        FactoryBot.create(:poster_creative, event: @event_1, photo_v3: @admin_medium_1,
                          photo_v2: @admin_medium_2, primary: true)
        FactoryBot.create(:poster_creative, event: @event_2, photo_v3: @admin_medium_1,
                          photo_v2: @admin_medium_2, primary: true)
        FactoryBot.create(:poster_creative, event: @event_3, photo_v3: @admin_medium_1,
                          photo_v2: @admin_medium_2, primary: true)

        @events_section = @user.get_events_section_v2(
          user_affiliated_circle_ids: @user.circles,
          is_eligible_for_premium_creatives: @user.is_eligible_for_premium_creatives?)
        expect(@events_section).not_to be_nil

        events = @events_section
        expect(events.length).to eq(4)
        expect(events.first[:items].first[:create_poster_params][:id]).to eq("#{@event_3.id}")
        expect(events.second[:items].first[:create_poster_params][:id]).to eq("#{@event_2.id}")
        expect(events.third[:items].first[:create_poster_params][:id]).to eq("#{@event_1.id}")
        expect(events.last[:items].first[:create_poster_params][:id]).to eq("#{@event.id}")
      end

      it "should return an event with two image urls if the event has two poster creatives" do
        @event = FactoryBot.create(:event)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false)
        @events_section = @user.get_events_section(@user.circles, @user.is_eligible_for_premium_creatives?)

        categories = @events_section[:categories]
        expect(categories.first[:title]).to eq(@event.name)
        expect(categories.first[:params][:id]).to eq("#{@event.id}")
        expect(categories.first[:image_urls][0]).to eq(@poster_creative.photo_v2.url)
        expect(categories.first[:image_urls][1]).to eq(@poster_creative_1.photo_v2.url)

        @events_section = @user.get_events_section_v2(
          user_affiliated_circle_ids: @user.circles,
          is_eligible_for_premium_creatives: @user.is_eligible_for_premium_creatives?)
        expect(@events_section).not_to be_nil
        items = @events_section.first[:items]
        expect(@events_section.first[:title]).to eq(@event.name)
        expect(items.first[:create_poster_params][:id]).to eq("#{@event.id}")
        expect(items.first[:create_poster_params][:creative_id]).to eq("#{@poster_creative.id}")
        expect(items.first[:creative_photo]).to eq(@poster_creative.photo_v3)
        expect(items.last[:creative_photo]).to eq(@poster_creative_1.photo_v3)
      end

      it "should not return an event if it has only premium poster creatives for a normal user" do
        @event = FactoryBot.create(:event)
        @circle = FactoryBot.create(:circle)
        @event_circle = FactoryBot.create(:event_circle, event: @event, circle: @circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @free_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                           photo_v2: @admin_medium_2, primary: true, paid: false)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true, paid: true)
        @poster_creative_1 = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                               photo_v2: @admin_medium_2, primary: false, paid: true)
        @free_creative.destroy
        @events_section = @user.get_events_section_v2(
          user_affiliated_circle_ids: @user.circles,
          is_eligible_for_premium_creatives: @user.is_eligible_for_premium_creatives?)
        expect(@events_section).to eq([])
      end

    end
  end

  describe "#get_creative_kinds_sections_v2" do
    context "get the data of creative kinds section" do
      it "should return the data of creative kinds section of schemes of normal user" do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                             photo_v2: @admin_medium_2, primary: true, creative_kind: :schemes)
        @poster_creative_1 = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                               photo_v2: @admin_medium_2, primary: false, creative_kind: :schemes)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative,
                          circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_1,
                          circle: @circle)

        @creative_kind_sections = @user.get_creative_kinds_sections_v2(
          user_affiliated_circle_ids: @user.circles,
          is_eligible_for_premium_creatives: @user.has_premium_layout?)
        expect(@creative_kind_sections).not_to be_nil
        expect(@creative_kind_sections.first[:title])
          .to eq("#{PosterCreative.get_creative_kind_verbose("schemes")}-#{@circle.name}")
        items = @creative_kind_sections.first[:items]
        expect(items.length).to eq(2)
        expect(items.first[:creative_photo]).to eq(@poster_creative.photo_v3)
        expect(items.last[:creative_photo]).to eq(@poster_creative_1.photo_v3)
      end

      it "should return the data of creative kinds section of schemes of premium user" do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                             photo_v2: @admin_medium_2, primary: true, creative_kind: :schemes)
        @poster_creative_1 = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                               photo_v2: @admin_medium_2, primary: false, creative_kind: :schemes)
        @poster_creative_2 = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                               photo_v2: @admin_medium_2, primary: false, creative_kind: :schemes,
                                               paid: true)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative,
                          circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_1,
                          circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_2,
                          circle: @circle)

        @creative_kind_sections = @user.get_creative_kinds_sections_v2(
          user_affiliated_circle_ids: @user.circles,
          is_eligible_for_premium_creatives: true)
        expect(@creative_kind_sections).not_to be_nil
        expect(@creative_kind_sections.first[:title])
          .to eq("#{PosterCreative.get_creative_kind_verbose("schemes")}-#{@circle.name}")
        items = @creative_kind_sections.first[:items]
        expect(items.length).to eq(3)
        expect(items.first[:creative_photo]).to eq(@poster_creative.photo_v3)
        expect(items.second[:creative_photo]).to eq(@poster_creative_1.photo_v3)
        expect(items.last[:creative_photo]).to eq(@poster_creative_2.photo_v3)
      end

      it "should return the data of creative kinds section of schemes of normal user where it returns only free poster
          creatives" do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                             photo_v2: @admin_medium_2, primary: true, creative_kind: :schemes)
        @poster_creative_1 = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                               photo_v2: @admin_medium_2, primary: false, creative_kind: :schemes)
        @poster_creative_2 = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                               photo_v2: @admin_medium_2, primary: false, creative_kind: :schemes,
                                               paid: true)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative,
                          circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_1,
                          circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_2,
                          circle: @circle)

        @creative_kind_sections = @user.get_creative_kinds_sections_v2(
          user_affiliated_circle_ids: @user.circles,
          is_eligible_for_premium_creatives: false)
        expect(@creative_kind_sections).not_to be_nil
        expect(@creative_kind_sections.first[:title])
          .to eq("#{PosterCreative.get_creative_kind_verbose("schemes")}-#{@circle.name}")
        items = @creative_kind_sections.first[:items]
        expect(items.length).to eq(2)
        expect(items.first[:creative_photo]).to eq(@poster_creative.photo_v3)
        expect(items.last[:creative_photo]).to eq(@poster_creative_1.photo_v3)
      end

      it "should return category kinds sections data in order" do
        @user = FactoryBot.create(:user)
        @circle = FactoryBot.create(:circle)
        @user_circle = FactoryBot.create(:user_circle, user: @user, circle: @circle)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                             photo_v2: @admin_medium_2, primary: true, creative_kind: :schemes)
        @poster_creative_1 = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                               photo_v2: @admin_medium_2, primary: false, creative_kind: :info)
        @poster_creative_2 = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                               photo_v2: @admin_medium_2, primary: false, creative_kind: :quotes)
        @poster_creative_3 = FactoryBot.create(:poster_creative, photo_v3: @admin_medium_1, has_event: false, event: nil,
                                               photo_v2: @admin_medium_2, primary: false, creative_kind: :wishes)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative,
                          circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_1,
                          circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_2,
                          circle: @circle)
        FactoryBot.create(:poster_creative_circle, poster_creative: @poster_creative_3,
                          circle: @circle)

        @creative_kind_sections = @user.get_creative_kinds_sections_v2(
          user_affiliated_circle_ids: @user.circles,
          is_eligible_for_premium_creatives: false)
        expect(@creative_kind_sections).not_to be_nil
        expect(@creative_kind_sections.first[:title])
          .to eq("#{PosterCreative.get_creative_kind_verbose("info")}-#{@circle.name}")
        expect(@creative_kind_sections.second[:title])
          .to eq("#{PosterCreative.get_creative_kind_verbose("schemes")}-#{@circle.name}")
        expect(@creative_kind_sections.third[:title])
          .to eq("#{PosterCreative.get_creative_kind_verbose("quotes")}-#{@circle.name}")
      end
    end
  end
end
