require 'rails_helper'

RSpec.describe Photo, type: :model do
  describe "photo record" do
    context "service being gcp" do
      before :each do
        # As gcp service data comes from APP, we made this flow wrt to data attribute
        # So any photo create in terms of gcp should happen wrt data attribute
        photo =
          {
            "file_id": "0c8f194b1be9391a18d4da433e03acfa",
            "file_name": "0c8f194b1be9391a18d4da433e03acfa.jpg",
            "original_file_name": "1645707.jpg",
            "path": "",
            "cdn_url": ""
          }
        @photo = FactoryBot.build(:photo, ms_data: photo, service: :gcp, url: nil, path: nil)
      end

      it "doesn't update if makes service null" do
        @photo.service = nil
        expect { @photo.presence_of_service }.to raise_error(RuntimeError, "Photo service is blank")
      end

      it "doesn't update if makes url null" do
        expect { @photo.presence_of_url }.to raise_error(RuntimeError, "Photo url is blank")
      end

      it "doesn't update if makes path null" do
        expect { @photo.presence_of_path }.to raise_error(RuntimeError, "Photo path is blank")
      end
    end

    context "#upload_new" do
      before :each do
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @photo = FactoryBot.create(:photo, blob_data: photo, service: :aws)
      end

      it "upload to aws if service is aws on before create" do
        expect(@photo.attributes['url'].start_with?('https://a-cdn.thecircleapp.in/')).to be_truthy
        expect(@photo.url.start_with?('https://a-cdn.thecircleapp.in/')).to be_truthy
        expect(@photo.service.to_sym).to be(:aws)
      end

      it "raises exception if the upload fails" do
        allow_any_instance_of(Photo).to receive(:upload_new).and_raise(StandardError)
        expect { @photo.upload_new }.to raise_error(StandardError)
      end
    end


    context "#to_honeybadger_context" do
      it "return photo_id json" do
        photo = FactoryBot.create(:photo, service: :aws)
        photo_honey_badger_context = photo.to_honeybadger_context

        expect(photo_honey_badger_context).to eq({ photo_id: photo.id })
      end
    end

    context "#bg_color and #set_dominant_dark_color" do
      before :each do
        @photo = FactoryBot.create(:photo, service: :aws)
      end

      it "return #000000 color for bg color" do
        expect(@photo.bg_color).to eq("#000000")
      end

      it "return #000000 color for dominant color" do
        expect(@photo.set_dominant_dark_color).to eq("#000000")
      end
    end

    context "#set_aspect_ratio"  do
      it "return set aspect ratio as a float if size is present" do
        photo = FactoryBot.create(:photo, service: :aws)
        allow(FastImage).to receive(:size).and_return([50, 100])

        expect(photo.set_aspect_ratio).to eq(0.5)
      end

      it "return set aspect ratio as 1.0 if size is nil" do
        photo = FactoryBot.create(:photo, service: :aws)
        allow(FastImage).to receive(:size).and_return(nil)

        expect(photo.set_aspect_ratio).to eq(1.0)
      end

      it "return 1.0 if raises any error" do
        photo = FactoryBot.create(:photo, service: :aws)
        allow(FastImage).to receive(:size).and_raise(StandardError)

        expect(photo.set_aspect_ratio).to eq(1.0)
      end
    end

    context "#aspect ratio"  do
      it "return aspect ratio if width and height are present" do
        photo_data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        photo = FactoryBot.create(:photo, blob_data: photo_data, service: :aws)
        width, height = FastImage.size(photo_data.path)
        expect(width).not_to be_nil
        expect(height).not_to be_nil
        photo.update(path: "test/photos/15271/0e88a5f0-725f-4231-841a-7125576256c3.png")
        expect(photo.aspect_ratio).to eq((width.to_f / height.to_f).round(2))
      end

      it "triggers SaveDimensions worker if width & height <= 0" do
        Sidekiq::Testing.fake! do
          expect {
            photo = FactoryBot.create(:photo, width: 0, height: 0, service: :aws)
            photo.aspect_ratio
          }.to change(SavePhotoDimensionsWorker.jobs, :size).by(1)
        end

        photo = FactoryBot.create(:photo, width: 0, height: 0, service: :aws)
        expect(photo).to receive(:set_aspect_ratio)
        photo.aspect_ratio
      end

      it "triggers set aspect ratio method if width & height <= 0" do
        photo = FactoryBot.create(:photo, width: 0, height: 0, service: :aws)
        expect(photo).to receive(:set_aspect_ratio)
        photo.aspect_ratio
      end

      it "return 1.0 for aspect ratio if photo is not in required format" do
        photo = FactoryBot.create(:photo, url: "https://cdn.thecircleapp.in/development/photos/1/8216c8de61020d89cfde5bd3f10d1337.heic",service: :aws)

        expect(photo.aspect_ratio).to eq(1.0)
      end
    end

    context '#upload' do
      it "raises exception if the upload fails" do
        user = FactoryBot.create(:user)
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")

        allow(Photo).to receive(:upload).and_raise(StandardError)
        expect { Photo.upload(photo, user.id) }.to raise_error(StandardError)
      end
    end

    context '#compressed_url' do
      it 'calls compressed_url method' do
        photo = FactoryBot.create(:photo, service: :aws)

        expect(photo).to receive(:compressed_url)
        photo.compressed_url(size: 100)

        expect(photo).to receive(:compressed_url)
        photo.compressed_url(size: 100)
      end
    end

    context '#get_compressed_photo_url' do
      it 'returns compressed photo url' do
        photo = FactoryBot.create(:photo, service: :aws)
        compressed_photo_url = Photo.get_compressed_photo_url(photo.url)

        result = photo.url.gsub('https://cdn.thecircleapp.in/', 'https://l-cdn.praja.buzz/120x120/')

        expect(compressed_photo_url).to be_present
        expect(compressed_photo_url).to eq(result)
      end
    end

    context '#compressed_url' do
      before :each do
        data =
          {
            'file_id': '0c8f194b1be9391a18d4da433e03acfa',
            'file_name': '0c8f194b1be9391a18d4da433e03acfa.jpg',
            'original_file_name': '1645707.jpg',
            'path': '50/0c8f194b1be9391a18d4da433e03acfa.jpg',
            'cdn_url': 'https://cdn.thecircleapp.in/50/0c8f194b1be9391a18d4da433e03acfa.jpg',
          }
        @photo = FactoryBot.create(:photo, ms_data: data, service: :azure)
        @photo.set_photo_data
      end

      it 'returns fit-in url when fit is true' do
        compressed_url = @photo.compressed_url(size: 100)

        expect(compressed_url).to eq('https://a-az-cdn.thecircleapp.in/fit-in/100x100/filters:quality(80)/50/0c8f194b1be9391a18d4da433e03acfa.jpg')
      end

      it 'returns cover url when fit is false' do
        compressed_url = @photo.compressed_url(fit: false, size: 100)

        expect(compressed_url).to eq('https://a-az-cdn.thecircleapp.in/100x100/filters:quality(80)/50/0c8f194b1be9391a18d4da433e03acfa.jpg')
      end

      it 'returns different width and height when they are provided' do
        compressed_url = @photo.compressed_url(fit: false, width: 100, height: 200)

        expect(compressed_url).to eq('https://a-az-cdn.thecircleapp.in/100x200/filters:quality(80)/50/0c8f194b1be9391a18d4da433e03acfa.jpg')
      end

      it 'returns gcp cdn url when service is gcp' do
        data = @photo.ms_data
        gcp_photo = FactoryBot.create(:photo, ms_data: data, service: :gcp)
        gcp_photo.set_photo_data

        compressed_url = gcp_photo.compressed_url(fit: false, size: 100)

        expect(compressed_url).to eq('https://a-gc-cdn.thecircleapp.in/100x100/filters:quality(80)/50/0c8f194b1be9391a18d4da433e03acfa.jpg')
      end

      it 'returns aws cdn url when service is aws' do
        aws_photo = FactoryBot.create(:photo, service: :aws)

        compressed_url = aws_photo.compressed_url(fit: false, size: 100)

        expect(compressed_url).to match(/^https:\/\/a-cdn.thecircleapp.in\/100x100\/.*/)
      end
    end
  end

  describe "#set_bucket" do
    context 'if the bucket is blank and data[:bucket] is present' do

      it 'sets the photo bucket with the data hash' do
        data =
          {
            'path': '50/0c8f194b1be9391a18d4da433e03acfa.jpg',
            'cdn_url': 'https://cdn.thecircleapp.in/50/0c8f194b1be9391a18d4da433e03acfa.jpg',
            'bucket': 'prodprajarup',
          }
        @photo = FactoryBot.create(:photo, ms_data: data, service: :azure)

        expect(@photo.bucket).to eq('prodprajarup')
      end

      it 'sets the photo bucket with the constants' do
        data =
          {
            'path': '50/0c8f194b1be9391a18d4da433e03acfa.jpg',
            'cdn_url': 'https://cdn.thecircleapp.in/50/0c8f194b1be9391a18d4da433e03acfa.jpg',
          }
        @photo = FactoryBot.create(:photo, ms_data: data, service: :azure)

        expect(@photo.bucket).to eq('prodprajarup')
      end

      it 'does not set bucket for invalid service' do
        data = {
          path: '50/0c8f194b1be9391a18d4da433e03acfa.jpg',
          cdn_url: 'https://cdn.thecircleapp.in/50/0c8f194b1be9391a18d4da433e03acfa.jpg'
        }

        expect {
          FactoryBot.create(:photo, ms_data: data, service: 'invalid_service')
        }.to raise_error(ArgumentError)
      end

      it "set the bucket even the data is not a hash(nil)" do
        @photo = FactoryBot.create(:photo, service: :aws)
        expect(@photo.bucket).to eq(Constants.aws_buckets_source)
      end

    end
  end

  describe "#set_path" do
    context 'if the path is blank and data[:path] is present' do

      it 'sets the photo path with the data hash' do
        data =
          {
            'path': '50/0c8f194b1be9391a18d4da433e03acfa.jpg',
            'cdn_url': 'https://cdn.thecircleapp.in/50/0c8f194b1be9391a18d4da433e03acfa.jpg',
          }
        @photo = FactoryBot.create(:photo, ms_data: data, service: :azure)
        expect(@photo.path).to eq('50/0c8f194b1be9391a18d4da433e03acfa.jpg')
      end

      it 'sets the photo bucket with the url where data is not there' do
        url = 'https://cdn.thecircleapp.in/50/0c8f194b1be9391a18d4da433e03acfa.jpg'
        @photo = FactoryBot.create(:photo, url: url, service: :aws)
        expect(@photo.path).to eq('50/0c8f194b1be9391a18d4da433e03acfa.jpg')
        expect(@photo.path).not_to eq('/50/0c8f194b1be9391a18d4da433e03acfa.jpg')
      end

    end
  end
  describe '#photo_download_url' do
    let(:photo) { build_stubbed(:photo, bucket: 'test-bucket', path: 'test/path/to/image.jpg') }
    let(:s3_resource_mock) { instance_double(Aws::S3::Resource) }
    let(:s3_bucket_mock) { instance_double(Aws::S3::Bucket) }
    let(:s3_object_mock) { instance_double(Aws::S3::Object) }
    let(:presigned_url) { 'https://presigned-url-for-download.com/image.jpg' }

    before do
      # Stub AWS credentials
      allow(Rails.application.credentials).to receive(:[]).with(:aws_access_key_id).and_return('test_access_key_id')
      allow(Rails.application.credentials).to receive(:[]).with(:aws_secret_access_key).and_return('test_secret_access_key')

      # Stub S3 resource and related objects
      allow(Aws::S3::Resource).to receive(:new).and_return(s3_resource_mock)
      allow(s3_resource_mock).to receive(:bucket).with('test-bucket').and_return(s3_bucket_mock)
      allow(s3_bucket_mock).to receive(:object).with('test/path/to/image.jpg').and_return(s3_object_mock)
      allow(s3_object_mock).to receive(:presigned_url)
                               .with(:get_object, response_content_disposition: "attachment;")
                               .and_return(presigned_url)
    end

    it 'returns a presigned URL for downloading the file' do
      expect(photo.photo_download_url).to eq(presigned_url)
    end

    it 'uses the correct bucket and path from the photo' do
      # Create a photo with different bucket and path
      custom_photo = build_stubbed(:photo, bucket: 'custom-bucket', path: 'custom/path.jpg')

      # Expect the bucket method to be called with the custom bucket
      expect(s3_resource_mock).to receive(:bucket).with('custom-bucket').and_return(s3_bucket_mock)
      # Expect the object method to be called with the custom path
      expect(s3_bucket_mock).to receive(:object).with('custom/path.jpg').and_return(s3_object_mock)

      custom_photo.photo_download_url
    end
  end
end
