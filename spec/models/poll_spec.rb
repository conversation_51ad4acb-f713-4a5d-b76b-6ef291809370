require 'rails_helper'

RSpec.describe Poll, type: :model do
  describe 'poll' do
    before :each do
      @post = FactoryBot.create(:post)
      @poll = FactoryBot.create(:poll, post: @post)
      @poll_option = FactoryBot.create(:poll_option, poll: @poll)
      @poll_option2 = FactoryBot.create(:poll_option, poll: @poll)
      @user_poll_option = FactoryBot.create(:user_poll_option, poll_option: @poll_option)
      @user_poll_option2 = FactoryBot.create(:user_poll_option, poll_option: @poll_option2, user: @post.user)
    end

    context 'total_votes_count' do
      it 'returns count' do
        expect(@poll.total_votes_count).to eq(2)
      end
    end

    context 'is_selected' do
      it 'returns true' do
        expect(@poll.is_selected(@post.user)).to eq(true)
      end

      it 'returns false' do
        user = FactoryBot.create(:user)
        expect(@poll.is_selected(user)).to eq(false)
      end
    end
  end
end
