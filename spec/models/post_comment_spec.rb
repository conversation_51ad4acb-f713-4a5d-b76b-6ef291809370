require 'rails_helper'

RSpec.describe PostComment, type: :model do
  describe '#send_notification_for_other_commented_users' do
    before :each do
      @user = FactoryBot.create(:user)
      @post = FactoryBot.create(:post)
      @other_user = FactoryBot.create(:user)

      @post_comment = FactoryBot.create(:post_comment, post: @post, user: @user)
      @post_comment2 = FactoryBot.create(:post_comment, post: @post, user: @other_user)
      @user_device_token = FactoryBot.create(:user_device_token, user: @post_comment2.user)
    end

    context 'send notification ' do
      it 'to last commented user on the post other than post_user and user' do
        allow(GarudaNotification).to receive(:send_user_notification)
        @post_comment.send(:send_notification_for_other_commented_users)
        expect(GarudaNotification).to have_received(:send_user_notification)
      end

      it 'to last commented user where user name is nil' do
        allow(GarudaNotification).to receive(:send_user_notification)
        @user.name = nil
        @user.save(validate: false)
        @post_comment.send(:send_notification_for_other_commented_users)
        expect(GarudaNotification).to have_received(:send_user_notification)
      end
    end
  end

  describe '#send_notification_for_post_creator' do
    before :each do
      @user = FactoryBot.create(:user)
      @other_user = FactoryBot.create(:user)
      @post = FactoryBot.create(:post, user: @user)
      @post_comment = FactoryBot.create(:post_comment, post: @post, user: @other_user)
      @user_device_token = FactoryBot.create(:user_device_token, user: @post.user)
    end

    context 'send notification ' do
      it ' to post_user' do
        allow(GarudaNotification).to receive(:send_user_notification)
        @post_comment.send(:send_notification_for_post_creator)
      expect(GarudaNotification).to have_received(:send_user_notification)
      end
    end
  end
end
