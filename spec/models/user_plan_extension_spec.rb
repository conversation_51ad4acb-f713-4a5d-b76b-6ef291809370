require 'rails_helper'

RSpec.describe UserPlanExtension, type: :model do
  describe 'associations' do
    it 'belongs to user' do
      association = UserPlanExtension.reflect_on_association(:user)
      expect(association.macro).to eq(:belongs_to)
    end

    it 'belongs to user_plan' do
      association = UserPlanExtension.reflect_on_association(:user_plan)
      expect(association.macro).to eq(:belongs_to)
    end
  end

  describe 'validations' do
    it 'validates presence of duration_in_days' do
      extension = UserPlanExtension.new(duration_in_days: nil)
      extension.valid?
      expect(extension.errors[:duration_in_days]).to include('can\'t be blank')
    end

    it 'validates duration_in_days is greater than 0' do
      extension = UserPlanExtension.new(duration_in_days: 0)
      extension.valid?
      expect(extension.errors[:duration_in_days]).to include('must be greater than 0')
    end
  end

  describe 'callbacks' do
    describe '#log_extension_mixpanel_event' do
      let(:user) { create(:user) }
      let(:plan) { create(:plan) }
      let(:user_plan) { create(:user_plan, user: user, plan: plan) }

      it 'logs a mixpanel event when extension is created' do
        allow(EventTracker).to receive(:perform_async)

        extension = UserPlanExtension.create!(
          user: user,
          user_plan: user_plan,
          duration_in_days: 7,
          reason: :cancellation_flow
        )

        expect(EventTracker).to have_received(:perform_async).with(
          user.id,
          'user_plan_extension_created',
          hash_including(
            'duration_in_days' => 7,
            'reason' => 'cancellation_flow',
            'user_plan_id' => user_plan.id,
            'extension_id' => extension.id
          )
        )
      end
    end
  end
end
