require 'rails_helper'

RSpec.describe Hashtag, type: :model do
  describe 'do validate' do
    context ' hashtag' do
      before :each do
        @hashtag = FactoryBot.create(:hashtag, name: 'Test', identifier: "test", active: true)
      end
      it "is valid with valid attributes" do
        expect(@hashtag).to be_valid
      end
      it "is invalid when name is different from identifier" do
        @hashtag.name = "Test2"
        expect(@hashtag).to be_invalid
      end
    end
  end

  describe '#opinions' do
    context 'hashtag' do
      before :each do
        @hashtag = FactoryBot.create(:hashtag, name: 'Test', identifier: "test", active: true)
        @post_hashtag = FactoryBot.create(:post_hashtag, hashtag: @hashtag)
        @post_hashtag2 = FactoryBot.create(:post_hashtag, hashtag: @hashtag)
      end
      it "has many opinions" do
        expect(@hashtag.opinions).to eq([@post_hashtag.post, @post_hashtag2.post])
      end
    end
  end

  describe '#get_trending_hashtags_based_on_query_version' do
    context 'get the trending hashtags' do
      let(:hashtag1) { FactoryBot.create(:hashtag, name: 'Test', identifier: "test") }
      let(:hashtag2) { FactoryBot.create(:hashtag, name: 'Test2', identifier: "test2") }
      let(:hashtag3) { FactoryBot.create(:hashtag, name: 'Test3', identifier: "test3") }
      let(:hashtag4) { FactoryBot.create(:hashtag, name: 'Test4', identifier: "test4") }
      let(:hashtag5) { FactoryBot.create(:hashtag, name: 'Test5', identifier: "test5") }
      it "returns the trending hashtags" do
        allow(ES_CLIENT).to receive(:search).and_return(
          "hits"=>
            {
              "total"=>{"value"=>5, "relation"=>"eq"},
              "hits"=>
                [
                  { "_id"=>hashtag1.id, "fields"=> {"name"=>[hashtag1.name], "active"=>[true], "created_at"=>[hashtag1.created_at], "id"=>[hashtag1.id]}},
                  { "_id"=>hashtag2.id, "fields"=> {"name"=>[hashtag2.name], "active"=>[true], "created_at"=>[hashtag2.created_at], "id"=>[hashtag2.id]}},
                  { "_id"=>hashtag3.id, "fields"=> {"name"=>[hashtag3.name], "active"=>[true], "created_at"=>[hashtag3.created_at], "id"=>[hashtag3.id]}},
                  { "_id"=>hashtag4.id, "fields"=> {"name"=>[hashtag4.name], "active"=>[true], "created_at"=>[hashtag4.created_at], "id"=>[hashtag4.id]}},
                  { "_id"=>hashtag5.id, "fields"=> {"name"=>[hashtag5.name], "active"=>[true], "created_at"=>[hashtag5.created_at], "id"=>[hashtag5.id]}}
                ]
            }
        )
        result = Hashtag.get_trending_hashtags_based_on_query_version
        expect(result.count).to eq(5)
        expect(result[0]["id"]).to eq(hashtag1.id)
      end

      it "returns the [] if hits are nil" do
        allow(ES_CLIENT).to receive(:search).and_return(
          "hits"=>
            {
              "total"=>{"value"=>0, "relation"=>"eq"},
              "hits"=>[]
            }
        )
        result = Hashtag.get_trending_hashtags_based_on_query_version
        expect(result.count).to eq(0)
        expect(result).to eq([])
      end

      it "returns the [] if total value is not greater than 0" do
        allow(ES_CLIENT).to receive(:search).and_return(
          "hits"=>
            {
              "total"=>{"value"=>0, "relation"=>"eq"},
              "hits"=> []
            }
        )

        result = Hashtag.get_trending_hashtags_based_on_query_version
        expect(result.count).to eq(0)
        expect(result).to eq([])
      end
    end
  end

  describe "#get_likes_count & #get_opinions_count" do
    context 'hashtag is liked' do
      it "returns the likes count as 1" do
        name = Faker::Lorem.unique.word
        hashtag = FactoryBot.create(:hashtag, name: name, identifier: name.downcase.gsub(" ", "_"))
        FactoryBot.create(:hashtag_like, hashtag: hashtag, user: FactoryBot.create(:user, phone: "91#{Faker::Number.unique.number(digits: 8)}"))

        expect(hashtag.get_opinions_count).to eq(0)
        expect(hashtag.get_likes_count).to eq(1)
      end
    end
    context 'post created with hashtag' do
      it "just a post created, returns likes count as 1" do
        name = Faker::Lorem.unique.word
        hashtag = FactoryBot.create(:hashtag, name: name, identifier: name.downcase.gsub(" ", "_"))
        post = FactoryBot.create(:post, user: FactoryBot.create(:user, phone: "91#{Faker::Number.unique.number(digits: 8)}"))
        FactoryBot.create(:post_hashtag, hashtag: hashtag, post: post)

        expect(hashtag.get_opinions_count).to eq(1)
        expect(hashtag.get_likes_count).to eq(1)
      end
      it "post created & post liked, returns likes count as 2" do
        name = Faker::Lorem.unique.word
        hashtag = FactoryBot.create(:hashtag, name: name, identifier: name.downcase.gsub(" ", "_"))
        post = FactoryBot.create(:post, user: FactoryBot.create(:user, phone: "91#{Faker::Number.unique.number(digits: 8)}"))
        FactoryBot.create(:post_hashtag, hashtag: hashtag, post: post)
        FactoryBot.create(:post_like, post: post, user: FactoryBot.create(:user, phone: "91#{Faker::Number.unique.number(digits: 8)}"))

        expect(hashtag.get_opinions_count).to eq(1)
        expect(hashtag.get_likes_count).to eq(2)
      end
    end
  end
end
