require 'rails_helper'

RSpec.describe BlockedUser, type: :model do
  it 'should create a blocked user' do
    @user = FactoryBot.create(:user)
    @blocked_user = FactoryBot.create(:user)
    FactoryBot.create(:blocked_user, user: @user, blocked_user: @blocked_user)
    expect(@blocked_user).to be_valid
    expect($redis.smembers(Constants.get_user_blocked_ids_key(@user.id)).include?(@blocked_user.id.to_s)).to eq(true)
    expect($redis.smembers(Constants.get_user_blocked_by_ids_key(@blocked_user.id)).include?(@user.id.to_s)).to eq(true)
  end

  it 'should remove the blocked user id from the redis case on destroy' do
    @user = FactoryBot.create(:user)
    @blocked_user = FactoryBot.create(:user)
    user_block_obj = FactoryBot.create(:blocked_user, user: @user, blocked_user: @blocked_user)
    expect($redis.smembers(Constants.get_user_blocked_ids_key(@user.id)).include?(@blocked_user.id.to_s)).to eq(true)
    expect($redis.smembers(Constants.get_user_blocked_by_ids_key(@blocked_user.id)).include?(@user.id.to_s)).to eq(true)
    user_block_obj.destroy
    expect($redis.smembers(Constants.get_user_blocked_ids_key(@user.id)).include?(@blocked_user.id.to_s)).to eq(false)
    expect($redis.smembers(Constants.get_user_blocked_by_ids_key(@blocked_user.id)).include?(@user.id.to_s)).to eq(false)
  end
end
