require 'rails_helper'

RSpec.describe HashtagLike, type: :model do
  describe "validate callbacks" do
    context "hashtag like" do
      before :each do
        name = Faker::Lorem.unique.word
        @hashtag = FactoryBot.create(:hashtag, name: name, identifier: name.downcase.gsub(" ", "_"), active: true)
        @user = FactoryBot.create(:user, phone: "91#{Faker::Number.unique.number(digits: 8)}")
        @hashtag_like = FactoryBot.create(:hashtag_like, hashtag: @hashtag, user: @user, active: true)
      end
      it "increases the hashtag likes count" do
        expect($redis.hget(Hashtag::HASHTAG_LIKES_COUNT_REDIS_KEY, @hashtag.id).to_i).to eq(1)
      end
      it "decreases the hashtag likes count" do
        @hashtag_like.destroy
        expect($redis.hget(Hashtag::HASHTAG_LIKES_COUNT_REDIS_KEY, @hashtag.id).to_i).to eq(0)
      end
    end
  end
end
