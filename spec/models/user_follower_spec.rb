require 'rails_helper'

RSpec.describe UserFollower, type: :model do
  describe "for user follower" do
    context "with user and a request user" do
      before :each do
        @user = FactoryBot.create(:user)
        @request_user = FactoryBot.create(:user)
        @user_follower = FactoryBot.create(:user_follower, user: @user, follower: @request_user)
      end

      it 'if user tries to follow self follow does not work' do
        @user_follower = FactoryBot.build(:user_follower, user: @user, follower: @user)
        expect{ @user_follower.save! }.to raise_error(ActiveRecord::RecordInvalid, "Validation failed: Follower can't do self follow")
      end

      it 'if user tries to follow another user triggers three jobs' do
        expect(UpdateFollowingCount.jobs.size).to eq(1)
        expect(UpdateFollowersCount.jobs.size).to eq(1)
        expect(SendFollowNotification.jobs.size).to eq(1)
      end
    end
  end
end
