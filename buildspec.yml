version: 0.2

phases:
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - echo "${DOCKERHUB_PASSWORD}" | docker login -u "${DOCKERHUB_USERNAME}" --password-stdin
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com
      - docker pull $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME:latest || true
  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...
      - echo "CURRENT COMMIT - $CODEBUILD_RESOLVED_SOURCE_VERSION"
      - docker build --cache-from $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME:latest --build-arg BUILDKIT_INLINE_CACHE=1 --build-arg GITHUB_SHA=$CODEBUILD_RESOLVED_SOURCE_VERSION --tag $IMAGE_REPO_NAME:$CODEBUILD_RESOLVED_SOURCE_VERSION .
      - docker tag $IMAGE_REPO_NAME:$CODEBUILD_RESOLVED_SOURCE_VERSION $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME:$CODEBUILD_RESOLVED_SOURCE_VERSION
      - docker tag $IMAGE_REPO_NAME:$CODEBUILD_RESOLVED_SOURCE_VERSION $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME:latest
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker image...
      - docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME:$CODEBUILD_RESOLVED_SOURCE_VERSION
      - docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME:latest
      - printf '[{"name":"production-api","imageUri":"%s"}]' $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME:$CODEBUILD_RESOLVED_SOURCE_VERSION > imagedefinitions.json
      - mkdir source_directory_2
      - cd source_directory_2
      - printf '[{"name":"production-sidekiq","imageUri":"%s"}]' $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME:$CODEBUILD_RESOLVED_SOURCE_VERSION > sidekiqImagedefinitions.json
      - curl -X POST "https://api.newrelic.com/v2/applications/**********/deployments.json" -H "X-Api-Key:${NEWRELIC_API_KEY}" -i -H 'Content-Type:application/json' -d "{\"deployment\":{\"revision\":\"${CODEBUILD_RESOLVED_SOURCE_VERSION}\",\"changelog\":\"${CODEBUILD_SOURCE_VERSION}\",\"description\":\"${CODEBUILD_SOURCE_VERSION}\",\"user\":\"${CODEBUILD_INITIATOR}\"}}"
      - curl -L -X POST -H "Accept:application/vnd.github+json" -H "Authorization:Bearer ${GITHUB_TOKEN}" -H "X-GitHub-Api-Version:2022-11-28" https://api.github.com/repos/praja/fleet-infra/actions/workflows/********/dispatches -d '{"ref":"master","inputs":{"imageTag":"'${CODEBUILD_RESOLVED_SOURCE_VERSION}'","cluster":"aws","env":"production"}}'

artifacts:
  files:
    - '**/*'
  secondary-artifacts:
    artifact1:
      base-directory: $CODEBUILD_SRC_DIR
      files:
        - imagedefinitions.json
    artifact2:
      base-directory: $CODEBUILD_SRC_DIR_source2
      files:
        - sidekiqImagedefinitions.json
