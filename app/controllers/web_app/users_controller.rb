module WebApp
  class UsersController < WebAppController
    def update_profile
      name = params[:name]
      short_bio = params[:short_bio]
      photo = params[:photo]

      @user.name = name if name.present?
      @user.short_bio = short_bio if short_bio.present?

      if photo.present?
        @user.photo = Photo.upload(photo, Constants.praja_account_user_id)
        if @user.photo.blank?
          return render json: { success: false,
                                message: 'ఫోటో అప్లోడ్ చేయడం విఫలమైంది. కాసేపు ఆగి ప్రయత్నించండి', },
                        status: :bad_request
        end
      end

      if @user.save
        render json: @user.get_short_json, status: :ok
      else
        render json: { success: false, message: 'మీ ప్రొఫైల్ అప్డేట్ విఫలమైంది. కాసేపు ఆగి ప్రయత్నించండి.' },
               status: :bad_request
      end
    end
  end
end
