# frozen_string_literal: true

class Circle < ApplicationRecord
  has_paper_trail ignore: [:members_count]
  include Hashid::Rails
  include CircleSearchConcern
  attr_accessor :filter_role_ids, :filter_location_circle_ids, :filter_grade_levels, :creative_kind, :photo_v3,
                :send_dm_message_notification, :dm_text_message, :circle_package_id, :start_date, :end_date,
                :entity_type, :entity_id

  CIRCLE_LEVEL = %i[private village mandal district public_figure topic political_party political_leader
  mp_constituency mla_constituency state language public municipality corporation profession legislative executive
  judiciary private_group sub caste religion real_estate health shops tours_travels education finance automobiles
  others communities investor media]
  CIRCLE_TYPE = %i[location interest my_circle governing_body user_created sub business]

  CONVERSATIONS_MEMBERS_TYPE = { admin: :admin, owner: :owner, creator: :admin, designer: :admin, member: :member }.freeze

  enum level: CIRCLE_LEVEL, _suffix: true
  enum circle_type: CIRCLE_TYPE, _suffix: true
  enum conversation_type: Constants.conversation_types, _suffix: true

  validates_presence_of :name, :name_en, :level, :circle_type
  validate :validate_circle_level_based_on_circle_type
  validate :check_parent_circle_id, if: -> { parent_circle_id.nil? }
  validate :validate_short_name, if: -> { errors.blank? }
  validate :validate_conversation_type_on_circle, if: -> { errors.blank? }
  validate :check_parent_circle_id_level, if: -> { errors.blank? }

  has_many :user_circle
  has_many :post_circles, -> { where(active: true) }
  has_many :users, through: :user_circle
  has_many :posts, through: :post_circles
  belongs_to :parent_circle, class_name: 'Circle', foreign_key: 'parent_circle_id', optional: true
  has_many :child_circles, class_name: 'Circle', foreign_key: 'parent_circle_id'
  belongs_to :photo, optional: true
  belongs_to :head_user, class_name: 'User', optional: true
  belongs_to :banner, class_name: 'Photo', optional: true
  belongs_to :poster_photo, class_name: 'Photo', optional: true
  belongs_to :slogan_icon, polymorphic: true, optional: true
  has_many :circle_story_threads, -> { where(active: true) }
  has_many :circle_photos, dependent: :destroy
  has_many :poster_photos, -> { where(photo_type: :poster) }, class_name: 'CirclePhoto'
  accepts_nested_attributes_for :poster_photos, allow_destroy: true
  has_many :photos, through: :circle_photos
  has_many :user_circle_permission_groups
  has_one :user_poster_layout, as: :entity
  has_many :circle_frames
  has_one :badge_icon_group
  has_many :sub_circles, -> { where(level: :sub) }, class_name: 'Circle', foreign_key: 'parent_circle_id'
  has_many :circle_package_mappings
  has_many :circle_packages, through: :circle_package_mappings
  has_one :active_circle_package_mapping, -> { where(active: true).where('start_date <= :today AND :today <= end_date', today: Time.zone.today) }, class_name: 'CirclePackageMapping'
  has_one :circle_package, through: :active_circle_package_mapping
  has_many :existing_circle_package_mappings, -> { where(active: true) }, class_name: 'CirclePackageMapping'
  belongs_to :relationship_manager, class_name: 'AdminUser', foreign_key: 'relationship_manager_id', optional: true
  has_one :circle_monthly_usage, -> { where(active: true).where('month_start <= :today AND :today <= month_end', today: Time.zone.today) }
  has_many :circle_monthly_usages
  has_many :first_circle_relations, class_name: 'CirclesRelation', foreign_key: 'first_circle_id'
  has_many :second_circle_relations, class_name: 'CirclesRelation', foreign_key: 'second_circle_id'

  attribute :hashid
  attribute :photo
  attribute :feed_type
  attribute :feed_item_id
  attribute :intro
  attribute :is_user_joined
  attribute :new_posts_count
  attribute :last_posted_at
  attribute :circle_banner
  attribute :profile_photo
  attribute :circle_background

  before_save :update_conversation_type_if_nil, :handle_conversation_callback_to_dm_service, :clean_up
  after_save :check_to_create_circle_package_mapping, :create_channel, :create_circle_layout
  after_commit :index_for_search, :flush_cache, :generate_channel_preview, :generate_circle_preview,
               :update_kyc_creative, :alert_on_name_change_circle_with_roles
  after_create_commit :sub_circle_creation_users_joining

  scope :taggable_circles, -> { where(level: [:private, :village, :municipality, :corporation, :political_party, :political_leader, :profession]) }
  scope :dm_groups, -> { where(conversation_type: :private_group) }
  scope :admin_searchable, -> { where(level: [:political_party, :political_leader, :public_figure]).or(Circle.where(circle_type: :business)) }

  CACHE_KEY = 'circle_v17'

  MAX_CIRCLES = 3
  MAX_VILLAGE_CIRCLES = 1
  MAX_PARTY_CIRCLES = 1
  MAX_LEADER_CIRCLES = 2

  CIRCLE_LEVELS_WITH_AUTO_FOLLOW_OWNER = [:political_leader, :political_party]

  POLITICAL_PARTIES = { "ycp" => 31403, "ysrcp" => 31403, "వై.యస్.ఆర్ కాంగ్రెస్ పార్టీ" => 31403,
                        "వైసీపీ" => 31403, "వైసిపి" => 31403, "వై.యస్.ఆర్" => 31403,
                        "ysr" => 31403, "y s r" => 31403, "y s r c p" => 31403,
                        "y c p" => 31403, "tdp" => 31402, "telugu desam" => 31402, "టిడిపి" => 31402,
                        "టీడీపీ" => 31402, "బీజేపీ" => 31398, "bjp" => 31398, "భాజాపా" => 31398,
                        "భారతీయ జనతా పార్టీ" => 31398, "jsp" => 31406, "janasena" => 31406,
                        "జనసేన" => 31406, "trs" => 31405, "inc" => 31401, "congress" => 31401, "కాంగ్రెస్" => 31401 }

  PARTY_GRADIENTS = {
    31403 => {
      "circle_text_color" => 0xffFFFFFF,
      "circle_background_colors" => [0xff22BBB8, 0xff0266B4, 0xff008E46],
      "circle_background_color_stops" => [0.007, 0.4732, 0.712],
      "circle_background_gradient_begin_x" => -1.0,
      "circle_background_gradient_begin_y" => -1.0,
      "circle_background_gradient_end_x" => 1.0,
      "circle_background_gradient_end_y" => 1.0,
      "circle_background_overlay_colors" => [0xff001D49, 0x00C4C4C4],
      "circle_background_overlay_color_stops" => [0.0, 0.8732],
      "circle_background_overlay_gradient_begin_x" => 0.0,
      "circle_background_overlay_gradient_begin_y" => -1.0,
      "circle_background_overlay_gradient_end_x" => 0.0,
      "circle_background_overlay_gradient_end_y" => 1.0,
      "circle_header_colors" => [0xff0266B4, 0xff22BBB8, 0xff008E46],
      "circle_header_color_stops" => [0.1388, 0.5503, 0.8159],
      "circle_header_gradient_begin_x" => -1.0,
      "circle_header_gradient_begin_y" => 1.0,
      "circle_header_gradient_end_x" => 1.0,
      "circle_header_gradient_end_y" => 1.0,
      "circle_header_text_color" => 0xffFFFFFF,
      "poster_and_badge_card_background_colors" => [0xff008E46, 0xff22BBB8, 0xff0266B4],
      "poster_and_badge_card_background_color_stops" => [0.0, 0.4461, 1.0],
      "poster_and_badge_card_bg_gradient_begin_x" => 1.0,
      "poster_and_badge_card_bg_gradient_begin_y" => -1.0,
      "poster_and_badge_card_bg_gradient_end_x" => 1.0,
      "poster_and_badge_card_bg_gradient_end_y" => 1.0,
      "poster_and_badge_card_footer_colors" => [0xff0266B4, 0xff0266B4],
      "poster_and_badge_card_footer_color_stops" => nil,
      "poster_and_badge_card_footer_gradient_begin_x" => nil,
      "poster_and_badge_card_footer_gradient_begin_y" => nil,
      "poster_and_badge_card_footer_gradient_end_x" => nil,
      "poster_and_badge_card_footer_gradient_end_y" => nil,
      "badge_banner_colors" => [0xff003B6A, 0xff0266B4, 0xff0266B4, 0xff003B6A],
      "badge_banner_color_stops" => [0.0, 0.3368, 0.6826, 1.0],
      "about_info_outline_color" => 0xff498DEF,
      "about_info_bg_color" => 0xffF4F8FF,
      "positions_highlight_color" => 0xff498DEF,
      "projects_button_text_color" => 0xffFFFFFF,
      "projects_bottom_line_color" => 0xff0266B4,
      "positions_section_background_colors" => [0xffF4F8FF, 0xffFFFFFF],
      "positions_section_background_color_stops" => [0.0, 1.0],
      "positions_section_background_gradient_begin_x" => -1.0,
      "positions_section_background_gradient_begin_y" => -1.0,
      "positions_section_background_gradient_end_x" => 1.0,
      "positions_section_background_gradient_end_y" => 1.0,
      "projects_section_background_colors" => [0xff144995, 0xff22BBB8, 0xff009247],
      "projects_section_background_color_stops" => [0.0, 0.47, 1.0],
      "projects_section_background_gradient_begin_x" => -1.0,
      "projects_section_background_gradient_begin_y" => -1.0,
      "projects_section_background_gradient_end_x" => 1.0,
      "projects_section_background_gradient_end_y" => 1.0,
    },
    31402 => {
      "circle_text_color" => 0xff000000,
      "circle_background_colors" => [0xffFFED00, 0xffE30613],
      "circle_background_color_stops" => [0.3767, 0.7362],
      "circle_background_gradient_begin_x" => 1.0,
      "circle_background_gradient_begin_y" => 1.0,
      "circle_background_gradient_end_x" => -1.0,
      "circle_background_gradient_end_y" => -1.0,
      "circle_background_overlay_colors" => [0xff550000, 0x00C4C4C4],
      "circle_background_overlay_color_stops" => [0.0, 0.9357],
      "circle_background_overlay_gradient_begin_x" => 0.0,
      "circle_background_overlay_gradient_begin_y" => -1.0,
      "circle_background_overlay_gradient_end_x" => 0.0,
      "circle_background_overlay_gradient_end_y" => 1.0,
      "circle_header_colors" => [0xffFFED00, 0xffFFED00, 0xffE30613],
      "circle_header_color_stops" => [0.0313, 0.4826, 0.9514],
      "circle_header_gradient_begin_x" => -1.0,
      "circle_header_gradient_begin_y" => 1.0,
      "circle_header_gradient_end_x" => 1.0,
      "circle_header_gradient_end_y" => 1.0,
      "circle_header_text_color" => 0xff000000,
      "poster_and_badge_card_background_colors" => [0xffFFED00, 0xffF17720, 0xffE30613, 0xffFFED00],
      "poster_and_badge_card_background_color_stops" => [0.0, 0.3003, 0.6441, 1.0],
      "poster_and_badge_card_bg_gradient_begin_x" => 1.0,
      "poster_and_badge_card_bg_gradient_begin_y" => 1.0,
      "poster_and_badge_card_bg_gradient_end_x" => 1.0,
      "poster_and_badge_card_bg_gradient_end_y" => -1.0,
      "poster_and_badge_card_footer_colors" => [0xffFFED00, 0xffFFED00],
      "poster_and_badge_card_footer_color_stops" => nil,
      "poster_and_badge_card_footer_gradient_begin_x" => nil,
      "poster_and_badge_card_footer_gradient_begin_y" => nil,
      "poster_and_badge_card_footer_gradient_end_x" => nil,
      "poster_and_badge_card_footer_gradient_end_y" => nil,
      "badge_banner_colors" => [0xffF17720, 0xffFFED00, 0xffF17720],
      "badge_banner_color_stops" => [0.0, 0.4357, 1.0],
      "about_info_outline_color" => 0xffFFE607,
      "about_info_bg_color" => 0xffFFFEF4,
      "positions_highlight_color" => 0xffFFED00,
      "projects_bottom_line_color" => 0xffFFED00,
      "projects_button_text_color" => 0xff000000,
      "positions_section_background_colors" => [0xffFFFEF4, 0xffFFFFFF],
      "positions_section_background_color_stops" => [0.0, 1.0],
      "positions_section_background_gradient_begin_x" => -1.0,
      "positions_section_background_gradient_begin_y" => -1.0,
      "positions_section_background_gradient_end_x" => 1.0,
      "positions_section_background_gradient_end_y" => 1.0,
      "projects_section_background_colors" => [0xffFFED00, 0xffE30613],
      "projects_section_background_color_stops" => [0.48, 0.95],
      "projects_section_background_gradient_begin_x" => -1.0,
      "projects_section_background_gradient_begin_y" => -1.0,
      "projects_section_background_gradient_end_x" => 1.0,
      "projects_section_background_gradient_end_y" => 1.0
    },
    31406 => {
      "circle_text_color" => 0xffFFFFFF,
      "circle_background_colors" => [0xffCC0000, 0xffFFD0D0],
      "circle_background_color_stops" => [0.0125, 0.8732],
      "circle_background_gradient_begin_x" => 1.0,
      "circle_background_gradient_begin_y" => 1.0,
      "circle_background_gradient_end_x" => -1.0,
      "circle_background_gradient_end_y" => -1.0,
      "circle_background_overlay_colors" => [0xff550000, 0x00C4C4C4],
      "circle_background_overlay_color_stops" => [0.0, 0.9097],
      "circle_background_overlay_gradient_begin_x" => 0.0,
      "circle_background_overlay_gradient_begin_y" => -1.0,
      "circle_background_overlay_gradient_end_x" => 0.0,
      "circle_background_overlay_gradient_end_y" => 1.0,
      "circle_header_colors" => [0xffCC0000, 0xffFFB0B0],
      "circle_header_color_stops" => [0.34, 1.0],
      "circle_header_gradient_begin_x" => -1.0,
      "circle_header_gradient_begin_y" => 1.0,
      "circle_header_gradient_end_x" => 1.0,
      "circle_header_gradient_end_y" => 1.0,
      "circle_header_text_color" => 0xffFFFFFF,
      "poster_and_badge_card_background_colors" => [0xffCC0000, 0xffFFB0B0, 0xffCC0000],
      "poster_and_badge_card_background_color_stops" => [0.0, 0.6216, 1.0],
      "poster_and_badge_card_bg_gradient_begin_x" => 1.0,
      "poster_and_badge_card_bg_gradient_begin_y" => 1.0,
      "poster_and_badge_card_bg_gradient_end_x" => 1.0,
      "poster_and_badge_card_bg_gradient_end_y" => -1.0,
      "poster_and_badge_card_footer_colors" => [0xffCC0000, 0xffCC0000],
      "poster_and_badge_card_footer_color_stops" => nil,
      "poster_and_badge_card_footer_gradient_begin_x" => nil,
      "poster_and_badge_card_footer_gradient_begin_y" => nil,
      "poster_and_badge_card_footer_gradient_end_x" => nil,
      "poster_and_badge_card_footer_gradient_end_y" => nil,
      "badge_banner_colors" => [0xff540000, 0xffCC0000, 0xffCC0000, 0xff540000],
      "badge_banner_color_stops" => [0.0, 0.3264, 0.7118, 1.0],
      "about_info_outline_color" => 0xffCC0000,
      "about_info_bg_color" => 0xffFFF6F6,
      "positions_highlight_color" => 0xffCC0000,
      "projects_bottom_line_color" => 0xffCC0000,
      "projects_button_text_color" => 0xffFFFFFF,
      "positions_section_background_colors" => [0xffFFF6F6, 0xffFFFFFF],
      "positions_section_background_color_stops" => [1.0, 1.0],
      "positions_section_background_gradient_begin_x" => -1.0,
      "positions_section_background_gradient_begin_y" => -1.0,
      "positions_section_background_gradient_end_x" => 1.0,
      "positions_section_background_gradient_end_y" => 1.0,
      "projects_section_background_colors" => [0xffCC0000, 0xffFFB0B0],
      "projects_section_background_color_stops" => [0.34, 1.0],
      "projects_section_background_gradient_begin_x" => -1.0,
      "projects_section_background_gradient_begin_y" => -1.0,
      "projects_section_background_gradient_end_x" => 1.0,
      "projects_section_background_gradient_end_y" => 1.0
    },
    31401 => {
      "circle_text_color" => 0xffFFFFFF,
      "circle_background_colors" => [0xffF37022, 0xffFFFCED, 0xff0F823F],
      "circle_background_color_stops" => [0.192, 0.4878, 0.8993],
      "circle_background_gradient_begin_x" => -1.0,
      "circle_background_gradient_begin_y" => -1.0,
      "circle_background_gradient_end_x" => 1.0,
      "circle_background_gradient_end_y" => 1.0,
      "circle_background_overlay_colors" => [0xff621D00, 0x00C4C4C4],
      "circle_background_overlay_color_stops" => [0.0, 0.9305],
      "circle_background_overlay_gradient_begin_x" => 0.0,
      "circle_background_overlay_gradient_begin_y" => -1.0,
      "circle_background_overlay_gradient_end_x" => 0.0,
      "circle_background_overlay_gradient_end_y" => 1.0,
      "circle_header_colors" => [0xffF37022, 0xffFFFFFF, 0xff0F823F],
      "circle_header_color_stops" => [0.01, 0.48, 0.95],
      "circle_header_gradient_begin_x" => -1.0,
      "circle_header_gradient_begin_y" => 1.0,
      "circle_header_gradient_end_x" => 1.0,
      "circle_header_gradient_end_y" => 1.0,
      "circle_header_text_color" => 0xff000000,
      "poster_and_badge_card_background_colors" => [0xff359B44, 0xffFFFCED, 0xffF37022],
      "poster_and_badge_card_background_color_stops" => [0.0763, 0.5295, 1.0],
      "poster_and_badge_card_bg_gradient_begin_x" => 1.0,
      "poster_and_badge_card_bg_gradient_begin_y" => 1.0,
      "poster_and_badge_card_bg_gradient_end_x" => 1.0,
      "poster_and_badge_card_bg_gradient_end_y" => -1.0,
      "poster_and_badge_card_footer_colors" => [0xff359B44, 0xff359B44],
      "poster_and_badge_card_footer_color_stops" => nil,
      "poster_and_badge_card_footer_gradient_begin_x" => nil,
      "poster_and_badge_card_footer_gradient_begin_y" => nil,
      "poster_and_badge_card_footer_gradient_end_x" => nil,
      "poster_and_badge_card_footer_gradient_end_y" => nil,
      "badge_banner_colors" => [0xff003E1A, 0xff0F823F, 0xff0F823F, 0xff003E1A],
      "badge_banner_color_stops" => [0.0, 0.2899, 0.6701, 1.0],
      "about_info_outline_color" => 0xff0F823F,
      "about_info_bg_color" => 0xffF6FFF8,
      "positions_highlight_color" => 0xff2CB565,
      "projects_bottom_line_color" => 0xffF37022,
      "projects_button_text_color" => 0xff000000,
      "positions_section_background_colors" => [0xffF6FFF8, 0xffFFFFFF],
      "positions_section_background_color_stops" => [0.0, 1.0],
      "positions_section_background_gradient_begin_x" => -1.0,
      "positions_section_background_gradient_begin_y" => -1.0,
      "positions_section_background_gradient_end_x" => 1.0,
      "positions_section_background_gradient_end_y" => 1.0,
      "projects_section_background_colors" => [0xffF37022, 0xffFFFFFF, 0xff0F823F],
      "projects_section_background_color_stops" => [0.01, 0.48, 0.95],
      "projects_section_background_gradient_begin_x" => -1.0,
      "projects_section_background_gradient_begin_y" => -1.0,
      "projects_section_background_gradient_end_x" => 1.0,
      "projects_section_background_gradient_end_y" => 1.0
    },
    37967 => {
      "circle_text_color" => 0xffFFFFFF,
      "circle_background_colors" => [0xffF37022, 0xffFFFCED, 0xff0F823F],
      "circle_background_color_stops" => [0.192, 0.4878, 0.8993],
      "circle_background_gradient_begin_x" => -1.0,
      "circle_background_gradient_begin_y" => -1.0,
      "circle_background_gradient_end_x" => 1.0,
      "circle_background_gradient_end_y" => 1.0,
      "circle_background_overlay_colors" => [0xff621D00, 0x00C4C4C4],
      "circle_background_overlay_color_stops" => [0.0, 0.9305],
      "circle_background_overlay_gradient_begin_x" => 0.0,
      "circle_background_overlay_gradient_begin_y" => -1.0,
      "circle_background_overlay_gradient_end_x" => 0.0,
      "circle_background_overlay_gradient_end_y" => 1.0,
      "circle_header_colors" => [0xffF37022, 0xffFFFFFF, 0xff0F823F],
      "circle_header_color_stops" => [0.01, 0.48, 0.95],
      "circle_header_gradient_begin_x" => -1.0,
      "circle_header_gradient_begin_y" => 1.0,
      "circle_header_gradient_end_x" => 1.0,
      "circle_header_gradient_end_y" => 1.0,
      "circle_header_text_color" => 0xff000000,
      "poster_and_badge_card_background_colors" => [0xff359B44, 0xffFFFCED, 0xffF37022],
      "poster_and_badge_card_background_color_stops" => [0.0763, 0.5295, 1.0],
      "poster_and_badge_card_bg_gradient_begin_x" => 1.0,
      "poster_and_badge_card_bg_gradient_begin_y" => 1.0,
      "poster_and_badge_card_bg_gradient_end_x" => 1.0,
      "poster_and_badge_card_bg_gradient_end_y" => -1.0,
      "poster_and_badge_card_footer_colors" => [0xff359B44, 0xff359B44],
      "poster_and_badge_card_footer_color_stops" => nil,
      "poster_and_badge_card_footer_gradient_begin_x" => nil,
      "poster_and_badge_card_footer_gradient_begin_y" => nil,
      "poster_and_badge_card_footer_gradient_end_x" => nil,
      "poster_and_badge_card_footer_gradient_end_y" => nil,
      "badge_banner_colors" => [0xff003E1A, 0xff0F823F, 0xff0F823F, 0xff003E1A],
      "badge_banner_color_stops" => [0.0, 0.2899, 0.6701, 1.0],
      "about_info_outline_color" => 0xff0F823F,
      "about_info_bg_color" => 0xffF6FFF8,
      "positions_highlight_color" => 0xff2CB565,
      "projects_bottom_line_color" => 0xffF37022,
      "projects_button_text_color" => 0xff000000,
      "positions_section_background_colors" => [0xffF6FFF8, 0xffFFFFFF],
      "positions_section_background_color_stops" => [0.0, 1.0],
      "positions_section_background_gradient_begin_x" => -1.0,
      "positions_section_background_gradient_begin_y" => -1.0,
      "positions_section_background_gradient_end_x" => 1.0,
      "positions_section_background_gradient_end_y" => 1.0,
      "projects_section_background_colors" => [0xffF37022, 0xffFFFFFF, 0xff0F823F],
      "projects_section_background_color_stops" => [0.01, 0.48, 0.95],
      "projects_section_background_gradient_begin_x" => -1.0,
      "projects_section_background_gradient_begin_y" => -1.0,
      "projects_section_background_gradient_end_x" => 1.0,
      "projects_section_background_gradient_end_y" => 1.0
    },
    31398 => {
      "circle_text_color" => 0xffFFFFFF,
      "circle_background_colors" => [0xffFFC77B, 0xffF47216, 0xffF47216],
      "circle_background_color_stops" => [0.265, 0.5345, 0.9877],
      "circle_background_gradient_begin_x" => -1.0,
      "circle_background_gradient_begin_y" => -1.0,
      "circle_background_gradient_end_x" => 1.0,
      "circle_background_gradient_end_y" => 1.0,
      "circle_background_overlay_colors" => [0xff491F00, 0xff491F00, 0xff491F00, 0x00C4C4C4],
      "circle_background_overlay_color_stops" => [0.0, 0.0, 0.001, 0.9253],
      "circle_background_overlay_gradient_begin_x" => 0.0,
      "circle_background_overlay_gradient_begin_y" => -1.0,
      "circle_background_overlay_gradient_end_x" => 0.0,
      "circle_background_overlay_gradient_end_y" => 1.0,
      "circle_header_colors" => [0xffF47216, 0xffFFC225],
      "circle_header_color_stops" => [0.34, 1.0],
      "circle_header_gradient_begin_x" => -1.0,
      "circle_header_gradient_begin_y" => 1.0,
      "circle_header_gradient_end_x" => 1.0,
      "circle_header_gradient_end_y" => 1.0,
      "circle_header_text_color" => 0xffFFFFFF,
      "poster_and_badge_card_background_colors" => [0xffF47216, 0xffFFC77B, 0xffFFC225],
      "poster_and_badge_card_background_color_stops" => [0.0, 0.4826, 1.0],
      "poster_and_badge_card_bg_gradient_begin_x" => 1.0,
      "poster_and_badge_card_bg_gradient_begin_y" => 1.0,
      "poster_and_badge_card_bg_gradient_end_x" => 1.0,
      "poster_and_badge_card_bg_gradient_end_y" => -1.0,
      "poster_and_badge_card_footer_colors" => [0xffEF6402, 0xffEF6402],
      "poster_and_badge_card_footer_color_stops" => nil,
      "poster_and_badge_card_footer_gradient_begin_x" => nil,
      "poster_and_badge_card_footer_gradient_begin_y" => nil,
      "poster_and_badge_card_footer_gradient_end_x" => nil,
      "poster_and_badge_card_footer_gradient_end_y" => nil,
      "badge_banner_colors" => [0xffAC4700, 0xffF47216, 0xffF47216, 0xffAC4700],
      "badge_banner_color_stops" => [0.0, 0.3472, 0.6441, 1.0],
      "about_info_outline_color" => 0xffF47216,
      "about_info_bg_color" => 0xffFFF8F4,
      "positions_highlight_color" => 0xffF47216,
      "projects_bottom_line_color" => 0xffF47216,
      "projects_button_text_color" => 0xffFFFFFF,
      "positions_section_background_colors" => [0xffFFF8F4, 0xffFFFFFF],
      "positions_section_background_color_stops" => [0.0, 1.0],
      "positions_section_background_gradient_begin_x" => -1.0,
      "positions_section_background_gradient_begin_y" => -1.0,
      "positions_section_background_gradient_end_x" => 1.0,
      "positions_section_background_gradient_end_y" => 1.0,
      "projects_section_background_colors" => [0xffF47216, 0xffFFC225],
      "projects_section_background_color_stops" => [0.34, 1.0],
      "projects_section_background_gradient_begin_x" => -1.0,
      "projects_section_background_gradient_begin_y" => -1.0,
      "projects_section_background_gradient_end_x" => 1.0,
      "projects_section_background_gradient_end_y" => 1.0
    },
    37788 => {
      "circle_text_color" => 0xffFFFFFF,
      "circle_background_colors" => [0xffFFC77B, 0xffF47216, 0xffF47216],
      "circle_background_color_stops" => [0.265, 0.5345, 0.9877],
      "circle_background_gradient_begin_x" => -1.0,
      "circle_background_gradient_begin_y" => -1.0,
      "circle_background_gradient_end_x" => 1.0,
      "circle_background_gradient_end_y" => 1.0,
      "circle_background_overlay_colors" => [0xff491F00, 0xff491F00, 0xff491F00, 0x00C4C4C4],
      "circle_background_overlay_color_stops" => [0.0, 0.0, 0.001, 0.9253],
      "circle_background_overlay_gradient_begin_x" => 0.0,
      "circle_background_overlay_gradient_begin_y" => -1.0,
      "circle_background_overlay_gradient_end_x" => 0.0,
      "circle_background_overlay_gradient_end_y" => 1.0,
      "circle_header_colors" => [0xffF47216, 0xffFFC225],
      "circle_header_color_stops" => [0.34, 1.0],
      "circle_header_gradient_begin_x" => -1.0,
      "circle_header_gradient_begin_y" => 1.0,
      "circle_header_gradient_end_x" => 1.0,
      "circle_header_gradient_end_y" => 1.0,
      "circle_header_text_color" => 0xffFFFFFF,
      "poster_and_badge_card_background_colors" => [0xffF47216, 0xffFFC77B, 0xffFFC225],
      "poster_and_badge_card_background_color_stops" => [0.0, 0.4826, 1.0],
      "poster_and_badge_card_bg_gradient_begin_x" => 1.0,
      "poster_and_badge_card_bg_gradient_begin_y" => 1.0,
      "poster_and_badge_card_bg_gradient_end_x" => 1.0,
      "poster_and_badge_card_bg_gradient_end_y" => -1.0,
      "poster_and_badge_card_footer_colors" => [0xffEF6402, 0xffEF6402],
      "poster_and_badge_card_footer_color_stops" => nil,
      "poster_and_badge_card_footer_gradient_begin_x" => nil,
      "poster_and_badge_card_footer_gradient_begin_y" => nil,
      "poster_and_badge_card_footer_gradient_end_x" => nil,
      "poster_and_badge_card_footer_gradient_end_y" => nil,
      "badge_banner_colors" => [0xffAC4700, 0xffF47216, 0xffF47216, 0xffAC4700],
      "badge_banner_color_stops" => [0.0, 0.3472, 0.6441, 1.0],
      "about_info_outline_color" => 0xffF47216,
      "about_info_bg_color" => 0xffFFF8F4,
      "positions_highlight_color" => 0xffF47216,
      "projects_bottom_line_color" => 0xffF47216,
      "projects_button_text_color" => 0xffFFFFFF,
      "positions_section_background_colors" => [0xffFFF8F4, 0xffFFFFFF],
      "positions_section_background_color_stops" => [0.0, 1.0],
      "positions_section_background_gradient_begin_x" => -1.0,
      "positions_section_background_gradient_begin_y" => -1.0,
      "positions_section_background_gradient_end_x" => 1.0,
      "positions_section_background_gradient_end_y" => 1.0,
      "projects_section_background_colors" => [0xffF47216, 0xffFFC225],
      "projects_section_background_color_stops" => [0.34, 1.0],
      "projects_section_background_gradient_begin_x" => -1.0,
      "projects_section_background_gradient_begin_y" => -1.0,
      "projects_section_background_gradient_end_x" => 1.0,
      "projects_section_background_gradient_end_y" => 1.0
    },
    31405 => {
      "circle_text_color" => 0xffFFFFFF,
      "circle_background_colors" => [0xffED008C, 0xffF47FC5, 0xffFBCEE9],
      "circle_background_color_stops" => [0.0, 0.3749, 0.7945],
      "circle_background_gradient_begin_x" => 1.0,
      "circle_background_gradient_begin_y" => 1.0,
      "circle_background_gradient_end_x" => -1.0,
      "circle_background_gradient_end_y" => -1.0,
      "circle_background_overlay_colors" => [0xff49002B, 0x00C4C4C4],
      "circle_background_overlay_color_stops" => [0.0, 0.9149],
      "circle_background_overlay_gradient_begin_x" => 0.0,
      "circle_background_overlay_gradient_begin_y" => -1.0,
      "circle_background_overlay_gradient_end_x" => 0.0,
      "circle_background_overlay_gradient_end_y" => 1.0,
      "circle_header_colors" => [0xffEB0288, 0xffF495BF],
      "circle_header_color_stops" => [0.0, 1.0],
      "circle_header_gradient_begin_x" => -1.0,
      "circle_header_gradient_begin_y" => 1.0,
      "circle_header_gradient_end_x" => 1.0,
      "circle_header_gradient_end_y" => 1.0,
      "circle_header_text_color" => 0xffFFFFFF,
      "poster_and_badge_card_background_colors" => [0xffF47FC5, 0xffED008C],
      "poster_and_badge_card_background_color_stops" => [0.0, 0.3695],
      "poster_and_badge_card_bg_gradient_begin_x" => 1.0,
      "poster_and_badge_card_bg_gradient_begin_y" => 1.0,
      "poster_and_badge_card_bg_gradient_end_x" => 1.0,
      "poster_and_badge_card_bg_gradient_end_y" => -1.0,
      "poster_and_badge_card_footer_colors" => [0xffF47FC5, 0xffED008C],
      "poster_and_badge_card_footer_color_stops" => nil,
      "poster_and_badge_card_footer_gradient_begin_x" => nil,
      "poster_and_badge_card_footer_gradient_begin_y" => nil,
      "poster_and_badge_card_footer_gradient_end_x" => nil,
      "poster_and_badge_card_footer_gradient_end_y" => nil,
      "badge_banner_colors" => [0xffAC0066, 0xffED008C, 0xffED008C, 0xffAC0066],
      "badge_banner_color_stops" => [0.0295, 0.3889, 0.6336, 0.9826],
      "about_info_outline_color" => 0xffEB0288,
      "about_info_bg_color" => 0xffFFF4FA,
      "positions_highlight_color" => 0xffEB0288,
      "projects_bottom_line_color" => 0xffEB0288,
      "projects_button_text_color" => 0xffFFFFFF,
      "positions_section_background_colors" => [0xffFFF4FA, 0xffFFFFFF],
      "positions_section_background_color_stops" => [0.0, 1.0],
      "positions_section_background_gradient_begin_x" => -1.0,
      "positions_section_background_gradient_begin_y" => -1.0,
      "positions_section_background_gradient_end_x" => 1.0,
      "positions_section_background_gradient_end_y" => 1.0,
      "projects_section_background_colors" => [0xffEB0288, 0xffF495BF],
      "projects_section_background_color_stops" => [0.0, 1.0],
      "projects_section_background_gradient_begin_x" => -1.0,
      "projects_section_background_gradient_begin_y" => -1.0,
      "projects_section_background_gradient_end_x" => 1.0,
      "projects_section_background_gradient_end_y" => 1.0
    }
  }

  POSTER_TAB_PARTY_GRADIENTS = {
    # YSRCP
    31403 => {
      "circle_text_color" => 0xffFFFFFF,
      "badge_text_color" => 0xffFFFFFF,
      "background_colors" => [0xff008E46, 0xff22BBB8, 0xff0266B4],
      "background_color_stops" => nil,
      "bg_gradient_begin_x" => 1.0,
      "bg_gradient_begin_y" => -1.0,
      "bg_gradient_end_x" => 1.0,
      "bg_gradient_end_y" => 1.0,
      "badge_banner_gradients" => [0xff003B6A, 0xff0266B4, 0xff0266B4, 0xff003B6A],
      "badge_banner_gradient_stops" => [0.0, 0.3368, 0.6826, 1.0],
      "badge_banner_gradient_begin_x" => -1.0,
      "badge_banner_gradient_begin_y" => 0,
      "badge_banner_gradient_end_x" => 1.0,
      "badge_banner_gradient_end_y" => 0,
      "footer_colors" => [0xff008E46, 0xff22BBB8, 0xff0266B4],
      "footer_color_stops" => nil,
      "footer_gradient_begin_x" => 1.0,
      "footer_gradient_begin_y" => 0,
      "footer_gradient_end_x" => -1.0,
      "footer_gradient_end_y" => 0,
      "neon_shadow_color" => 0xff0266B4,
      "party_icon_colors" => [0xff008E46, 0xff22BBB8, 0xff0266B4],
      "party_icon_color_stops" => nil,
      "left_party_icon_gradient_begin_x" => 0.0,
      "left_party_icon_gradient_begin_y" => -1.0,
      "left_party_icon_gradient_end_x" => 0.0,
      "left_party_icon_gradient_end_y" => 1.0,
      "top_party_icon_gradient_begin_x" => -1.0,
      "top_party_icon_gradient_begin_y" => -1.0,
      "top_party_icon_gradient_end_x" => 1.0,
      "top_party_icon_gradient_end_y" => 1.0,
      "badge_ribbon_background_gradients_colors" => [0x80003B6A, 0x800266B4, 0x8000A3FF, 0x800266B4, 0x80003B6A],
      "badge_ribbon_background_gradients_stops" => [0.0111, 0.3012, 0.5163, 0.7417, 0.9944],
      "badge_ribbon_background_gradients_begin_x" => -1.0,
      "badge_ribbon_background_gradients_begin_y" => 0.0,
      "badge_ribbon_background_gradients_end_x" => 1.0,
      "badge_ribbon_background_gradients_end_y" => 0.0,
      "identity_border_gradients_colors" => [0xFF9B7B0C, 0xFFD1A818, 0xFFF3D978, 0xFFFFF4C3, 0xFFFFFFFF, 0xFFFFF4C3, 0xFFF3D979, 0xFFD1A818, 0xFF9B7B0C],
      "identity_border_gradients_stops" => [0.0141, 0.1252, 0.3134, 0.4424, 0.5285, 0.6468, 0.7705, 0.9587, 1.0465],
      "identity_border_gradients_begin_x" => 0.0,
      "identity_border_gradients_begin_y" => -1.0,
      "identity_border_gradients_end_x" => 0.0,
      "identity_border_gradients_end_y" => 1.0,
      "shiny_primary_highlight_color" => 0xff51D5FF,
      "shiny_secondary_highlight_color" => 0xff008E46,
      "shiny_identity_border_gradients_colors" => [0xFFFFFFFF, 0xFF0266B4, 0xFF2393EA, 0xFFFFFFFF, 0xFF0266B4, 0xFFFFFFFF, 0xFF003B6A],
      "shiny_identity_border_gradients_stops" => [-0.0531, 0.2081, 0.3296, 0.4987, 0.8824, 1.0248, 1.1132],
      "shiny_identity_border_gradients_begin_x" => -1.0,
      "shiny_identity_border_gradients_begin_y" => 0.0,
      "shiny_identity_border_gradients_end_x" => 1.0,
      "shiny_identity_border_gradients_end_y" => 0.0,
      "identity_inner_background_gradients_colors" => [0xff0266B4, 0xff0266B4],
      "identity_inner_background_gradients_stops" => nil,
      "identity_inner_background_gradients_begin_x" => 1.0,
      "identity_inner_background_gradients_begin_y" => 0.0,
      "identity_inner_background_gradients_end_x" => -1.0,
      "identity_inner_background_gradients_end_y" => 0.0,
      "primary_highlight_color" => 0xff0266B4,
      "secondary_highlight_color" => 0xff0266B4,
    },
    # YSRCP DEMO
    37983 => {
      "circle_text_color" => 0xffFFFFFF,
      "badge_text_color" => 0xffFFFFFF,
      "background_colors" => [0xff008E46, 0xff22BBB8, 0xff0266B4],
      "background_color_stops" => nil,
      "bg_gradient_begin_x" => 1.0,
      "bg_gradient_begin_y" => -1.0,
      "bg_gradient_end_x" => 1.0,
      "bg_gradient_end_y" => 1.0,
      "badge_banner_gradients" => [0xff003B6A, 0xff0266B4, 0xff0266B4, 0xff003B6A],
      "badge_banner_gradient_stops" => [0.0, 0.3368, 0.6826, 1.0],
      "badge_banner_gradient_begin_x" => -1.0,
      "badge_banner_gradient_begin_y" => 0,
      "badge_banner_gradient_end_x" => 1.0,
      "badge_banner_gradient_end_y" => 0,
      "footer_colors" => [0xff008E46, 0xff22BBB8, 0xff0266B4],
      "footer_color_stops" => nil,
      "footer_gradient_begin_x" => 1.0,
      "footer_gradient_begin_y" => 0,
      "footer_gradient_end_x" => -1.0,
      "footer_gradient_end_y" => 0,
      "neon_shadow_color" => 0xff0266B4,
      "party_icon_colors" => [0xff008E46, 0xff22BBB8, 0xff0266B4],
      "party_icon_color_stops" => nil,
      "left_party_icon_gradient_begin_x" => 0.0,
      "left_party_icon_gradient_begin_y" => -1.0,
      "left_party_icon_gradient_end_x" => 0.0,
      "left_party_icon_gradient_end_y" => 1.0,
      "top_party_icon_gradient_begin_x" => -1.0,
      "top_party_icon_gradient_begin_y" => -1.0,
      "top_party_icon_gradient_end_x" => 1.0,
      "top_party_icon_gradient_end_y" => 1.0,
      "badge_ribbon_background_gradients_colors" => [0x80003B6A, 0x800266B4, 0x8000A3FF, 0x800266B4, 0x80003B6A],
      "badge_ribbon_background_gradients_stops" => [0.0111, 0.3012, 0.5163, 0.7417, 0.9944],
      "badge_ribbon_background_gradients_begin_x" => -1.0,
      "badge_ribbon_background_gradients_begin_y" => 0.0,
      "badge_ribbon_background_gradients_end_x" => 1.0,
      "badge_ribbon_background_gradients_end_y" => 0.0,
      "identity_border_gradients_colors" => [0xFF9B7B0C, 0xFFD1A818, 0xFFF3D978, 0xFFFFF4C3, 0xFFFFFFFF, 0xFFFFF4C3, 0xFFF3D979, 0xFFD1A818, 0xFF9B7B0C],
      "identity_border_gradients_stops" => [0.0141, 0.1252, 0.3134, 0.4424, 0.5285, 0.6468, 0.7705, 0.9587, 1.0465],
      "identity_border_gradients_begin_x" => 0.0,
      "identity_border_gradients_begin_y" => -1.0,
      "identity_border_gradients_end_x" => 0.0,
      "identity_border_gradients_end_y" => 1.0,
      "shiny_primary_highlight_color" => 0xff51D5FF,
      "shiny_secondary_highlight_color" => 0xff008E46,
      "shiny_identity_border_gradients_colors" => [0xFFFFFFFF, 0xFF0266B4, 0xFF2393EA, 0xFFFFFFFF, 0xFF0266B4, 0xFFFFFFFF, 0xFF003B6A],
      "shiny_identity_border_gradients_stops" => [-0.0531, 0.2081, 0.3296, 0.4987, 0.8824, 1.0248, 1.1132],
      "shiny_identity_border_gradients_begin_x" => -1.0,
      "shiny_identity_border_gradients_begin_y" => 0.0,
      "shiny_identity_border_gradients_end_x" => 1.0,
      "shiny_identity_border_gradients_end_y" => 0.0,
      "identity_inner_background_gradients_colors" => [0xff0266B4, 0xff0266B4],
      "identity_inner_background_gradients_stops" => nil,
      "identity_inner_background_gradients_begin_x" => 1.0,
      "identity_inner_background_gradients_begin_y" => 0.0,
      "identity_inner_background_gradients_end_x" => -1.0,
      "identity_inner_background_gradients_end_y" => 0.0,
      "primary_highlight_color" => 0xff0266B4,
      "secondary_highlight_color" => 0xff0266B4,
    },
    # TDP
    31402 => {
      "circle_text_color" => 0xffFFFFFF,
      "badge_text_color" => 0xffFFFFFF,
      "background_colors" => [0xFFF6BD00, 0xFFD32030],
      "background_color_stops" => [0.0603, 0.9463],
      "bg_gradient_begin_x" => -1.0,
      "bg_gradient_begin_y" => 0,
      "bg_gradient_end_x" => 1.0,
      "bg_gradient_end_y" => 0,
      "badge_banner_gradients" => [0xFFB71B28, 0xFFD32030, 0xFFB71B28],
      "badge_banner_gradient_stops" => [0.005, 0.5, 0.995],
      "badge_banner_gradient_begin_x" => -1.0,
      "badge_banner_gradient_begin_y" => 0,
      "badge_banner_gradient_end_x" => 1.0,
      "badge_banner_gradient_end_y" => 0,
      "footer_colors" => [0xFFF6BD00, 0xFFD32030],
      "footer_color_stops" => [0.0603, 0.9463],
      "footer_gradient_begin_x" => 1.0,
      "footer_gradient_begin_y" => 0.0,
      "footer_gradient_end_x" => -1.0,
      "footer_gradient_end_y" => 0.0,
      "neon_shadow_color" => 0xffFFED00,
      "party_icon_colors" => [0xFFF6BD00, 0xFFD32030],
      "party_icon_color_stops" => [0.0603, 0.9463],
      "left_party_icon_gradient_begin_x" => -1.0,
      "left_party_icon_gradient_begin_y" => 0.0,
      "left_party_icon_gradient_end_x" => 1.0,
      "left_party_icon_gradient_end_y" => 0.0,
      "top_party_icon_gradient_begin_x" => 0.0,
      "top_party_icon_gradient_begin_y" => -1.0,
      "top_party_icon_gradient_end_x" => 0.0,
      "top_party_icon_gradient_end_y" => 1.0,
      "badge_ribbon_background_gradients_colors" => [0x80FC6A02, 0x80F1E920, 0x80FFFFFF, 0x80FC6A02, 0x80F1E920],
      "badge_ribbon_background_gradients_stops" => [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
      "badge_ribbon_background_gradients_begin_x" => -1.0,
      "badge_ribbon_background_gradients_begin_y" => 0.0,
      "badge_ribbon_background_gradients_end_x" => 1.0,
      "badge_ribbon_background_gradients_end_y" => -0.0,
      "identity_border_gradients_colors" => [0xFF9B7B0C, 0xFFD1A818, 0xFFF3D978, 0xFFFFF4C3, 0xFFFFFFFF, 0xFFFFF4C3, 0xFFF3D979, 0xFFD1A818, 0xFF9B7B0C],
      "identity_border_gradients_stops" => [0.0141, 0.1252, 0.3134, 0.4424, 0.5285, 0.6468, 0.7705, 0.9587, 1.0465],
      "identity_border_gradients_begin_x" => 0.0,
      "identity_border_gradients_begin_y" => -1.0,
      "identity_border_gradients_end_x" => 0.0,
      "identity_border_gradients_end_y" => 1.0,
      "shiny_primary_highlight_color" => 0xffFCF5DE,
      "shiny_secondary_highlight_color" => 0xffCC0000,
      "shiny_identity_border_gradients_colors" => [0xFFD32030, 0xFFFFFFFF, 0xFFF37216, 0xFFFFFFFF, 0xFFF37216, 0xFFD32030, 0xFFFFFFFF],
      "shiny_identity_border_gradients_stops" => [-0.0142, 0.1858, 0.4025, 0.5791, 0.7399, 0.8310, 0.9501],
      "shiny_identity_border_gradients_begin_x" => -1.0,
      "shiny_identity_border_gradients_begin_y" => 0.0,
      "shiny_identity_border_gradients_end_x" => 1.0,
      "shiny_identity_border_gradients_end_y" => 0.0,
      "identity_inner_background_gradients_colors" => [0xffF6BD00, 0xffF6BD00],
      "identity_inner_background_gradients_stops" => nil,
      "identity_inner_background_gradients_begin_x" => 1.0,
      "identity_inner_background_gradients_begin_y" => 0.0,
      "identity_inner_background_gradients_end_x" => -1.0,
      "identity_inner_background_gradients_end_y" => 0.0,
      "primary_highlight_color" => 0xFFF6BD00,
      "secondary_highlight_color" => 0xFFD32030,
    },
    # JSP
    31406 => {
      "circle_text_color" => 0xffFFFFFF,
      "badge_text_color" => 0xffFFFFFF,
      "background_colors" => [0xffCC0000, 0xffFFB0B0, 0xffCC0000],
      "background_color_stops" => nil,
      "bg_gradient_begin_x" => -1.0,
      "bg_gradient_begin_y" => -1.0,
      "bg_gradient_end_x" => -1.0,
      "bg_gradient_end_y" => 1.0,
      "badge_banner_gradients" => [0xff540000, 0xffCC0000, 0xffCC0000, 0xff540000],
      "badge_banner_gradient_stops" => [0.0, 0.3264, 0.7118, 1.0],
      "badge_banner_gradient_begin_x" => -1.0,
      "badge_banner_gradient_begin_y" => 0,
      "badge_banner_gradient_end_x" => 1.0,
      "badge_banner_gradient_end_y" => 0,
      "footer_colors" => [0xffCC0000, 0xffFFB0B0, 0xffCC0000],
      "footer_color_stops" => nil,
      "footer_gradient_begin_x" => 1.0,
      "footer_gradient_begin_y" => 0,
      "footer_gradient_end_x" => -1.0,
      "footer_gradient_end_y" => 0,
      "neon_shadow_color" => 0xffCC0000,
      "party_icon_colors" => [0xffCC0000, 0xffFFB0B0, 0xffCC0000],
      "party_icon_color_stops" => nil,
      "left_party_icon_gradient_begin_x" => 0.0,
      "left_party_icon_gradient_begin_y" => -1.0,
      "left_party_icon_gradient_end_x" => 0.0,
      "left_party_icon_gradient_end_y" => 1.0,
      "top_party_icon_gradient_begin_x" => -1.0,
      "top_party_icon_gradient_begin_y" => -1.0,
      "top_party_icon_gradient_end_x" => 1.0,
      "top_party_icon_gradient_end_y" => 1.0,
      "badge_ribbon_background_gradients_colors" => [0x80540000, 0x80CC0000, 0x80CC0000, 0x80540000],
      "badge_ribbon_background_gradients_stops" => [-0.0603, 0.3237, 0.7772, 1.1163],
      "badge_ribbon_background_gradients_begin_x" => -1.0,
      "badge_ribbon_background_gradients_begin_y" => 0.0,
      "badge_ribbon_background_gradients_end_x" => 1.0,
      "badge_ribbon_background_gradients_end_y" => 0.0,
      "identity_border_gradients_colors" => [0xFF9B7B0C, 0xFFD1A818, 0xFFF3D978, 0xFFFFF4C3, 0xFFFFFFFF, 0xFFFFF4C3, 0xFFF3D979, 0xFFD1A818, 0xFF9B7B0C],
      "identity_border_gradients_stops" => [0.0141, 0.1252, 0.3134, 0.4424, 0.5285, 0.6468, 0.7705, 0.9587, 1.0465],
      "identity_border_gradients_begin_x" => 0.0,
      "identity_border_gradients_begin_y" => -1.0,
      "identity_border_gradients_end_x" => 0.0,
      "identity_border_gradients_end_y" => 1.0,
      "shiny_primary_highlight_color" => 0xffFF9898,
      "shiny_secondary_highlight_color" => 0xffF6A8A8,
      "shiny_identity_border_gradients_colors" => [0xFFCC0000, 0xFFFFFFFF, 0xFFCC0000, 0xFFFFFFFF, 0xFFFFB0B0, 0xFFCC0000, 0xFFFFFFFF],
      "shiny_identity_border_gradients_stops" => [0.0007, 0.0927, 0.2669, 0.4567, 0.6412, 0.7898, 0.9280,],
      "shiny_identity_border_gradients_begin_x" => -1.0,
      "shiny_identity_border_gradients_begin_y" => 0.0,
      "shiny_identity_border_gradients_end_x" => 1.0,
      "shiny_identity_border_gradients_end_y" => 0.0,
      "identity_inner_background_gradients_colors" => [0xffCC0000, 0xffCC0000],
      "identity_inner_background_gradients_stops" => nil,
      "identity_inner_background_gradients_begin_x" => 1.0,
      "identity_inner_background_gradients_begin_y" => 0.0,
      "identity_inner_background_gradients_end_x" => -1.0,
      "identity_inner_background_gradients_end_y" => 0.0,
      "primary_highlight_color" => 0xffCC0000,
      "secondary_highlight_color" => 0xffCC0000,
    },
    # INC TG
    31401 => {
      "circle_text_color" => 0xff000000,
      "badge_text_color" => 0xffFFFFFF,
      "background_colors" => [0xffF37022, 0xffFFFCED, 0xff359B44],
      "background_color_stops" => nil,
      "bg_gradient_begin_x" => -1.0,
      "bg_gradient_begin_y" => -1.0,
      "bg_gradient_end_x" => -1.0,
      "bg_gradient_end_y" => 1.0,
      "badge_banner_gradients" => [0xff003E1A, 0xff0F823F, 0xff0F823F, 0xff003E1A],
      "badge_banner_gradient_stops" => [0.0, 0.2899, 0.6701, 1.0],
      "badge_banner_gradient_begin_x" => -1.0,
      "badge_banner_gradient_begin_y" => 0,
      "badge_banner_gradient_end_x" => 1.0,
      "badge_banner_gradient_end_y" => 0,
      "footer_colors" => [0xffF37022, 0xffFFFCED, 0xff359B44],
      "footer_color_stops" => nil,
      "footer_gradient_begin_x" => 1.0,
      "footer_gradient_begin_y" => 0,
      "footer_gradient_end_x" => -1.0,
      "footer_gradient_end_y" => 0,
      "neon_shadow_color" => 0xffF37022,
      "party_icon_colors" => [0xffF37022, 0xffFFFCED, 0xff359B44],
      "party_icon_color_stops" => nil,
      "left_party_icon_gradient_begin_x" => 0.0,
      "left_party_icon_gradient_begin_y" => -1.0,
      "left_party_icon_gradient_end_x" => 0.0,
      "left_party_icon_gradient_end_y" => 1.0,
      "top_party_icon_gradient_begin_x" => -1.0,
      "top_party_icon_gradient_begin_y" => -1.0,
      "top_party_icon_gradient_end_x" => 1.0,
      "top_party_icon_gradient_end_y" => 1.0,
      "badge_ribbon_background_gradients_colors" => [0x80003E1A, 0x800F823F, 0x800F823F, 0x80003E1A],
      "badge_ribbon_background_gradients_stops" => [-0.0603, 0.2808, 0.7282, 1.1163],
      "badge_ribbon_background_gradients_begin_x" => -1.0,
      "badge_ribbon_background_gradients_begin_y" => 0.0,
      "badge_ribbon_background_gradients_end_x" => 1.0,
      "badge_ribbon_background_gradients_end_y" => -0.0,
      "identity_border_gradients_colors" => [0xFF9B7B0C, 0xFFD1A818, 0xFFF3D978, 0xFFFFF4C3, 0xFFFFFFFF, 0xFFFFF4C3, 0xFFF3D979, 0xFFD1A818, 0xFF9B7B0C],
      "identity_border_gradients_stops" => [0.0141, 0.1252, 0.3134, 0.4424, 0.5285, 0.6468, 0.7705, 0.9587, 1.0465],
      "identity_border_gradients_begin_x" => 0.0,
      "identity_border_gradients_begin_y" => -1.0,
      "identity_border_gradients_end_x" => 0.0,
      "identity_border_gradients_end_y" => 1.0,
      "shiny_primary_highlight_color" => 0xffFFEBA7,
      "shiny_secondary_highlight_color" => 0xff79FEB9,
      "shiny_identity_border_gradients_colors" => [0xFFFFFFFF, 0xFF0F823F, 0xFFFFFFFF, 0xFF0F823F, 0xFFFFFFFF, 0xFF4B775D],
      "shiny_identity_border_gradients_stops" => [0.0791, 0.1848, 0.5158, 0.7282, 0.9284, 0.9919],
      "shiny_identity_border_gradients_begin_x" => -1.0,
      "shiny_identity_border_gradients_begin_y" => 0.0,
      "shiny_identity_border_gradients_end_x" => 1.0,
      "shiny_identity_border_gradients_end_y" => 0.0,
      "identity_inner_background_gradients_colors" => [0xFFF37022, 0xFFF3FFFB, 0xFF0F823F],
      "identity_inner_background_gradients_stops" => [0.0, 0.5191, 1.0],
      "identity_inner_background_gradients_begin_x" => 1.0,
      "identity_inner_background_gradients_begin_y" => 0.0,
      "identity_inner_background_gradients_end_x" => -1.0,
      "identity_inner_background_gradients_end_y" => 0.0,
      "primary_highlight_color" => 0xff359B44,
      "secondary_highlight_color" => 0xff359B44,
    },
    # INC AP
    37967 => {
      "circle_text_color" => 0xff000000,
      "badge_text_color" => 0xffFFFFFF,
      "background_colors" => [0xffF37022, 0xffFFFCED, 0xff359B44],
      "background_color_stops" => nil,
      "bg_gradient_begin_x" => -1.0,
      "bg_gradient_begin_y" => -1.0,
      "bg_gradient_end_x" => -1.0,
      "bg_gradient_end_y" => 1.0,
      "badge_banner_gradients" => [0xff003E1A, 0xff0F823F, 0xff0F823F, 0xff003E1A],
      "badge_banner_gradient_stops" => [0.0, 0.2899, 0.6701, 1.0],
      "badge_banner_gradient_begin_x" => -1.0,
      "badge_banner_gradient_begin_y" => 0,
      "badge_banner_gradient_end_x" => 1.0,
      "badge_banner_gradient_end_y" => 0,
      "footer_colors" => [0xffF37022, 0xffFFFCED, 0xff359B44],
      "footer_color_stops" => nil,
      "footer_gradient_begin_x" => 1.0,
      "footer_gradient_begin_y" => 0,
      "footer_gradient_end_x" => -1.0,
      "footer_gradient_end_y" => 0,
      "neon_shadow_color" => 0xffF37022,
      "party_icon_colors" => [0xffF37022, 0xffFFFCED, 0xff359B44],
      "party_icon_color_stops" => nil,
      "left_party_icon_gradient_begin_x" => 0.0,
      "left_party_icon_gradient_begin_y" => -1.0,
      "left_party_icon_gradient_end_x" => 0.0,
      "left_party_icon_gradient_end_y" => 1.0,
      "top_party_icon_gradient_begin_x" => -1.0,
      "top_party_icon_gradient_begin_y" => -1.0,
      "top_party_icon_gradient_end_x" => 1.0,
      "top_party_icon_gradient_end_y" => 1.0,
      "badge_ribbon_background_gradients_colors" => [0x80003E1A, 0x800F823F, 0x800F823F, 0x80003E1A],
      "badge_ribbon_background_gradients_stops" => [-0.0603, 0.2808, 0.7282, 1.1163],
      "badge_ribbon_background_gradients_begin_x" => -1.0,
      "badge_ribbon_background_gradients_begin_y" => 0.0,
      "badge_ribbon_background_gradients_end_x" => 1.0,
      "badge_ribbon_background_gradients_end_y" => -0.0,
      "identity_border_gradients_colors" => [0xFF9B7B0C, 0xFFD1A818, 0xFFF3D978, 0xFFFFF4C3, 0xFFFFFFFF, 0xFFFFF4C3, 0xFFF3D979, 0xFFD1A818, 0xFF9B7B0C],
      "identity_border_gradients_stops" => [0.0141, 0.1252, 0.3134, 0.4424, 0.5285, 0.6468, 0.7705, 0.9587, 1.0465],
      "identity_border_gradients_begin_x" => 0.0,
      "identity_border_gradients_begin_y" => -1.0,
      "identity_border_gradients_end_x" => 0.0,
      "identity_border_gradients_end_y" => 1.0,
      "shiny_primary_highlight_color" => 0xffFFEBA7,
      "shiny_secondary_highlight_color" => 0xff79FEB9,
      "shiny_identity_border_gradients_colors" => [0xFFFFFFFF, 0xFF0F823F, 0xFFFFFFFF, 0xFF0F823F, 0xFFFFFFFF, 0xFF4B775D],
      "shiny_identity_border_gradients_stops" => [0.0791, 0.1848, 0.5158, 0.7282, 0.9284, 0.9919],
      "shiny_identity_border_gradients_begin_x" => -1.0,
      "shiny_identity_border_gradients_begin_y" => 0.0,
      "shiny_identity_border_gradients_end_x" => 1.0,
      "shiny_identity_border_gradients_end_y" => 0.0,
      "identity_inner_background_gradients_colors" => [0xFFF37022, 0xFFF3FFFB, 0xFF0F823F],
      "identity_inner_background_gradients_stops" => [0.0, 0.5191, 1.0],
      "identity_inner_background_gradients_begin_x" => 1.0,
      "identity_inner_background_gradients_begin_y" => 0.0,
      "identity_inner_background_gradients_end_x" => -1.0,
      "identity_inner_background_gradients_end_y" => 0.0,
      "primary_highlight_color" => 0xff359B44,
      "secondary_highlight_color" => 0xff359B44,
    },
    # BJP DEMO
    37982 => {
      "circle_text_color" => 0xffFFFFFF,
      "badge_text_color" => 0xffFFFFFF,
      "background_colors" => [0xffFFA724, 0xffFFC77B, 0xffF47216],
      "background_color_stops" => nil,
      "bg_gradient_begin_x" => -1.0,
      "bg_gradient_begin_y" => -1.0,
      "bg_gradient_end_x" => -1.0,
      "bg_gradient_end_y" => 1.0,
      "badge_banner_gradients" => [0xffAC4700, 0xffF47216, 0xffF47216, 0xffAC4700],
      "badge_banner_gradient_stops" => [0.0, 0.3472, 0.6441, 1.0],
      "badge_banner_gradient_begin_x" => -1.0,
      "badge_banner_gradient_begin_y" => 0,
      "badge_banner_gradient_end_x" => 1.0,
      "badge_banner_gradient_end_y" => 0,
      "footer_colors" => [0xffFFA724, 0xffFFC77B, 0xffF47216],
      "footer_color_stops" => nil,
      "footer_gradient_begin_x" => 1.0,
      "footer_gradient_begin_y" => 0,
      "footer_gradient_end_x" => -1.0,
      "footer_gradient_end_y" => 0,
      "neon_shadow_color" => 0xffFC8800,
      "party_icon_colors" => [0xffFFA724, 0xffFFC77B, 0xffF47216],
      "party_icon_color_stops" => nil,
      "left_party_icon_gradient_begin_x" => 0.0,
      "left_party_icon_gradient_begin_y" => -1.0,
      "left_party_icon_gradient_end_x" => 0.0,
      "left_party_icon_gradient_end_y" => 1.0,
      "top_party_icon_gradient_begin_x" => -1.0,
      "top_party_icon_gradient_begin_y" => -1.0,
      "top_party_icon_gradient_end_x" => 1.0,
      "top_party_icon_gradient_end_y" => 1.0,
      "badge_ribbon_background_gradients_colors" => [0x80AC4700, 0x80F47216, 0x80F47216, 0x80AC4700],
      "badge_ribbon_background_gradients_stops" => [-0.0603, 0.3482, 0.6975, 1.1163],
      "badge_ribbon_background_gradients_begin_x" => -1.0,
      "badge_ribbon_background_gradients_begin_y" => 0.0,
      "badge_ribbon_background_gradients_end_x" => 1.0,
      "badge_ribbon_background_gradients_end_y" => -0.0,
      "identity_border_gradients_colors" => [0xFF9B7B0C, 0xFFD1A818, 0xFFF3D978, 0xFFFFF4C3, 0xFFFFFFFF, 0xFFFFF4C3, 0xFFF3D979, 0xFFD1A818, 0xFF9B7B0C],
      "identity_border_gradients_stops" => [0.0141, 0.1252, 0.3134, 0.4424, 0.5285, 0.6468, 0.7705, 0.9587, 1.0465],
      "identity_border_gradients_begin_x" => 0.0,
      "identity_border_gradients_begin_y" => -1.0,
      "identity_border_gradients_end_x" => 0.0,
      "identity_border_gradients_end_y" => 1.0,
      "shiny_primary_highlight_color" => 0xffFFEFAA,
      "shiny_secondary_highlight_color" => 0xffFDC74D,
      "shiny_identity_border_gradients_colors" => [0xFFFFFFFF, 0xFFAC4700, 0xFFF47216, 0xFFFFFFFF, 0xFFF47216, 0xFFFFFFFF, 0xFFFF6F09],
      "shiny_identity_border_gradients_stops" => [0.0316, 0.167, 0.3482, 0.5174, 0.6975, 0.8838, 0.9907],
      "shiny_identity_border_gradients_begin_x" => -1.0,
      "shiny_identity_border_gradients_begin_y" => 0.0,
      "shiny_identity_border_gradients_end_x" => 1.0,
      "shiny_identity_border_gradients_end_y" => 0.0,
      "identity_inner_background_gradients_colors" => [0xffF47216, 0xffF47216],
      "identity_inner_background_gradients_stops" => nil,
      "identity_inner_background_gradients_begin_x" => 1.0,
      "identity_inner_background_gradients_begin_y" => 0.0,
      "identity_inner_background_gradients_end_x" => -1.0,
      "identity_inner_background_gradients_end_y" => 0.0,
      "primary_highlight_color" => 0xffF47216,
      "secondary_highlight_color" => 0xffF47216,
    },
    # BJP TG
    31398 => {
      "circle_text_color" => 0xffFFFFFF,
      "badge_text_color" => 0xffFFFFFF,
      "background_colors" => [0xffFFA724, 0xffFFC77B, 0xffF47216],
      "background_color_stops" => nil,
      "bg_gradient_begin_x" => -1.0,
      "bg_gradient_begin_y" => -1.0,
      "bg_gradient_end_x" => -1.0,
      "bg_gradient_end_y" => 1.0,
      "badge_banner_gradients" => [0xffAC4700, 0xffF47216, 0xffF47216, 0xffAC4700],
      "badge_banner_gradient_stops" => [0.0, 0.3472, 0.6441, 1.0],
      "badge_banner_gradient_begin_x" => -1.0,
      "badge_banner_gradient_begin_y" => 0,
      "badge_banner_gradient_end_x" => 1.0,
      "badge_banner_gradient_end_y" => 0,
      "footer_colors" => [0xffFFA724, 0xffFFC77B, 0xffF47216],
      "footer_color_stops" => nil,
      "footer_gradient_begin_x" => 1.0,
      "footer_gradient_begin_y" => 0,
      "footer_gradient_end_x" => -1.0,
      "footer_gradient_end_y" => 0,
      "neon_shadow_color" => 0xffFC8800,
      "party_icon_colors" => [0xffFFA724, 0xffFFC77B, 0xffF47216],
      "party_icon_color_stops" => nil,
      "left_party_icon_gradient_begin_x" => 0.0,
      "left_party_icon_gradient_begin_y" => -1.0,
      "left_party_icon_gradient_end_x" => 0.0,
      "left_party_icon_gradient_end_y" => 1.0,
      "top_party_icon_gradient_begin_x" => -1.0,
      "top_party_icon_gradient_begin_y" => -1.0,
      "top_party_icon_gradient_end_x" => 1.0,
      "top_party_icon_gradient_end_y" => 1.0,
      "badge_ribbon_background_gradients_colors" => [0x80AC4700, 0x80F47216, 0x80F47216, 0x80AC4700],
      "badge_ribbon_background_gradients_stops" => [-0.0603, 0.3482, 0.6975, 1.1163],
      "badge_ribbon_background_gradients_begin_x" => -1.0,
      "badge_ribbon_background_gradients_begin_y" => 0.0,
      "badge_ribbon_background_gradients_end_x" => 1.0,
      "badge_ribbon_background_gradients_end_y" => -0.0,
      "identity_border_gradients_colors" => [0xFF9B7B0C, 0xFFD1A818, 0xFFF3D978, 0xFFFFF4C3, 0xFFFFFFFF, 0xFFFFF4C3, 0xFFF3D979, 0xFFD1A818, 0xFF9B7B0C],
      "identity_border_gradients_stops" => [0.0141, 0.1252, 0.3134, 0.4424, 0.5285, 0.6468, 0.7705, 0.9587, 1.0465],
      "identity_border_gradients_begin_x" => 0.0,
      "identity_border_gradients_begin_y" => -1.0,
      "identity_border_gradients_end_x" => 0.0,
      "identity_border_gradients_end_y" => 1.0,
      "shiny_primary_highlight_color" => 0xffFFEFAA,
      "shiny_secondary_highlight_color" => 0xffFDC74D,
      "shiny_identity_border_gradients_colors" => [0xFFFFFFFF, 0xFFAC4700, 0xFFF47216, 0xFFFFFFFF, 0xFFF47216, 0xFFFFFFFF, 0xFFFF6F09],
      "shiny_identity_border_gradients_stops" => [0.0316, 0.167, 0.3482, 0.5174, 0.6975, 0.8838, 0.9907],
      "shiny_identity_border_gradients_begin_x" => -1.0,
      "shiny_identity_border_gradients_begin_y" => 0.0,
      "shiny_identity_border_gradients_end_x" => 1.0,
      "shiny_identity_border_gradients_end_y" => 0.0,
      "identity_inner_background_gradients_colors" => [0xffF47216, 0xffF47216],
      "identity_inner_background_gradients_stops" => nil,
      "identity_inner_background_gradients_begin_x" => 1.0,
      "identity_inner_background_gradients_begin_y" => 0.0,
      "identity_inner_background_gradients_end_x" => -1.0,
      "identity_inner_background_gradients_end_y" => 0.0,
      "primary_highlight_color" => 0xffF47216,
      "secondary_highlight_color" => 0xffF47216,
    },
    # BJP AP
    37788 => {
      "circle_text_color" => 0xffFFFFFF,
      "badge_text_color" => 0xffFFFFFF,
      "background_colors" => [0xffFFA724, 0xffFFC77B, 0xffF47216],
      "background_color_stops" => nil,
      "bg_gradient_begin_x" => -1.0,
      "bg_gradient_begin_y" => -1.0,
      "bg_gradient_end_x" => -1.0,
      "bg_gradient_end_y" => 1.0,
      "badge_banner_gradients" => [0xffAC4700, 0xffF47216, 0xffF47216, 0xffAC4700],
      "badge_banner_gradient_stops" => [0.0, 0.3472, 0.6441, 1.0],
      "badge_banner_gradient_begin_x" => -1.0,
      "badge_banner_gradient_begin_y" => 0,
      "badge_banner_gradient_end_x" => 1.0,
      "badge_banner_gradient_end_y" => 0,
      "footer_colors" => [0xffFFA724, 0xffFFC77B, 0xffF47216],
      "footer_color_stops" => nil,
      "footer_gradient_begin_x" => 1.0,
      "footer_gradient_begin_y" => 0,
      "footer_gradient_end_x" => -1.0,
      "footer_gradient_end_y" => 0,
      "neon_shadow_color" => 0xffFC8800,
      "party_icon_colors" => [0xffFFA724, 0xffFFC77B, 0xffF47216],
      "party_icon_color_stops" => nil,
      "left_party_icon_gradient_begin_x" => 0.0,
      "left_party_icon_gradient_begin_y" => -1.0,
      "left_party_icon_gradient_end_x" => 0.0,
      "left_party_icon_gradient_end_y" => 1.0,
      "top_party_icon_gradient_begin_x" => -1.0,
      "top_party_icon_gradient_begin_y" => -1.0,
      "top_party_icon_gradient_end_x" => 1.0,
      "top_party_icon_gradient_end_y" => 1.0,
      "badge_ribbon_background_gradients_colors" => [0x80AC4700, 0x80F47216, 0x80F47216, 0x80AC4700],
      "badge_ribbon_background_gradients_stops" => [-0.0603, 0.3482, 0.6975, 1.1163],
      "badge_ribbon_background_gradients_begin_x" => -1.0,
      "badge_ribbon_background_gradients_begin_y" => 0.0,
      "badge_ribbon_background_gradients_end_x" => 1.0,
      "badge_ribbon_background_gradients_end_y" => -0.0,
      "identity_border_gradients_colors" => [0xFF9B7B0C, 0xFFD1A818, 0xFFF3D978, 0xFFFFF4C3, 0xFFFFFFFF, 0xFFFFF4C3, 0xFFF3D979, 0xFFD1A818, 0xFF9B7B0C],
      "identity_border_gradients_stops" => [0.0141, 0.1252, 0.3134, 0.4424, 0.5285, 0.6468, 0.7705, 0.9587, 1.0465],
      "identity_border_gradients_begin_x" => 0.0,
      "identity_border_gradients_begin_y" => -1.0,
      "identity_border_gradients_end_x" => 0.0,
      "identity_border_gradients_end_y" => 1.0,
      "shiny_primary_highlight_color" => 0xffFFEFAA,
      "shiny_secondary_highlight_color" => 0xffFDC74D,
      "shiny_identity_border_gradients_colors" => [0xFFFFFFFF, 0xFFAC4700, 0xFFF47216, 0xFFFFFFFF, 0xFFF47216, 0xFFFFFFFF, 0xFFFF6F09],
      "shiny_identity_border_gradients_stops" => [0.0316, 0.167, 0.3482, 0.5174, 0.6975, 0.8838, 0.9907],
      "shiny_identity_border_gradients_begin_x" => -1.0,
      "shiny_identity_border_gradients_begin_y" => 0.0,
      "shiny_identity_border_gradients_end_x" => 1.0,
      "shiny_identity_border_gradients_end_y" => 0.0,
      "identity_inner_background_gradients_colors" => [0xffF47216, 0xffF47216],
      "identity_inner_background_gradients_stops" => nil,
      "identity_inner_background_gradients_begin_x" => 1.0,
      "identity_inner_background_gradients_begin_y" => 0.0,
      "identity_inner_background_gradients_end_x" => -1.0,
      "identity_inner_background_gradients_end_y" => 0.0,
      "primary_highlight_color" => 0xffF47216,
      "secondary_highlight_color" => 0xffF47216,
    },
    # BRS
    31405 => {
      "circle_text_color" => 0xffFFFFFF,
      "badge_text_color" => 0xffFFFFFF,
      "background_colors" => [0xffF47FC5, 0xffED008C],
      "background_color_stops" => nil,
      "bg_gradient_begin_x" => -1.0,
      "bg_gradient_begin_y" => 1.0,
      "bg_gradient_end_x" => -1.0,
      "bg_gradient_end_y" => -1.0,
      "badge_banner_gradients" => [0xffAC0066, 0xffED008C, 0xffED008C, 0xffAC0066],
      "badge_banner_gradient_stops" => [0.0295, 0.3889, 0.6336, 0.9826],
      "badge_banner_gradient_begin_x" => -1.0,
      "badge_banner_gradient_begin_y" => 0,
      "badge_banner_gradient_end_x" => 1.0,
      "badge_banner_gradient_end_y" => 0,
      "footer_colors" => [0xffF47FC5, 0xffED008C],
      "footer_color_stops" => [0.0, 0.97],
      "footer_gradient_begin_x" => 1.0,
      "footer_gradient_begin_y" => 0,
      "footer_gradient_end_x" => -1.0,
      "footer_gradient_end_y" => 0,
      "neon_shadow_color" => 0xffF47FC5,
      "party_icon_colors" => [0xffF47FC5, 0xffED008C],
      "party_icon_color_stops" => nil,
      "left_party_icon_gradient_begin_x" => 0.0,
      "left_party_icon_gradient_begin_y" => -1.0,
      "left_party_icon_gradient_end_x" => 0.0,
      "left_party_icon_gradient_end_y" => 1.0,
      "top_party_icon_gradient_begin_x" => -1.0,
      "top_party_icon_gradient_begin_y" => -1.0,
      "top_party_icon_gradient_end_x" => 1.0,
      "top_party_icon_gradient_end_y" => 1.0,
      "badge_ribbon_background_gradients_colors" => [0xFFAC0066, 0xFFED008C, 0xFFED008C, 0xFFAC0066],
      "badge_ribbon_background_gradients_stops" => [-0.0282, 0.363, 0.6295, 1.0094],
      "badge_ribbon_background_gradients_begin_x" => -1.0,
      "badge_ribbon_background_gradients_begin_y" => 0.0,
      "badge_ribbon_background_gradients_end_x" => 1.0,
      "badge_ribbon_background_gradients_end_y" => -0.0,
      "identity_border_gradients_colors" => [0xFF9B7B0C, 0xFFD1A818, 0xFFF3D978, 0xFFFFF4C3, 0xFFFFFFFF, 0xFFFFF4C3, 0xFFF3D979, 0xFFD1A818, 0xFF9B7B0C],
      "identity_border_gradients_stops" => [0.0141, 0.1252, 0.3134, 0.4424, 0.5285, 0.6468, 0.7705, 0.9587, 1.0465],
      "identity_border_gradients_begin_x" => 0.0,
      "identity_border_gradients_begin_y" => -1.0,
      "identity_border_gradients_end_x" => 0.0,
      "identity_border_gradients_end_y" => 1.0,
      "shiny_primary_highlight_color" => 0xffFBD1EA,
      "shiny_secondary_highlight_color" => 0xffDF0084,
      "shiny_identity_border_gradients_colors" => [0xFFAC0066, 0xFFFBC1E3, 0xFFED008C, 0xFFFFAFDE, 0xFFED008C, 0xFFFFAFDE, 0xFFAC0066],
      "shiny_identity_border_gradients_stops" => [-0.0149, 0.1608, 0.3309, 0.5067, 0.7052, 0.8695, 1.0094,],
      "shiny_identity_border_gradients_begin_x" => -1.0,
      "shiny_identity_border_gradients_begin_y" => 0.0,
      "shiny_identity_border_gradients_end_x" => 1.0,
      "shiny_identity_border_gradients_end_y" => 0.0,
      "identity_inner_background_gradients_colors" => [0xffF47FC5, 0xffF47FC5],
      "identity_inner_background_gradients_stops" => nil,
      "identity_inner_background_gradients_begin_x" => 1.0,
      "identity_inner_background_gradients_begin_y" => 0.0,
      "identity_inner_background_gradients_end_x" => -1.0,
      "identity_inner_background_gradients_end_y" => 0.0,
      "primary_highlight_color" => 0xffF47FC5,
      "secondary_highlight_color" => 0xffF47FC5,
    }
  }

  def get_webapp_mixpanel_embed_link
    Metadatum.where(entity: self, key: 'webapp_mixpanel_embed_link').first.try(:value)
  end

  def webapp_json
    { id: self.hashid, name: self.name, photo_url: self.photo&.url }
  end

  def create_channel
    # TODO:: ignoring circle package tenure as we didn't introduce cron to create channels and layouts for the new records
    circle_package_mappings = CirclePackageMapping.where(active: true, circle_id: id).where('start_date <= :today AND :today <= end_date', today: Time.zone.today)
    updated_circle_package = circle_package_mappings.last&.circle_package
    return if updated_circle_package.blank?
    return if !updated_circle_package.enable_channel || self.channel_conversation_type?
    unless self.update(conversation_type: :channel)
      raise "Couldn't able to create channel"
    end
  rescue => e
    errors.add(:base, e.message)
    raise ActiveRecord::Rollback
  end

  def create_circle_layout
    # TODO:: ignoring circle package tenure as we didn't introduce cron to create channels and layouts for the new records
    circle_package_mappings = CirclePackageMapping.where(active: true, circle_id: id).where('start_date <= :today AND :today <= end_date', today: Time.zone.today)
    updated_circle_package = circle_package_mappings.last&.circle_package
    return if updated_circle_package.blank?
    return if !updated_circle_package.enable_fan_posters || Circle.has_layout?(id)

    user_poster_layout = UserPosterLayout.new(entity: self, active: true)
    poster_photos = get_poster_photos.map(&:photo)
    if political_party_level?
      poster_photos.each_with_index do |photo, index|
        user_poster_layout.user_leader_photos.build(photo: photo,
                                                    header_type: :header_1,
                                                    priority: index + 1,
                                                    creator: relationship_manager)
      end
    elsif political_leader_level?
      if poster_photos.first.present?
        user_poster_layout.user_leader_photos.build(photo: poster_photos.first,
                                                    header_type: :header_1,
                                                    priority: 1,
                                                    creator: relationship_manager)
      end

      party_circle = get_leader_circle_party
      if party_circle.present?
        party_circle.get_poster_photos.map(&:photo).each_with_index do |photo, index|
          user_poster_layout.user_leader_photos.build(photo: photo,
                                                      header_type: :header_2,
                                                      priority: index + 1,
                                                      creator: relationship_manager)
        end
      end
    end

    unless user_poster_layout.save
      raise "Couldn't able to create circle layout"
    end
  rescue => e
    errors.add(:base, e.message)
    raise ActiveRecord::Rollback
  end

  def check_to_create_circle_package_mapping
    # temporarily if circle has channel and no package then create unlimited package mapping by default
    if saved_change_to_conversation_type? && conversation_type.to_sym == :channel && circle_package_mappings.blank?
      CirclePackageMapping.create!(circle_id: id, circle_package_id: Constants.unlimited_package_id,
                                   active: true, start_date: Time.zone.today, end_date: '9999-12-31')

      # update relationship manager id to 1 (<EMAIL>)
      update_column(:relationship_manager_id, 1)
    end
  rescue => e
    errors.add(:base, e.message)
    raise ActiveRecord::Rollback
  end

  def clean_up
    self.name = self.name.strip
    self.name_en = self.name_en.strip
    self.short_name = self.short_name.strip if self.short_name.present?
  end

  def sub_circle_creation_users_joining
    SubCircleCreationUsersJoinHandling.perform_async(self.id) if sub_level?
  end

  def generate_channel_preview
    # TODO:: channel disabled need to remove metadata mapping
    # if name or photo change then only generate preview and conversation_type needs to be channel
    if self.conversation_type.to_sym == :channel && (self.saved_change_to_name? ||
      self.saved_change_to_photo_id? || self.saved_change_to_conversation_type?)
      GenerateChannelPreview.perform_async(self.id)
    end
  end

  def generate_circle_preview
    # if name or photo change then only generate preview
    if self.saved_change_to_name? || self.saved_change_to_photo_id?
      GenerateCirclePreviewImage.perform_async(self.id)
    end
  end

  def update_kyc_creative
    if self.political_leader_level? && (self.saved_change_to_name? || self.saved_change_to_photo_id?)
      GenerateKycCreativeImage.perform_async(id)
    end
  end

  def eligibile_for_sub_circle_flow?
    private_level? || political_leader_level? || political_party_level? || investor_level?
  end

  def self.level_options_for(circle_type)
    case circle_type.to_sym
    when :location
      [:village, :mandal, :mla_constituency, :mp_constituency, :district, :state, :municipality, :corporation]
    when :interest
      [:political_party, :political_leader, :profession, :caste, :religion, :public_figure, :communities, :media]
    when :governing_body
      [:legislative, :executive, :judiciary]
    when :user_created
      [:private_group]
    when :my_circle
      [:private]
    when :sub
      [:sub]
    when :business
      [:real_estate, :health, :shops, :tours_travels, :education, :finance, :automobiles, :investor, :others]
    else
      []
    end
  end

  def alert_on_name_change_circle_with_roles
    if (saved_change_to_name? || saved_change_to_short_name?) && (Role.where(parent_circle_id: id).exists? || UserRole.where(parent_circle_id: id).or(UserRole.where(purview_circle_id: id)).exists?)
      Honeybadger.notify("Circle name changed for circle with roles", context: { circle_id: id, circle_name: name })
    end
  end

  def validate_circle_level_based_on_circle_type
    if self.circle_type.present? && self.level.present?
      unless Circle.level_options_for(self.circle_type).include?(self.level.to_sym)
        errors.add(:level, "is not valid for the given circle type")
      end
    end
  end

  def self.ransackable_associations(auth_object = nil)
    ["banner", "child_circles", "circle_photos", "circle_story_threads", "head_user", "parent_circle", "photo", "photos", "post_circles", "posts", "user_circle", "user_circle_permission_groups", "users", "versions"]
  end

  def validate_short_name
    # as per discussion with dheeraj, short name should not be there for circles with location circle_type
    if short_name.present? && location_circle_type?
      errors.add(:short_name, "Short name should not be there for circles with location circle_type")
    end
  end

  def validate_conversation_type_on_circle
    if conversation_type.present? && conversation_type_changed? && conversation_type.to_sym == :channel && !is_eligible_for_channel
      errors.add(:conversation_type, "circle type/level not eligible for channel")
    end
  end

  def update_conversation_type_if_nil
    if conversation_type.blank?
      self.conversation_type = :none
    end
  end

  def handle_conversation_callback_to_dm_service
    return if new_record?
    return unless conversation_type_changed?

    success = if conversation_type_changed?(to: :none)
                DmUtil.disable_circle_conversations_callback_to_dm(id)
              elsif conversation_type_changed?(to: :channel, from: :private_group)
                false
                # DmUtil.create_channel_callback_to_dm(id, get_members_count) && DmUtil.disable_private_group_callback_to_dm(id)
              elsif conversation_type_changed?(to: :private_group, from: :channel)
                false
                # DmUtil.create_private_group_callback_to_dm(id, get_members_count) && DmUtil.disable_channel_callback_to_dm(id)
              elsif conversation_type_changed?(to: :channel, from: :none)
                DmUtil.create_channel_callback_to_dm(id, get_members_count)
              elsif conversation_type_changed?(to: :private_group, from: :none)
                DmUtil.create_private_group_callback_to_dm(id, get_members_count)
              end

    if success == false
      message =
        if conversation_type.to_sym == :channel
          "Couldn't able to create channel"
        elsif conversation_type.to_sym == :none
          "Couldn't able to disable channel"
        elsif conversation_type.to_sym == :private_group
          "Couldn't able to create private group"
        end

      self.conversation_type = conversation_type_was
      errors.add(:conversation_type, message)
      throw :abort
    end
  end

  def is_eligible_for_channel
    Constants.channel_enabled_circle_levels.include? level.to_sym
  end

  def self.get_feed_item(circle_id, logged_in_user = nil)
    circle_feed_item = Rails.cache.fetch([CACHE_KEY, circle_id], expires_in: 1.month) do
      circle = self.find(circle_id)
      circle.photo.compressed_url!(size: 200) if circle.photo.present?
      {
        id: circle_id,
        name: circle.name,
        name_en: circle.name_en,
        photo: circle.photo&.photo_obj,
        type_of_organisation: circle.type_of_organisation,
        profile_photo: circle.photo&.photo_obj,
        circle_background: circle.circle_background,
        level: circle.level,
        circle_type: circle.circle_type,
        members_count: circle.members_count.to_i,
        sub_header: circle.get_sub_header
      }
    end

    circle_feed_item[:is_user_joined] = nil
    circle_feed_item[:is_user_joined] = is_user_joined(circle_id, logged_in_user.id) if logged_in_user.present?

    circle_feed_item
  end

  def get_leader_circle_party
    return nil unless self.political_leader_level?

    CirclesRelation.where(first_circle_id: self.id, active: true, relation: 'Leader2Party').first&.second_circle
  end

  def get_sub_header
    # TODO: district and state levels to be handled
    sub_header = []
    sub_header << parent_circle&.name
    sub_header << parent_circle&.parent_circle&.name if is_village_level?

    sub_header.compact.reject(&:empty?).join(', ')
  end

  def flush_cache
    Rails.cache.delete([CACHE_KEY, id])
  end

  def get_last_posted_at
    posts.last&.created_at
  end

  def get_circle_banner
    banner&.url
  end

  def update_members_count
    self.members_count = UserCircle.where(circle_id: get_all_child_circle_ids_including_itself).pluck(:user_id).uniq.count
    save!
  end

  def get_members_count
    if mandal_level? || district_level? || state_level?
      all_child_circles = get_all_child_circle_ids
      all_child_circles_members_count = 0
      if all_child_circles.present?
        all_child_circles_members_count = $redis.hmget(Constants.circle_members_count_redis_key,
                                                       all_child_circles.map { |vid| vid.to_s }).map(&:to_i).sum
      end
      members_count.to_i + all_child_circles_members_count
    else
      members_count.to_i + $redis.hget(Constants.circle_members_count_redis_key, self.id).to_i
    end
  end

  def get_badge_users_count
    # added distinct because a single user will have multiple badges.
    UserCircle.
      joins(user: :user_roles).
      where(user_roles: { active: 1 }).
      where(circle_id: get_all_child_circle_ids_including_itself).
      merge(User.active).
      distinct.
      count
  end

  def owner_or_admin?(user_id)
    admin_permission_group_id = Constants.admin_permission_group_id || PermissionGroup.where(name: 'admin').first.id
    user_circle_permission_groups.where(user_id: user_id, permission_group_id: [Constants.owner_permission_group_id, admin_permission_group_id]).exists?
  end

  def get_last_parent
    return self if parent_circle.nil?

    parent_circle.get_last_parent
  end

  def get_owner_id
    owner_permission_group_id = Constants.owner_permission_group_id
    user_circle_permission_groups.where(permission_group_id: owner_permission_group_id).first&.user_id
  end

  def get_owner
    owner_permission_group_id = Constants.owner_permission_group_id
    user_circle_permission_groups.where(permission_group_id: owner_permission_group_id).first&.user
  end

  def add_user_as_admin(user_id)
    admin_permission_group_id = PermissionGroup.where(name: 'admin').first.id
    user_circle_permission_groups.where(user_id: user_id, permission_group_id: admin_permission_group_id).first_or_create
  end

  def remove_user_as_admin(user_id)
    admin_permission_group_id = PermissionGroup.where(name: 'admin').first.id
    user_circle_permission_groups.where(user_id: user_id, permission_group_id: admin_permission_group_id).first&.destroy
  end

  def upload_creative(creative, user)
    photo = Photo.upload(
      creative,
      user.id
    )

    poster_creative = PosterCreative.new(
      creative_kind: :info,
      photo_v3: photo,
      paid: false,
      primary: false,
      h1_leader_photo_ring_type: :light,
      h2_leader_photo_ring_type: :sticker,
      active: true,
      creator: user,
      send_dm_message: true,
      send_dm_message_notification: false,
      dm_text_message: ""
    )

    poster_creative.poster_creative_circles.build(
      poster_creative_id: poster_creative.id,
      circle_id: id
    )

    if poster_creative.save!
      poster_creative
    else
      nil
    end
  end

  attribute :level_verbose

  def level_verbose
    return short_info if short_info.present?
    return '' unless level.present?

    if level == 'political_leader'
      constituency = CirclesRelation.where(second_circle: self, active: true, relation: %w[MP MLA MP\ contestant MLA\ contestant ]).first
      return "#{constituency.relation} @ #{constituency.first_circle.name}" unless constituency.nil?
    end

    level_verbose_te = {
      'Private' => 'ప్రైవేట్',
      'Village' => 'గ్రామం',
      'Mandal' => 'మండలం',
      'District' => 'జిల్లా',
      'State' => 'రాష్త్రం',
      'Public figure' => 'ప్రముఖ వ్యక్తి',
      'Political party' => 'రాజకీయ పార్టీ',
      'Political leader' => 'రాజకీయ నాయకులు',
      'Topic' => 'విషయం',
      'Municipality' => 'మున్సిపాలిటీ',
      'Mp constituency' => 'ఎంపీ కాన్స్టిట్యూఎన్సీ',
      'Mla constituency' => 'ఎమ్మెల్యే నియోజకవర్గం',
      'Corporation' => 'కార్పొరేషన్',
      'Investor' => 'Investor'
    }
    level_verbose_te[level.humanize] || ''
  end

  def self.get_user_joined_at_for_dm(circle_id, user_id)
    UserCircle.where(circle_id: circle_id, user_id: user_id).pluck(:created_at)&.first
  end

  def level_verbose_for_role_purview
    level_verbose_te = {
      'Village' => 'గ్రామ',
      'Mandal' => 'మండల',
      'Mla constituency' => 'నియోజకవర్గ',
      'Mp constituency' => 'పార్లమెంటు',
      'District' => 'జిల్లా',
      'Municipality' => 'మున్సిపాలిటీ',
      'Corporation' => 'కార్పొరేషన్',
      'State' => 'రాష్ట్ర'
    }
    level_verbose_te[level.humanize] || ''
  end

  attribute :type_of_organisation

  def type_of_organisation
    return short_info if short_info.present?
    return '' unless level.present?

    type_of_organisation_te = {
      'private' => 'ప్రైవేట్',
      'village' => 'గ్రామం',
      'mandal' => 'మండలం',
      'district' => 'జిల్లా',
      'public_figure' => 'ప్రముఖ వ్యక్తి',
      'political_party' => 'రాజకీయ పార్టీ',
      'political_leader' => 'రాజకీయ నాయకులు',
      'topic' => 'విషయం',
      'municipality' => 'మున్సిపాలిటీ',
      'corporation' => 'కార్పొరేషన్',
      'mla_constituency' => 'శాసనసభ నియోజకవర్గం',
      'mp_constituency' => 'లోక్‌సభ నియోజకవర్గం',
      'state' => 'రాష్ట్రం',
      'investor' => 'Investor'
    }
    type_of_organisation_te[level] || ''
  end

  attribute :type_verbose

  def type_verbose
    return '' if circle_type.nil?

    circle_type.humanize
  end

  def profile_photo
    photo
  end

  def circle_background
    circle_background_gradients_and_text_color = get_gradients_of_circle_background_and_text_color
    text_color = circle_background_gradients_and_text_color[:circle_text_color]
    gradients = circle_background_gradients_and_text_color[:circle_gradients]
    {
      photo_url: nil,
      text_color: text_color,
      gradients: gradients
    }
  end

  # attribute :name
  # def name
  #   if self.district_level?
  #     (name + " (జిల్లా)").encode("UTF-8", "Windows-1252")
  #   elsif self.mandal_level?
  #     (name + " (మండల్)").encode("UTF-8", "Windows-1252")
  #   end
  # end

  def get_new_posts_count(user)
    last_open = get_user_last_open(user).to_i

    if last_open > 0
      posts.where('posts.created_at > ?', DateTime.strptime(last_open.to_s, '%s')).count
    else
      0
    end
  end

  def set_user_last_open(user)
    $redis.hset("circle_last_open_#{id}", user.id.to_s, Time.now.to_i.to_s)
  end

  def get_user_last_open(user)
    $redis.hget("circle_last_open_#{id}", user.id.to_s)
  end

  def get_poster(user, app_version)
    circle_ids = [id]
    leader_circle = nil

    if app_version > Gem::Version.new('1.16.0')
      if political_party_level?
        return unless [0, self.id].include? user.affiliated_party_circle_id # to customize badge user posters in party circles
      elsif political_leader_level?
        political_party = CirclesRelation.where(first_circle_id: id, active: true, relation: 'Leader2Party').first&.second_circle
        leader_circle = self

        # to customize badge user posters in leader circles
        return unless [0, political_party&.id].compact.include? user.get_badge_affiliated_party_circle_id
      elsif is_village_level?
        mandal = self.parent_circle
        district = mandal&.parent_circle
        state_id = district&.parent_circle_id
        mla_constituency = self.class.get_mla_constituency_of_mandal(mandal&.id)
        mp_constituency_id = mla_constituency&.parent_circle_id

        all_location_ids = [mandal&.id, mla_constituency&.id, mp_constituency_id, district&.id, state_id].compact
        circle_ids += all_location_ids
      end

      circle_ids << political_party.id if political_party.present?

      # Ordered by leader, party, village, mandal ,mla_constituency, mp_constituency, district, state
      poster = Poster.joins(:circle)
                     .where("? > start_time AND ? < end_time AND posters.active is true AND posters.poster_type = 'normal' and circle_id IN (#{circle_ids.join(',')})",
                            Time.zone.now,
                            Time.zone.now)
                     .select("posters.id, posters.circle_id, posters.poster_type, CASE WHEN circles.level = 7 THEN 0 WHEN circles.level = 6 THEN 1
                      WHEN circles.level IN (1,13,14) THEN 2 WHEN circles.level = 2 THEN 3 WHEN circles.level = 9 THEN 4
                      WHEN circles.level = 8 THEN 5 WHEN circles.level = 3 THEN 6 ELSE 7 END AS circle_level_order")
                     .order("circle_level_order ASC")
                     .first
    else
      poster = Poster.where('? > start_time AND ? < end_time AND active is true AND poster_type = "normal" and circle_id = ?', Time.zone.now, Time.zone.now, id).last
    end

    poster.get_poster_hash(user, app_version, true, leader_circle) if poster.present?
  end

  def self.get_member_type_for_conversation(permission_group_symbol)
    CONVERSATIONS_MEMBERS_TYPE[permission_group_symbol.name.to_sym] || :passive
  end

  def get_badge_card_data(user, app_version)
    user_badge_card = nil
    is_eligible_for_badge_card, political_party_id = user.is_eligible_for_badge_card
    if is_eligible_for_badge_card && app_version > Gem::Version.new('1.16.0') &&
       self.id == political_party_id
      user_badge_card = user.get_badge_card(political_party_id, true)
    end
    user_badge_card
  end

  def get_badge_card_data_v1(user)
    user_badge_card = nil
    is_eligible_for_badge_card, political_party_id = user.is_eligible_for_badge_card
    if is_eligible_for_badge_card && self.id == political_party_id
      user_badge_card = user.get_badge_card(political_party_id, true)
    end
    user_badge_card
  end

  def searchable_select_name_for_sub_circles
    if village_level?
      "#{name_en} (#{level_verbose}), #{parent_circle.name_en}, #{parent_circle.parent_circle.name_en}"
    elsif mandal_level?
      "#{name_en} (#{level_verbose}), #{parent_circle.name_en}"
    else
      "#{name_en} (#{level_verbose})"
    end
  end

  # this is old now, refer get_json_v2 which is latest
  def get_json(user, app_version)
    self.photo.compressed_url!(size: 1024) if self.photo.present?
    {
      id: id,
      name: name,
      name_en: name_en,
      level: level,
      hashid: hashid,
      level_verbose: level_verbose,
      circle_type: circle_type,
      is_user_joined: check_user_joined(user),
      description: description,
      head_user: head_user,
      members_count: get_members_count,
      photo: photo,
      metadata: get_metadata,
      parents: get_all_parent_circles,
      banner: banner,
      is_fan_group: interest_circle_type? ? true : false,
      poster: get_poster(user, app_version),
      badge_card: get_badge_card_data(user, app_version),
      circle_info_url: website_url&.empty? ? nil : website_url
    }
  end

  # this is old now, refer get_json_v2 which is latest
  def get_json_v1(user, app_version, selected_feed_option = nil)
    circle_tabs = %w(posts members)
    available_circle_feed_options = Constants.get_circle_feed_options
    feed_options = [{ display_name: available_circle_feed_options[:generic], key: "generic" }]

    if [:political_party, :political_leader, :state, :district, :mla_constituency, :mp_constituency, :corporation, :municipality].include? self.level.to_sym
      if selected_feed_option == "trending" || selected_feed_option == nil
        trending_feed_position = 0
      else
        trending_feed_position = 1
      end
      feed_options.insert(trending_feed_position, { display_name: available_circle_feed_options[:trending], key:
        "trending" })
    end

    if (app_version > Gem::Version.new('1.17.2') && self.political_leader_level? && self.get_owner_id.present?) || website_url.present?
      circle_tabs << "info"
    end

    show_suggested_users_list = false
    if AppVersionSupport.is_new_suggested_users_list_enabled? && user.affiliated_party_circle_id == self.id
      show_suggested_users_list = user.is_eligible_for_interest_suggested_lists_in_members_tab
    end

    self.photo.compressed_url!(size: 1024) if self.photo.present?

    # Remove mandal circle if the circle is municipality or corporation
    parents = get_all_parent_circles((self.municipality_level? || self.corporation_level?))

    {
      id: id,
      name: name,
      name_en: name_en,
      level: level,
      hashid: hashid,
      type_of_organisation: type_of_organisation,
      circle_type: circle_type,
      is_user_joined: check_user_joined(user),
      description: description,
      members_count: get_members_count,
      profile_photo: photo,
      circle_background: circle_background,
      leader_photos: get_circle_photos,
      parents: parents,
      is_fan_group: interest_circle_type? ? true : false,
      badge_card: get_badge_card_data(user, app_version),
      circle_info_url: website_url&.empty? ? nil : website_url,
      show_suggested_users_list: show_suggested_users_list,
      circle_tabs: circle_tabs,
      circle_tag_config: user.get_circle_tag_config(id, app_version),
      feed_options: feed_options,
      conversation_type: conversation_type,
      allow_add_to_homescreen: user.is_test_user? || Constants.add_to_homescreen_enabled_circles.include?(id),
    }
  end

  def get_json_v2(user, selected_feed_option = nil)
    circle_tabs = %w(posts members)
    available_circle_feed_options = Constants.get_circle_feed_options
    feed_options = [{ display_name: available_circle_feed_options[:generic], key: "generic" }]

    if [:political_party, :political_leader, :state, :district, :mla_constituency, :mp_constituency, :corporation, :municipality].include? self.level.to_sym
      if selected_feed_option == "trending" || selected_feed_option == nil
        trending_feed_position = 0
      else
        trending_feed_position = 1
      end
      feed_options.insert(trending_feed_position, { display_name: available_circle_feed_options[:trending], key:
        "trending" })
    end

    if (self.political_leader_level? && self.get_owner_id.present?) || website_url.present?
      circle_tabs << "info"
    end

    show_suggested_users_list = false
    if user.affiliated_party_circle_id == self.id
      show_suggested_users_list = user.is_eligible_for_interest_suggested_lists_in_members_tab
    end

    self.photo.compressed_url!(size: 1024) if self.photo.present?

    # Remove mandal circle if the circle is municipality or corporation
    parents = get_all_parent_circles((self.municipality_level? || self.corporation_level?))

    {
      id: id,
      name: name,
      name_en: name_en,
      level: level,
      hashid: hashid,
      type_of_organisation: type_of_organisation,
      circle_type: circle_type,
      is_user_joined: check_user_joined(user),
      description: description,
      members_count: get_members_count,
      profile_photo: photo,
      circle_background: circle_background,
      leader_photos: get_circle_photos,
      parents: parents,
      is_fan_group: interest_circle_type? ? true : false,
      badge_card: get_badge_card_data_v1(user),
      circle_info_url: website_url&.empty? ? nil : website_url,
      show_suggested_users_list: show_suggested_users_list,
      circle_tabs: circle_tabs,
      circle_tag_config: user.get_circle_tag_config_v1(id),
      feed_options: feed_options,
      conversation_type: conversation_type,
      allow_add_to_homescreen: user.is_test_user?,
    }
  end

  def official?
    # NOTE: For now considering that if a channel is existing, it is official.
    channel_conversation_type?
  end

  def get_short_json

    biggest = Constants.biggest_circle_ids.include?(self.id)
    {
      id: id,
      name: name,
      name_en: name_en,
      level: level,
      circle_type: circle_type,
      hashid: hashid,
      members_count: get_members_count,
      photo: photo,
      description: short_info,
      verified: official?,
      biggest: biggest,
      analytics_params: {
        circle_id: id,
        circle_name: name,
        circle_name_en: name_en,
        circle_level: level,
        circle_type: circle_type,
      }
    }
  end

  def get_json_for_rm_layout_creation(poster_photos_required: true)
    {
      id: id,
      name: name,
      name_en: name_en,
      short_name: short_name,
      poster_photos: poster_photos_required ? poster_photos_json : [],
      sub_text: get_sub_text
    }
  end

  def poster_photos_json
    poster_photos.map do |poster_photo|
      {
        id: poster_photo.photo_id,
        url: poster_photo.photo.placeholder_url
      }
    end
  end

  def get_sub_text
    return nil unless political_leader_level? || political_party_level?

    if political_leader_level?
      # find through constituency relations
      location = CirclesRelation.where(second_circle: self,
                                       relation: [:MLA, :MLA_Contestant, :MP, :MP_Contestant,
                                                  :MLC, :MLC_Contestant, :MP_Rajyasabha])
                                .first&.first_circle
      # If that blank, find location via Leader2Location relation first
      if location.blank?
        location = CirclesRelation.where(first_circle: self, relation: [:Leader2Location])
                                  .first&.second_circle
      end

      # Find party affiliation
      party = CirclesRelation.where(first_circle: self, relation: [:Leader2Party])
                             .first&.second_circle
      parts = [location&.name, party&.name].compact
      parts.present? ? parts.join(" , ") : nil

    elsif political_party_level?
      # Find state for party
      location = CirclesRelation.where(first_circle: self, relation: [:Party2State])
                                .first&.second_circle
      location&.name
    end
  end

  def json_for_circle_selection_in_layout_creation
    {
      id: id,
      name: name,
      name_en: name_en,
      short_name: short_name,
    }
  end

  def get_circle_photos
    circle_photos.where(photo_type: :circle).map(&:photo).map { |p| p.url.gsub('https://cdn.thecircleapp.in/', 'https://a-cdn.thecircleapp.in/120x120/') }.compact
  end

  def suggested_users_lists(user)
    user.get_suggested_users_lists
  end

  def get_creatives_for_feed_carousel(user)
    circle_id = id
    political_party = nil
    all_location_ids = []
    if self.political_party_level?
      return unless [0, self.id].include? user.get_badge_affiliated_party_circle_id # to customize badge user posters in party circles
    elsif self.political_leader_level?
      political_party = CirclesRelation.where(first_circle_id: id, active: true, relation: 'Leader2Party').first&.second_circle

      # to customize badge user posters in leader circles
      return unless [0, political_party&.id].compact.include? user.get_badge_affiliated_party_circle_id
    elsif self.is_village_level?
      mandal = self.parent_circle
      district = mandal&.parent_circle
      state_id = district&.parent_circle_id
      mla_constituency = self.class.get_mla_constituency_of_mandal(mandal&.id)
      mp_constituency_id = mla_constituency&.parent_circle_id

      all_location_ids = [mandal&.id, mla_constituency&.id, mp_constituency_id, district&.id, state_id].compact
    end

    creatives = PosterCreative.creatives_with_events_of_circle(circle_id:)

    # If creatives are not present for leader circle level, then get creatives of leader affiliated party
    if creatives.empty? && !political_party.nil?
      creatives = PosterCreative.creatives_with_events_of_circle(circle_id: political_party.id)
    end

    # If creatives are not present for village circle, then get creatives of its parent circle.
    if creatives.blank? && !all_location_ids.blank?
      all_location_ids.each do |location_id|
        creatives = PosterCreative.creatives_with_events_of_circle(circle_id: location_id)
        break if creatives.present?
      end
    end

    creatives.map { |creative| creative.get_json(event: creative.event, category_kind: creative.creative_kind, circle_id:) }
  end

  def get_posters_feed(user, app_version)
    self.get_active_posters(user, app_version)
  end

  def get_active_posters(user, app_version)
    leader_circle = nil
    circle_id = id
    political_party = nil
    all_location_ids = []
    if self.political_party_level?
      return unless [0, self.id].include? user.get_badge_affiliated_party_circle_id # to customize badge user posters in party circles
    elsif self.political_leader_level?
      political_party = CirclesRelation.where(first_circle_id: id, active: true, relation: 'Leader2Party').first&.second_circle
      leader_circle = self

      # to customize badge user posters in leader circles
      return unless [0, political_party&.id].compact.include? user.get_badge_affiliated_party_circle_id
    elsif self.is_village_level?
      mandal = self.parent_circle
      district = mandal&.parent_circle
      state_id = district&.parent_circle_id
      mla_constituency = self.class.get_mla_constituency_of_mandal(mandal&.id)
      mp_constituency_id = mla_constituency&.parent_circle_id

      all_location_ids = [mandal&.id, mla_constituency&.id, mp_constituency_id, district&.id, state_id].compact
    end

    # for circle level posters
    posters = get_circle_posters(user, circle_id, app_version)

    # If posters are not present for leader circle level, then get posters of leader affiliated party
    if posters.empty? && !political_party.nil?
      posters = get_circle_posters(user, political_party.id, app_version)
    end

    # If posters are not present for village circle, then get posters of its parent circle.
    if posters.blank? && !all_location_ids.blank?
      all_location_ids.each do |location_id|
        posters = get_circle_posters(user, location_id, app_version)
        break if posters.present?
      end
    end

    posters = posters.map { |poster| poster.get_posters_hash_with_all_photos(user, app_version, leader_circle) }.flatten(1)
    posters if posters.count > 0
  end

  def get_circle_posters(user, circle_id, app_version)
    # framed posters should send only for users who has profile picture
    if app_version > Gem::Version.new('1.17.0') && user.has_profile_picture
      # show normal poster first half of the day, and after 3PM show frame poster first
      poster_type_order_clause = "CASE WHEN poster_type = 0 THEN 0 WHEN poster_type = 1 THEN 1 ELSE 2 END AS poster_type_order"
      if Time.zone.now.hour >= 15
        poster_type_order_clause = "CASE WHEN poster_type = 1 THEN 0 WHEN poster_type = 2 THEN 1 ELSE 2 END AS poster_type_order"
      end

      Poster.where('? > start_time AND ? < end_time AND active is true',
                   Time.zone.now,
                   Time.zone.now)
            .where(circle_id: circle_id)
            .select("posters.id, posters.circle_id, posters.poster_type, #{poster_type_order_clause}")
            .order("poster_type_order ASC, posters.id DESC")
    else
      Poster.where('? > start_time AND ? < end_time AND active is true',
                   Time.zone.now,
                   Time.zone.now).where(circle_id: circle_id, poster_type: :normal)
            .order("id DESC")
    end
  end

  def get_gradients_of_circle_background_and_text_color
    circle_id = self.political_leader_level? ?
                  CirclesRelation.where(first_circle_id: self.id, active: true, relation: 'Leader2Party').first&.second_circle_id : self.id
    party_gradients = self.get_gradients_of_circle(circle_id)

    {
      circle_gradients: {
        background_gradients: {
          colors: party_gradients["circle_background_colors"],
          stops: party_gradients["circle_background_color_stops"]
        },
        "gradient_direction": {
          begin_x: party_gradients["circle_background_gradient_begin_x"],
          begin_y: party_gradients["circle_background_gradient_begin_y"],
          end_x: party_gradients["circle_background_gradient_end_x"],
          end_y: party_gradients["circle_background_gradient_end_y"],
        },
        header_gradients: {
          colors: party_gradients["circle_header_colors"],
          stops: party_gradients["circle_header_color_stops"]
        },
        header_gradient_direction: {
          begin_x: party_gradients["circle_header_gradient_begin_x"],
          begin_y: party_gradients["circle_header_gradient_begin_y"],
          end_x: party_gradients["circle_header_gradient_end_x"],
          end_y: party_gradients["circle_header_gradient_end_y"],
        },
        overlay_gradients: {
          colors: party_gradients["circle_background_overlay_colors"],
          stops: party_gradients["circle_background_overlay_color_stops"]
        },
        overlay_gradient_direction: {
          begin_x: party_gradients["circle_background_overlay_gradient_begin_x"],
          begin_y: party_gradients["circle_background_overlay_gradient_begin_y"],
          end_x: party_gradients["circle_background_overlay_gradient_end_x"],
          end_y: party_gradients["circle_background_overlay_gradient_end_y"],
        }
      },
      circle_text_color: party_gradients["circle_header_text_color"]
    }
  end

  def get_gradients_of_poster_header

    circle_id = self.political_leader_level? ?
                  CirclesRelation.where(first_circle_id: self.id, active: true, relation: 'Leader2Party').first&.second_circle_id : self.id
    party_gradients = self.get_gradients_of_circle(circle_id)

    {
      header_gradients: {
        gradient_direction: {
          begin_x: nil,
          begin_y: nil,
          end_x: nil,
          end_y: nil,
        },
        background_gradients: {
          colors: party_gradients["badge_banner_colors"],
          stops: party_gradients["badge_banner_color_stops"]
        }
      },
      header_text_color: party_gradients["circle_text_color"]
    }
  end

  def get_gradients_of_circle(circle_id = nil)
    PARTY_GRADIENTS[circle_id] || {
      "circle_text_color" => 0xffFFFFFF,
      "circle_background_colors" => [0xffE6F0F8, 0xff8EA3B4, 0xff477292],
      "circle_background_color_stops" => [0.0, 0.6461, 1.0],
      "circle_background_gradient_begin_x" => -1.0,
      "circle_background_gradient_begin_y" => -1.0,
      "circle_background_gradient_end_x" => 1.0,
      "circle_background_gradient_end_y" => 1.0,
      "circle_background_overlay_colors" => [0xff00345A, 0x00C4C4C4],
      "circle_background_overlay_color_stops" => [0.0, 0.8524],
      "circle_background_overlay_gradient_begin_x" => 0.0,
      "circle_background_overlay_gradient_begin_y" => -1.0,
      "circle_background_overlay_gradient_end_x" => 0.0,
      "circle_background_overlay_gradient_end_y" => 1.0,
      "circle_header_colors" => [0xff477292, 0xff8EA3B4],
      "circle_header_color_stops" => [0.2014, 0.9514],
      "circle_header_gradient_begin_x" => -1.0,
      "circle_header_gradient_begin_y" => 1.0,
      "circle_header_gradient_end_x" => 1.0,
      "circle_header_gradient_end_y" => 1.0,
      "circle_header_text_color" => 0xffFFFFFF,
      "poster_and_badge_card_background_colors" => [0xff2C88CB, 0xffD6DAFF, 0xff2FAEFF, 0xff38A8FF, 0xff2698E0, 0xff1E76B0],
      "poster_and_badge_card_background_color_stops" => [0.1, 0.292, 0.47, 0.64, 0.750, 0.96],
      "poster_and_badge_card_bg_gradient_begin_x" => 1.0,
      "poster_and_badge_card_bg_gradient_begin_y" => -1.0,
      "poster_and_badge_card_bg_gradient_end_x" => -1.0,
      "poster_and_badge_card_bg_gradient_end_y" => 1.0,
      "poster_and_badge_card_footer_colors" => nil,
      "poster_and_badge_card_footer_color_stops" => nil,
      "poster_and_badge_card_footer_gradient_begin_x" => nil,
      "poster_and_badge_card_footer_gradient_begin_y" => nil,
      "poster_and_badge_card_footer_gradient_end_x" => nil,
      "poster_and_badge_card_footer_gradient_end_y" => nil,
      "badge_banner_colors" => [0xff477292, 0xff8EA3B4, 0xff477292],
      "badge_banner_color_stops" => [0.1441, 0.4741, 0.842],
      "about_info_outline_color" => 0xff477292,
      "about_info_bg_color" => 0xffF5FAFF,
      "positions_highlight_color" => 0xff8EA3B4,
      "projects_bottom_line_color" => 0xff477292,
      "projects_button_text_color" => 0xffFFFFFF,
      "positions_section_background_colors" => [0xffF5FAFF, 0xffFFFFFF],
      "positions_section_background_color_stops" => [0.0, 1.0],
      "positions_section_background_gradient_begin_x" => -1.0,
      "positions_section_background_gradient_begin_y" => -1.0,
      "positions_section_background_gradient_end_x" => 1.0,
      "positions_section_background_gradient_end_y" => 1.0,
      "projects_section_background_colors" => [0xff477292, 0xff8EA3B4],
      "projects_section_background_color_stops" => [0.2014, 0.9514],
      "projects_section_background_gradient_begin_x" => -1.0,
      "projects_section_background_gradient_begin_y" => -1.0,
      "projects_section_background_gradient_end_x" => 1.0,
      "projects_section_background_gradient_end_y" => 1.0
    }

  end

  def get_gradients_v2(user, is_posters_tab = false)
    tool_tip_color = 0xff606060
    tool_tip_text_color = 0xffFFFFFF
    circle_id = self.political_leader_level? ?
                  CirclesRelation.where(first_circle_id: self.id, active: true, relation: 'Leader2Party').first&.second_circle_id : self.id
    party_gradients = get_gradients_of_circle(circle_id)

    {
      footer_text_color: party_gradients["circle_text_color"],
      badge_text_color: party_gradients["circle_text_color"],
      gradient_direction: {
        begin_x: party_gradients["poster_and_badge_card_bg_gradient_begin_x"],
        begin_y: party_gradients["poster_and_badge_card_bg_gradient_begin_y"],
        end_x: party_gradients["poster_and_badge_card_bg_gradient_end_x"],
        end_y: party_gradients["poster_and_badge_card_bg_gradient_end_y"],
      },
      "footer_gradient_direction": {
        "begin_x": is_posters_tab ? 1.0 : party_gradients["poster_and_badge_card_footer_gradient_begin_x"],
        "begin_y": is_posters_tab ? 0.0 : party_gradients["poster_and_badge_card_footer_gradient_begin_y"],
        "end_x": is_posters_tab ? -1.0 : party_gradients["poster_and_badge_card_footer_gradient_end_x"],
        "end_y": is_posters_tab ? 0.0 : party_gradients["poster_and_badge_card_footer_gradient_end_y"]
      },
      upload_tool_tip_color: tool_tip_color,
      upload_tool_tip_text_color: tool_tip_text_color,
      background_gradients: {
        colors: party_gradients["poster_and_badge_card_background_colors"],
        stops: party_gradients["poster_and_badge_card_background_color_stops"],
      },
      "footer_gradients": {
        "colors": is_posters_tab ? party_gradients["poster_and_badge_card_background_colors"] :
                    party_gradients["poster_and_badge_card_footer_colors"],
        "stops": party_gradients["poster_and_badge_card_footer_color_stops"]
      },
      badge_banner_gradients: {
        colors: user.get_badge_role&.role&.show_badge_banner? ? party_gradients["badge_banner_colors"] : [0x00ffffff, 0x00ffffff],
        stops: user.get_badge_role&.role&.show_badge_banner? ? party_gradients["badge_banner_color_stops"] : [0, 1],
      }
    }
  end

  def self.get_party_icon_url(circle_id, color = 'WHITE')
    badge_icon_url = BadgeIconGroup.includes(:badge_icons => [:admin_medium]).where("badge_icons.color = '#{color}'
      AND badge_icon_groups.circle_id = ?", circle_id).pluck(:url).first

    return nil if badge_icon_url.blank?

    badge_icon_url.gsub(Photo::PHOTO_REGEX, 'https://a-cdn.thecircleapp.in/')
  end

  def self.get_gradients_by_circle_id(circle_id)
    POSTER_TAB_PARTY_GRADIENTS[circle_id] || {
      "circle_text_color" => 0xffFFFFFF,
      "badge_text_color" => 0xffFFFFFF,
      "background_colors" => [0xff0061FF, 0xffA1DDFF],
      "background_color_stops" => [0.0, 1.0],
      "bg_gradient_begin_x" => 0.0,
      "bg_gradient_begin_y" => -1.0,
      "bg_gradient_end_x" => 0.0,
      "bg_gradient_end_y" => 1.0,
      "badge_banner_gradients" => [0xFF003B6A, 0xFF0266B4, 0xFF0266B4, 0xFF003B6A],
      "badge_banner_gradient_stops" => [0.0475, 0.3633, 0.6758, 0.9852],
      "badge_banner_gradient_begin_x" => -1.0,
      "badge_banner_gradient_begin_y" => 0,
      "badge_banner_gradient_end_x" => 1.0,
      "badge_banner_gradient_end_y" => 0,
      "footer_colors" => [0xff0061FF, 0xffA1DDFF],
      "footer_color_stops" => nil,
      "footer_gradient_begin_x" => 1.0,
      "footer_gradient_begin_y" => 0,
      "footer_gradient_end_x" => -1.0,
      "footer_gradient_end_y" => 0,
      "neon_shadow_color" => 0xff0266B4,
      "party_icon_colors" => [0xff0061FF, 0xffA1DDFF],
      "party_icon_color_stops" => nil,
      "left_party_icon_gradient_begin_x" => 0.0,
      "left_party_icon_gradient_begin_y" => -1.0,
      "left_party_icon_gradient_end_x" => 0.0,
      "left_party_icon_gradient_end_y" => 1.0,
      "top_party_icon_gradient_begin_x" => -1.0,
      "top_party_icon_gradient_begin_y" => -1.0,
      "top_party_icon_gradient_end_x" => 1.0,
      "top_party_icon_gradient_end_y" => 1.0,
      "badge_ribbon_background_gradients_colors" => [0xFF8FE2FF, 0xFF0886FF],
      "badge_ribbon_background_gradients_stops" => [0.389, 1.0421],
      "badge_ribbon_background_gradients_begin_x" => -1.0,
      "badge_ribbon_background_gradients_begin_y" => 0.0,
      "badge_ribbon_background_gradients_end_x" => 1.0,
      "badge_ribbon_background_gradients_end_y" => 0.0,
      "identity_border_gradients_colors" => [0xFF9B7B0C, 0xFFD1A818, 0xFFF3D978, 0xFFFFF4C3, 0xFFFFFFFF, 0xFFFFF4C3, 0xFFF3D979, 0xFFD1A818, 0xFF9B7B0C],
      "identity_border_gradients_stops" => [0.0141, 0.1252, 0.3134, 0.4424, 0.5285, 0.6468, 0.7705, 0.9587, 1.0465],
      "identity_border_gradients_begin_x" => 0.0,
      "identity_border_gradients_begin_y" => -1.0,
      "identity_border_gradients_end_x" => 0.0,
      "identity_border_gradients_end_y" => 1.0,
      "shiny_primary_highlight_color" => 0xff40C0EB,
      "shiny_secondary_highlight_color" => 0xff40C0EB,
      "shiny_identity_border_gradients_colors" => [0xFFFFFFFF, 0xFF0266B4, 0xFF2393EA, 0xFFFFFFFF, 0xFF0266B4, 0xFFFFFFFF, 0xFF003B6A],
      "shiny_identity_border_gradients_stops" => [-0.0531, 0.2081, 0.3296, 0.4987, 0.8824, 1.0248, 1.1132],
      "shiny_identity_border_gradients_begin_x" => -1.0,
      "shiny_identity_border_gradients_begin_y" => 0.0,
      "shiny_identity_border_gradients_end_x" => 1.0,
      "shiny_identity_border_gradients_end_y" => 0.0,
      "identity_inner_background_gradients_colors" => [0xff0667FF, 0xff0667FF],
      "identity_inner_background_gradients_stops" => nil,
      "identity_inner_background_gradients_begin_x" => 1.0,
      "identity_inner_background_gradients_begin_y" => 0.0,
      "identity_inner_background_gradients_end_x" => -1.0,
      "identity_inner_background_gradients_end_y" => 0.0,
      "primary_highlight_color" => 0xffA1DDFF,
      "secondary_highlight_color" => 0xffA1DDFF,
    }
  end

  def get_gradients_of_circle_for_posters_tab(circle_id)
    Circle.get_gradients_by_circle_id(circle_id)
  end

  # this method is deprecated, from now onwards use badge_banner_gradients_v2
  # in the below method, we require user to check whether user has badge role or not based on that we are sending gradients
  # but in the new method, we are sending gradients based on whether user has badge role or not
  def badge_banner_gradients(user)
    party_gradients = get_gradients_of_circle_for_posters_tab(self.id)
    {
      colors: user.get_badge_role_including_unverified&.role&.show_badge_banner? ? party_gradients["badge_banner_gradients"] :
                [0x00ffffff, 0x00ffffff],
      stops: user.get_badge_role_including_unverified&.role&.show_badge_banner? ? party_gradients["badge_banner_gradient_stops"] : [0, 1],
      directions: {
        begin_x: party_gradients["badge_banner_gradient_begin_x"],
        begin_y: party_gradients["badge_banner_gradient_begin_y"],
        end_x: party_gradients["badge_banner_gradient_end_x"],
        end_y: party_gradients["badge_banner_gradient_end_y"]
      }
    }
  end

  def badge_banner_gradients_v2
    # need to fetch party gradients for political leader circle
    if self.political_leader_level?
      political_party = get_leader_circle_party
      party_gradients = get_gradients_of_circle_for_posters_tab(political_party&.id)
    else
      party_gradients = get_gradients_of_circle_for_posters_tab(self.id)
    end
    {
      colors: party_gradients["badge_banner_gradients"],
      stops: party_gradients["badge_banner_gradient_stops"],
      directions: {
        begin_x: party_gradients["badge_banner_gradient_begin_x"],
        begin_y: party_gradients["badge_banner_gradient_begin_y"],
        end_x: party_gradients["badge_banner_gradient_end_x"],
        end_y: party_gradients["badge_banner_gradient_end_y"]
      }
    }
  end

  def get_gradients_for_poster_web_tool(has_gold_border, identity_type)
    {
      footer_gradients: footer_gradients(identity_type, has_gold_border, nil),
      background_gradients: background_gradients(has_gold_border),
      h2_background_gradients: get_gradients_for_h2_background
    }
  end

  def get_gradients_for_posters_tab(user, has_gold_border, identity_type)
    party_gradients = get_gradients_of_circle_for_posters_tab(self.id)
    party_gradients_hash = {
      background_gradients: background_gradients(has_gold_border),
      badge_banner_gradients: badge_banner_gradients(user),
      badge_ribbon_background_gradients: badge_ribbon_background_gradients(identity_type, has_gold_border),
      footer_gradients: footer_gradients(identity_type, has_gold_border, user),
      identity_border_gradients: identity_border_gradients(identity_type),
      identity_inner_background_gradients: identity_inner_background_gradients(identity_type),
      inner_background_gradients: inner_background_gradients(identity_type, has_gold_border),
      party_icon_background_gradients: party_icon_background_gradients(identity_type, has_gold_border),
      h2_background_gradients: get_gradients_for_h2_background
    }

    # TODO:: try to create a separate function to fetch upper footer gradients
    # mostly upper footer gradients will be same as badge banner gradients except for gold frames
    # gold frames || gold footer gradients will have different gradients for upper footer
    if identity_type&.to_sym == :multi_color_identity
      party_gradients_hash[:upper_footer_gradients] = white_footer_gradients
    elsif identity_type&.to_sym == :polygonal_profile_identity
      party_gradients_hash[:upper_footer_gradients] = party_gradients_hash[:badge_banner_gradients]
    else
      party_gradients_hash[:upper_footer_gradients] = {
        colors: party_gradients["footer_colors"],
        stops: party_gradients["footer_color_stops"],
        directions: {
          begin_x: party_gradients["footer_gradient_begin_x"],
          begin_y: party_gradients["footer_gradient_begin_y"],
          end_x: party_gradients["footer_gradient_end_x"],
          end_y: party_gradients["footer_gradient_end_y"]
        }
      }
    end

    party_gradients_hash
  end

  def identity_border_gradients(identity_type)
    party_gradients = get_gradients_of_circle_for_posters_tab(self.id)
    if identity_type&.to_sym == :shiny_identity
      {
        colors: party_gradients["shiny_identity_border_gradients_colors"],
        stops: party_gradients["shiny_identity_border_gradients_stops"],
        directions: {
          begin_x: party_gradients["shiny_identity_border_gradients_begin_x"],
          begin_y: party_gradients["shiny_identity_border_gradients_begin_y"],
          end_x: party_gradients["shiny_identity_border_gradients_end_x"],
          end_y: party_gradients["shiny_identity_border_gradients_end_y"]
        }
      }
    elsif identity_type&.to_sym == :shiny_identity_with_low_shadow
      {
        colors: [0xFFEBEBEB, 0xFF555B5F, 0xFF9CA5AB, 0xFFFFFFFF, 0xFFAAADAF, 0xFF555B5F, 0xFFFFFFFF, 0xFFA8ABAD, 0xFF555B5F, 0xFFFFFFFF],
        stops: [0.0112, 0.0925, 0.1881, 0.3236, 0.4463, 0.5402, 0.6266, 0.6724, 0.7741, 0.9255],
        directions: {
          begin_x: -1.0,
          begin_y: 0,
          end_x: 1.0,
          end_y: 0.0
        }
      }
    else
      {
        colors: party_gradients["identity_border_gradients_colors"],
        stops: party_gradients["identity_border_gradients_stops"],
        directions: {
          begin_x: party_gradients["identity_border_gradients_begin_x"],
          begin_y: party_gradients["identity_border_gradients_begin_y"],
          end_x: party_gradients["identity_border_gradients_end_x"],
          end_y: party_gradients["identity_border_gradients_end_y"]
        }
      }
    end

  end

  def identity_inner_background_gradients(identity_type)
    party_gradients = get_gradients_of_circle_for_posters_tab(self.id)
    if identity_type&.to_sym.in?([:shiny_identity, :shiny_identity_with_low_shadow])
      {
        colors: party_gradients["identity_inner_background_gradients_colors"],
        stops: party_gradients["identity_inner_background_gradients_stops"],
        directions: {
          begin_x: party_gradients["identity_inner_background_gradients_begin_x"],
          begin_y: party_gradients["identity_inner_background_gradients_begin_y"],
          end_x: party_gradients["identity_inner_background_gradients_end_x"],
          end_y: party_gradients["identity_inner_background_gradients_end_y"]
        }
      }
    else
      {
        colors: party_gradients["background_colors"],
        stops: party_gradients["background_color_stops"],
        directions: {
          begin_x: party_gradients["bg_gradient_begin_x"],
          begin_y: party_gradients["bg_gradient_begin_y"],
          end_x: party_gradients["bg_gradient_end_x"],
          end_y: party_gradients["bg_gradient_end_y"]
        }
      }
    end
  end

  def white_footer_gradients
    {
      colors: [0xFFffffff, 0xFFffffff],
      stops: nil,
      directions: {
        begin_x: -1.0,
        begin_y: 0,
        end_x: 1.0,
        end_y: 0
      }
    }
  end

  def inner_background_gradients(identity_type, has_gold_border)
    {
      colors: [0xFFF3D978, 0xFFDD9F18, 0xFFFFF4C3, 0xFFFFF4C3, 0xFFF3D979, 0xFFDD9F18],
      stops: [0.0909, 0.1889, 0.4163, 0.5715, 0.7349, 0.8752],
      directions: {
        begin_x: -1.0,
        begin_y: 0.1,
        end_x: 1.0,
        end_y: -0.1
      }
    } if identity_type&.to_sym.in?([:curved_with_depth, :polygonal_profile_identity]) && has_gold_border

  end

  def gold_background_gradients
    {
      colors: [0xFF9B7B0C,
               0xFFD1A818,
               0xFFF3D978,
               0xFFFFF4C3,
               0xFFFFFFFF,
               0xFFFFF4C3,
               0xFFF3D979,
               0xFFD1A818,
               0xFF9B7B0C],
      stops: [0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465,],
      directions: {
        begin_x: 1.0,
        begin_y: 0.1,
        end_x: -1.0,
        end_y: -0.1
      }
    }
  end

  def background_gradients(has_gold_border)
    party_gradients = get_gradients_of_circle_for_posters_tab(self.id)
    if has_gold_border
      gold_background_gradients
    else
      {
        colors: party_gradients["background_colors"],
        stops: party_gradients["background_color_stops"],
        directions: {
          begin_x: party_gradients["bg_gradient_begin_x"],
          begin_y: party_gradients["bg_gradient_begin_y"],
          end_x: party_gradients["bg_gradient_end_x"],
          end_y: party_gradients["bg_gradient_end_y"]
        }
      }
    end
  end

  def badge_ribbon_background_gradients(identity_type, has_gold_border)
    party_gradients = get_gradients_of_circle_for_posters_tab(self.id)
    if identity_type&.to_sym == :gold_lettered_user && !has_gold_border
      nil
    elsif identity_type&.to_sym == :gold_lettered_user && has_gold_border
      {
        colors: [0xff9B7B0C, 0xffD1A818, 0xffF3D978, 0xffFFF4C3, 0xffFFFFFF, 0xffFFF4C3, 0xffF3D979, 0xffD1A818,
                 0xff9B7B0C],
        stops: [0.0141, 0.1252, 0.3134, 0.4424, 0.5285, 0.6468, 0.7705, 0.9587, 1.0465,],
        directions: {
          begin_x: -1.0,
          begin_y: 0.0,
          end_x: 1.0,
          end_y: -0.0
        }
      }
    elsif has_gold_border
      {
        colors: [0x80FC6A02, 0x80F1E920, 0x80FFFFFF, 0x80FC6A02, 0x80F1E920],
        stops: [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
        directions: {
          begin_x: -1.0,
          begin_y: 0.0,
          end_x: 1.0,
          end_y: -0.0
        }
      }
    else
      {
        colors: party_gradients["badge_ribbon_background_gradients_colors"],
        stops: party_gradients["badge_ribbon_background_gradients_stops"],
        directions: {
          begin_x: party_gradients["badge_ribbon_background_gradients_begin_x"],
          begin_y: party_gradients["badge_ribbon_background_gradients_begin_y"],
          end_x: party_gradients["badge_ribbon_background_gradients_end_x"],
          end_y: party_gradients["badge_ribbon_background_gradients_end_y"]
        }
      }
    end
  end

  def party_icon_background_gradients(identity_type, has_gold_border)
    party_gradients = get_gradients_of_circle_for_posters_tab(self.id)
    if identity_type&.to_sym.in?([:premium_cornered_party_icon_gradient_identity]) && has_gold_border
      # send party icon gradients for premium cornered party icon gradient identity
      background_gradients(!has_gold_border)
    end
  end

  def footer_gradients(identity_type, has_gold_border, user)
    party_gradients = get_gradients_of_circle_for_posters_tab(self.id)

    if identity_type&.to_sym.in?([:curved_with_depth, :party_tag_identity, :polygonal_profile_identity,
                                  :premium_cornered_party_icon_gradient_identity]) && has_gold_border
      footer_gold_gradients
    elsif identity_type&.to_sym.in?([:gold_lettered_user, :party_slogan_identity_with_party_icon,
                                     :linear_name_and_role_identity, :trapezoidal_identity,
                                     :bottom_trapezoidal_identity, :top_trapezoidal_identity,
                                     :party_slogan_identity, :plain_identity_with_party_icon])
      white_footer_gradients
    elsif identity_type&.to_sym == :flat_user && self.circle_type&.to_sym != :interest
      white_footer_gradients
    elsif identity_type&.to_sym == :shiny_identity
      {
        "colors": [0x00000000, 0x00000000, 0x00000000],
        "stops": nil,
        "directions": { "begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0 }
      }
    elsif identity_type&.to_sym.in?([:basic_transparent_identity, :basic_no_profile_pic_identity])
      transparent_gradients
    else
      {
        colors: party_gradients["footer_colors"],
        stops: party_gradients["footer_color_stops"],
        directions: {
          begin_x: party_gradients["footer_gradient_begin_x"],
          begin_y: party_gradients["footer_gradient_begin_y"],
          end_x: party_gradients["footer_gradient_end_x"],
          end_y: party_gradients["footer_gradient_end_y"]
        }
      }
    end
  end

  def transparent_gradients
    {
      colors: [0x00000000, 0x00000000, 0x00000000],
      stops: nil,
      directions: { begin_x: 1.0, begin_y: 0, end_x: -1.0, end_y: 0 }
    }
  end

  def footer_gold_gradients
    {
      colors: [0xffEEBE47, 0xffF8DB77, 0xffFFEE96, 0xffFFE8AF, 0xffFFEE96, 0xffA2621E, 0xffEEA54C, 0xffF8DB77,
               0xffEEBE47, 0xff9E791D],
      stops: [0.0, 0.0816, 0.1753, 0.2586, 0.4097, 0.5451, 0.6545, 0.8264, 0.9253, 1.0],
      directions: {
        begin_x: 1.0,
        begin_y: 0.1,
        end_x: -1.0,
        end_y: -0.1
      }
    }
  end

  def get_gradients_for_party_icon(icon_position, gold_border)
    party_gradients = get_gradients_of_circle_for_posters_tab(self.id)
    if icon_position == "left"
      directions = {
        begin_x: party_gradients["left_party_icon_gradient_begin_x"],
        begin_y: party_gradients["left_party_icon_gradient_begin_y"],
        end_x: party_gradients["left_party_icon_gradient_end_x"],
        end_y: party_gradients["left_party_icon_gradient_end_y"]
      }
    else
      directions = {
        begin_x: party_gradients["top_party_icon_gradient_begin_x"],
        begin_y: party_gradients["top_party_icon_gradient_begin_y"],
        end_x: party_gradients["top_party_icon_gradient_end_x"],
        end_y: party_gradients["top_party_icon_gradient_end_y"]
      }
    end

    party_icon_hash = {
      directions: directions
    }

    if gold_border
      party_icon_hash[:colors] = [0xFFBE6C00, 0xFFCD8E24, 0xFFDFB650, 0xFFECD36F, 0xFFF4E582, 0xFFF7EB89, 0xFFD6AE49,
                                  0xFFBE811A]
      party_icon_hash[:stops] = [0.1062, 0.1851, 0.2931, 0.3902, 0.4721, 0.5288, 0.7086, 0.8529]
      party_icon_hash[:directions] = {
        begin_x: -1.0,
        begin_y: 1.0,
        end_x: 1.0,
        end_y: -1.0 }
    else
      party_icon_hash[:colors] = party_gradients["party_icon_colors"]
      party_icon_hash[:stops] = party_gradients["party_icon_color_stops"]
    end
    party_icon_hash
  end

  def get_primary_highlight_color(identity_type = nil)
    party_gradients = get_gradients_of_circle_for_posters_tab(self.id)
    if identity_type&.to_sym.in?([:shiny_identity, :shiny_identity_with_low_shadow])
      party_gradients["shiny_primary_highlight_color"]
    else
      party_gradients["primary_highlight_color"]
    end
  end

  def get_secondary_highlight_color(identity_type = nil)
    if identity_type&.to_sym.in?([:shiny_identity])
      party_gradients = get_gradients_of_circle_for_posters_tab(self.id)
      party_gradients["shiny_secondary_highlight_color"]
    elsif identity_type&.to_sym.in?([:trapezoidal_identity, :bottom_trapezoidal_identity])
      party_gradients = get_gradients_of_circle_for_posters_tab(self.id)
      party_gradients["secondary_highlight_color"]
    end
  end

  def get_text_color
    # need to fetch party gradients for political leader circle
    if self.political_leader_level?
      political_party = get_leader_circle_party
      party_gradients = get_gradients_of_circle_for_posters_tab(political_party&.id)
    else
      party_gradients = get_gradients_of_circle_for_posters_tab(self.id)
    end
    party_gradients["circle_text_color"]
  end

  def get_badge_text_color(identity_type)
    if identity_type&.to_sym.in?([:party_slogan_identity_with_party_icon, :linear_name_and_role_identity,
                                  :trapezoidal_identity, :bottom_trapezoidal_identity, :top_trapezoidal_identity,
                                  :party_slogan_identity, :plain_identity_with_party_icon])
      0xff000000
    else
      party_gradients = get_gradients_of_circle_for_posters_tab(self.id)
      party_gradients["badge_text_color"]
    end
  end

  # optimise it later if possible
  def get_neon_frame_colors
    party_gradients = get_gradients_of_circle_for_posters_tab(self.id)
    party_gradients["neon_shadow_color"]
  end

  def get_gradients_for_h2_background
    party_gradients = get_gradients_of_circle_for_posters_tab(self.id)
    {
      colors: party_gradients["background_colors"],
      directions: {
        begin_x: party_gradients["bg_gradient_begin_x"],
        begin_y: party_gradients["bg_gradient_begin_y"],
        end_x: party_gradients["bg_gradient_end_x"],
        end_y: party_gradients["bg_gradient_end_y"]
      }
    }
  end

  def intro
    intro = "Introducing a new Circle on #{name}. Join now to get updates"
    if political_party_level?
      intro = "#{name} లేటెస్ట్ అప్డేట్స్ కొరకు, మరియు పార్టీ సభ్యులతో కనెక్ట్ అవటం కొరకు జాయిన్ అవ్వండి!"
    elsif political_leader_level?
      leader_relation = CirclesRelation.where(second_circle: self, active: true).first

      intro = ''
      intro += "#{leader_relation.relation}, #{leader_relation.first_circle.name}\n" unless leader_relation.nil?
      intro += "#{name} లేటెస్ట్ అప్డేట్స్ కొరకు, మరియు వారి ఫాలోవర్స్ తో కనెక్ట్ అవ్వటం కొరకు జాయిన్ అవ్వండి!"
    end

    intro
  end

  def self.is_user_joined(circle_id, user_id)
    UserCircle.where(circle_id: circle_id, user_id: user_id).exists?
  end

  def check_user_joined(user)
    return true if id == 0
    return true if circle_type == :location

    user.circles.include? self
  end

  def get_all_parent_circles(ignore_mandal = false)
    if ignore_mandal
      Circle.where(id: get_all_parent_circle_ids, active: true).where.not(level: :mandal).order(level: :asc).all
    else
      Circle.where(id: get_all_parent_circle_ids, active: true).order(level: :asc).all
    end
  end

  def get_all_parent_circle_ids
    Rails.cache.fetch([cache_key, 'v5', __method__], expires_in: 1.month) do
      all_parent_circle_ids = []

      unless parent_circle.nil?
        all_parent_circle_ids << parent_circle.id
        all_parent_circle_ids += parent_circle.get_all_parent_circle_ids
      end

      all_parent_circle_ids
    end
  end

  def get_all_child_circles_including_itself
    [self] + get_all_child_circles
  end

  def get_all_child_circle_ids_including_itself
    [self.id] + get_all_child_circle_ids
  end

  def get_all_child_circles
    Circle.where(id: get_all_child_circle_ids, active: true).order(level: :desc).all
  end

  def get_all_child_circle_ids
    Rails.cache.fetch([cache_key, 'v7', __method__], expires_in: 1.month) do
      all_child_circle_ids = []
      if mla_constituency_level?
        child_circles = self.class.mandals_of_mla_constituency(self.id)
      else
        child_circles = self.child_circles
      end

      child_circles.each do |child_circle|
        all_child_circle_ids << child_circle.id
        all_child_circle_ids += child_circle.get_all_child_circle_ids
      end

      all_child_circle_ids
    end
  end

  def get_all_child_circle_ids_with_given_level(level)
    Rails.cache.fetch([cache_key, 'v6', __method__, level], expires_in: 1.month) do
      all_child_circle_ids = []
      child_circles.each do |child_circle|
        all_child_circle_ids << child_circle.id if child_circle.level.to_sym == level
        all_child_circle_ids += child_circle.get_all_child_circle_ids_with_given_level(level)
      end
      all_child_circle_ids
    end
  end

  def get_all_village_circles
    Rails.cache.fetch([cache_key, 'v3', __method__], expires_in: 1.month) do
      all_village_circles = []
      if child_circles.count > 0
        child_circles.each do |child_circle|
          all_village_circles += child_circle.get_all_village_circles
        end
      else
        all_village_circles << self
      end
      all_village_circles
    end
  end

  def get_posts(user, offset, count, app_version)
    circle_ids = []

    circle_ids << id
    circle_ids += get_all_child_circle_ids

    sql = "SELECT a.entity_id, GROUP_CONCAT(a.feed_type), GROUP_CONCAT(a.timestamp), MAX(a.timestamp) FROM
      ((SELECT p.id as entity_id, 'circle_posted' as feed_type, p.created_at as timestamp
        FROM posts p
        INNER JOIN post_circles pc ON p.id = pc.post_id AND pc.active = 1
        WHERE p.active = 1 AND pc.circle_id IN (#{circle_ids.join(',')}))) as a
      group by 1
      order by 4 DESC
      limit #{offset}, #{count}"

    news_feed_records = []
    query_results = ActiveRecord::Base.connection.execute(sql)
    query_results.each do |result|
      news_feed_record = {
        id: result[0].to_i,
        timestamp: result[3]
      }

      grouped_feed_types = result[1].split(',')
      grouped_timestamps = result[2].split(',')

      index = 0
      grouped_timestamps.map do |grouped_timestamp|
        if news_feed_record[:timestamp] == grouped_timestamp
          news_feed_record[:feed_type] = grouped_feed_types[index]
          break
        end
        index += 1
      end

      news_feed_records << news_feed_record
    end

    news_feed_record_hash = {}

    news_feed_records.map do |record|
      news_feed_record_hash[record[:id]] = record
    end

    circle_posts = []

    news_feed_record_hash.each do |_index, news_feed_record|
      post = Post.find(news_feed_record[:id])
      post.is_logged_in_user_post = false
      post.feed_type = news_feed_record[:feed_type]
      post.feed_type_timestamp = Time.at(news_feed_record[:timestamp]).to_datetime
      post.user_liked = post.is_liked(user)
      post.user_circled = false # post.is_circled(user)
      post.user_seen = post.is_seen(user)
      post.preview_comments = post.get_preview_comments
      post.user.follows = post.user.id == user.id ? nil : UserFollower.where(user: post.user, follower: user).count > 0
      post.user.blocked = post.user.id == user.id ? false : BlockedUser.where(blocked_user: post.user, user: user).exists?

      current_user_badge_role = post.user.get_badge_role
      post.user.badge = current_user_badge_role&.get_json
      post.post_share_card_gradients = post.post_share_card_gradients_util(app_version)
      post.videos.each { |v| v['url'] ||= v['source_url'] }

      circle_posts << post
    end

    circle_posts
  end

  def get_users(logged_in_user, offset, count)
    circle_users = []

    if id == 0
      if offset == 0
        circle_users << logged_in_user

        count -= 1
      end

      logged_in_user.get_following(logged_in_user, offset, count).each do |user|
        user.loggedInUser = (logged_in_user.id == user.id)
        unless user.loggedInUser
          user.follows = user.get_followers(logged_in_user).map(&:id).include?(logged_in_user.id)
          user.village = user.circles.first
          user.photo&.compressed_url!(size: 200)
        end

        circle_users << user
      end

      return circle_users
    end

    circle_ids = []

    circle_ids << id
    circle_ids += get_all_child_circle_ids

    query_results = User.
      joins(:user_circles).
      where(user_circles: { circle_id: circle_ids }).
      merge(User.active).
      order(total_followers_count: :desc).
      merge(UserCircle.order(id: :asc)).
      offset(offset).
      limit(count)

    query_results.each do |user|
      user.loggedInUser = (logged_in_user.id == user.id)
      user.badge = user.get_badge_role&.get_json
      unless user.loggedInUser
        user.follows = user.get_followers(logged_in_user).map(&:id).include?(logged_in_user.id)
        user.photo&.compressed_url!(size: 200)
      end

      circle_users << user
    end

    circle_users
  end

  def mark_suggested_as_seen(user_id)
    unless $redis.hexists("suggested_circle_views_#{self.id}", user_id.to_s)
      $redis.hset("suggested_circle_views_#{self.id}", user_id.to_s, Time.now.to_i.to_s)
    end
  end

  def get_metadata
    []
  end

  def get_leader_circle_affiliated_party_id
    if political_leader_level?
      affiliated_political_party_id = CirclesRelation.where(first_circle: self, relation: "Leader2Party",
                                                            active: true).pluck(:second_circle_id).first
    else
      affiliated_political_party_id = 0
    end
    affiliated_political_party_id
  end

  def get_circle_affiliated_party_id
    affiliated_political_party_id = nil
    if political_leader_level?
      affiliated_political_party_id = CirclesRelation.where(first_circle: self, relation: "Leader2Party",
                                                            active: true).pluck(:second_circle_id).first
    elsif political_party_level?
      affiliated_political_party_id = id
    end
    affiliated_political_party_id
  end

  def self.get_mla_constituency_of_mandal(mandal_id)
    return if mandal_id.blank?

    all_relations = CirclesRelation.where(first_circle_id: mandal_id, active: true, relation: 'Mandal2MLA').to_a
    if all_relations.count == 1
      mandal_mla_constituency_relation = all_relations.first
      mandal_mla_constituency_relation&.second_circle
    end
  end

  def self.get_mp_constituency_of_mandal(mandal_id)
    get_mla_constituency_of_mandal(mandal_id)&.parent_circle
  end

  def self.get_mandal_of_mla_constituency(mla_constituency_id, force_fetch_first_mandal = false)
    return if mla_constituency_id.blank?

    all_relations = CirclesRelation.where(second_circle_id: mla_constituency_id, active: true, relation: 'Mandal2MLA').to_a
    if all_relations.count == 1 || force_fetch_first_mandal
      mandal_mla_constituency_relation = all_relations.first
      mandal_mla_constituency_relation&.first_circle
    end
  end

  # get all the mandals of a mla constituency
  def self.mandals_of_mla_constituency(mla_constituency_id)
    CirclesRelation.where(second_circle_id: mla_constituency_id, active: true, relation: 'Mandal2MLA').map(&:first_circle)
  end

  # get all the mla constituencies of mandal
  def self.mla_constituencies_of_mandal(mandal_id)
    CirclesRelation.where(first_circle_id: mandal_id, active: true, relation: 'Mandal2MLA').map(&:second_circle)
  end

  def search_entity_obj
    if political_leader_level?
      first_circle = CirclesRelation.where(second_circle: self, relation: ["MLA", "MLA Contestant", "MP",
                                                                           "MP Contestant"], active: true)
                                    .first&.first_circle
      if first_circle.present?
        case true
        when first_circle.mla_constituency_level?
          mla_constituency = first_circle
          mp_constituency_id = mla_constituency.parent_circle_id
          mandal = self.class.get_mandal_of_mla_constituency(mla_constituency.id, true)
          if mandal.present?
            district = mandal.parent_circle
            state_id = district.parent_circle_id
          end
        when first_circle.mp_constituency_level?
          mp_constituency_id = first_circle.id
        end
      end
    elsif political_party_level?
      state_id = CirclesRelation.where(first_circle: self, relation: :Party2State, active: true).first&.second_circle_id
    end

    data = {
      "name": name,
      "name_en": name_en,
      "photo_url": photo&.url,
      "members_count": get_members_count,
      "level": level,
      "circle_type": circle_type,
      "active": active,
      "affiliated_political_party_id": self.get_leader_circle_affiliated_party_id,
      "village_id": 0,
      "mandal_id": 0,
      "mla_constituency_id": mla_constituency&.id.to_i,
      "mp_constituency_id": mp_constituency_id.to_i,
      "district_id": district&.id.to_i,
      "state_id": state_id.to_i,
      "photo_id": photo_id.to_i,
      "has_poster_photo": get_poster_photos.present?,
    }

    # Only include short_name if it's present
    data["short_name"] = short_name if short_name.present?

    data
  end

  def index_for_search
    IndexSearchEntity.perform_async('circle', id)
  end

  def self.political_party_keys
    POLITICAL_PARTIES.keys
  end

  def self.party_name_related_circle_id(party_name)
    POLITICAL_PARTIES[party_name]
  end

  def check_parent_circle_id
    if self.is_village_level? || self.mandal_level? || self.district_level? || self.mla_constituency_level? || self.sub_level?
      errors.add(:parent_circle_id, "can't be blank")
    end
  end

  def check_quota_limit_of_role_in_circle(role_id)
    role = Role.find(role_id)
    if role.present? && role.absolute_quota_type?
      role_count = UserRole.where(parent_circle: self, role_id: role_id, active: true).count
      if role_count >= role.quota_value
        return false
      end

    elsif role.present? && role.percentage_quota_type?
      role_count = UserRole.where(parent_circle: self, role_id: role_id, active: true).count
      role_limit = (self.get_members_count * (role.quota_value / 100.0)).to_i
      if role_count >= role_limit
        return false
      end
    end
    true
  end

  def get_location_circle_data
    village_id, mandal_id, mla_constituency_id, mp_constituency_id, district_id, state_id = nil, nil, nil, nil, nil, nil
    case true
    when is_village_level?
      village_id = self.id
      mandal_id = self.parent_circle_id
      mla_constituency = self.class.get_mla_constituency_of_mandal(mandal_id)
      mla_constituency_id = mla_constituency&.id
      mp_constituency_id = mla_constituency&.parent_circle_id
      district_id = self.parent_circle.parent_circle_id
      state_id = self.parent_circle.parent_circle.parent_circle_id
    when mandal_level?
      mandal_id = self.id
      mla_constituency = self.class.get_mla_constituency_of_mandal(mandal_id)
      mla_constituency_id = mla_constituency&.id
      mp_constituency_id = mla_constituency&.parent_circle_id
      district_id = self.parent_circle_id
      state_id = self.parent_circle.parent_circle_id
    when mla_constituency_level?
      mandal = self.class.get_mandal_of_mla_constituency(self.id, true)
      mla_constituency_id = self.id
      mp_constituency_id = self.parent_circle_id
      district_id = mandal.parent_circle_id if mandal.present?
      state_id = mandal.parent_circle.parent_circle_id if mandal.present?
    when mp_constituency_level?
      mp_constituency_id = self.id
      state_id = get_state_id(self)
    when district_level?
      district_id = self.id
      state_id = self.parent_circle_id
    when state_level?
      state_id = self.id
    end
    {
      "village_id" => village_id,
      "mandal_id" => mandal_id,
      "mla_constituency_id" => mla_constituency_id,
      "mp_constituency_id" => mp_constituency_id,
      "district_id" => district_id,
      "state_id" => state_id
    }.compact
  end

  def can_send_with_notification
    return false if circle_monthly_usage.blank?
    return true if circle_monthly_usage.channel_notification_limit.blank?
    circle_monthly_usage.channel_notification_usage < circle_monthly_usage.channel_notification_limit
  end

  def self.get_admin_options(user, post_id, circle_ids, app_version)
    return nil unless app_version > Gem::Version.new('1.16.3')
    admin_options = []

    circle_ids.each do |cid|
      user_permissions = user.get_all_circle_permissions(cid)

      next if user_permissions.blank?
      user_permissions.each do |permission|
        permission_data = PermissionGroup.get_permission_data_for_post(permission, cid, user, post_id)
        admin_options << permission_data if permission_data.present?
      end
    end
    return nil if admin_options.blank?
    {
      header_text: "అడ్మిన్ ఆప్షన్స్",
      "options": admin_options
    }
  end

  def is_village_level?
    Constants.village_levels.include? self.level.to_sym
  end

  def get_state_id(mp_constituency)
    mla_constituency_id = mp_constituency.child_circles.first.id
    mandal = self.class.get_mandal_of_mla_constituency(mla_constituency_id, true)
    mandal.parent_circle.parent_circle_id if mandal.present?
  end

  def check_circle_level_to_perform_auto_actions
    Circle::CIRCLE_LEVELS_WITH_AUTO_FOLLOW_OWNER.include? self.level.to_sym
  end

  def self.get_state_ids
    Circle.where(level: :state, active: true).pluck(:id)
  end

  # Returning from method as both share texts are same
  def get_about_page_share_text(leader_circle)
    link = "https://prajaapp.sng.link/A3x5b/8mt6?_dl=praja%3A%2F%2Fcircles/#{leader_circle.id}&_ddl=praja%3A%2F%2Fcircles/#{leader_circle.id}"
    link = Singular.shorten_link(link)

    share_text = "మన #{leader_circle.name} సర్కిల్ ఇప్పుడు *Praja App* లో ఉంది." + "\n" +
                 "*#{leader_circle.name}* సంబంధించి *బయోడేటా* మరియు *పూర్తి విషయాలు* తెలుసుకోండి." + "\n" +
                 "మరి ఎందుకు ఆలస్యం నేను *మెంబర్* అయ్యాను. మీరు కూడా వెంటనే *ఉచితంగా మెంబర్* అవ్వండి. 👇👇" + "\n" + link

    share_text
  end

  def get_owner_all_positions
    owner_user = self.get_owner_info
    party_circle = self.get_leader_circle_party

    all_owners_roles = owner_user.user_roles
                                 .joins(:role)
                                 .where(roles: { has_badge: true })
                                 .where.not("start_date > ?", Date.current)
                                 .order("end_date DESC, roles.grade_level ASC")

    header = "నాయకత్వ పదవులు"

    get_party_gradients = get_gradients_of_circle(party_circle&.id)
    gradients = {
      "background_gradients": {
        "colors": get_party_gradients["positions_section_background_colors"],
        "stops": get_party_gradients["positions_section_background_color_stops"]
      },
      "gradient_direction": {
        "begin_x": get_party_gradients["positions_section_background_gradient_begin_x"],
        "begin_y": get_party_gradients["positions_section_background_gradient_begin_y"],
        "end_x": get_party_gradients["positions_section_background_gradient_end_x"],
        "end_y": get_party_gradients["positions_section_background_gradient_end_y"]
      }
    }

    all_positions_hash_list = get_user_roles_hash_list(all_owners_roles, party_circle.id)

    get_positions_json =
      {
        header: header,
        positions: all_positions_hash_list,
        gradients: gradients,
      }

    get_positions_json
  end

  def get_owner_info
    owner_id = self.get_owner_id
    return nil if owner_id.blank?

    user = User.get_feed_item(owner_id)
    user
  end

  def get_about_page_info
    owner_user = self.get_owner_info
    party_circle = self.get_leader_circle_party

    return {} if owner_user.blank?

    about_leader_info = self.get_about_leader_info(owner_user, self, party_circle)
    positions_info = self.get_top_positions_info(owner_user, party_circle)
    projects_section = self.get_projects_info(owner_user, self, party_circle)

    about_page_info =
      {
        "about_leader": about_leader_info,
        "positions_section": positions_info,
        "projects_section": projects_section
      }

    about_page_info.compact!
    about_page_info
  end

  def get_about_leader_info(owner_user, leader_circle, party_circle)
    details = []

    if owner_user.birth_place_id.present?
      details << { title: "పుట్టిన ప్రదేశం", content: owner_user.birth_place.name, click_url: "praja-app://buzz.praja.app/circles/#{owner_user.birth_place_id}" }
    end

    if owner_user.dob.present?
      details << { title: "పుట్టిన రోజు", content: owner_user.get_birth_day }
    end

    if owner_user.education.present?
      details << { title: "విద్య", content: owner_user.education }
    end

    user_active_role_constituency_name = owner_user.get_active_role_constituency_name
    if user_active_role_constituency_name.present?
      details << { title: "నియోజకవర్గం", content: user_active_role_constituency_name }
    end

    if owner_user.office_address.present?
      details << { title: "కార్యాలయ చిరునామా", content: owner_user.office_address, "click_url": "https://www.google.com/maps?q= #{owner_user.office_address}" }
    end

    if owner_user.contact_email.present?
      details << { title: "కాంటాక్ట్", content: owner_user.contact_email, "click_url": "mailto:#{owner_user.contact_email}" }
    end

    share_text = get_about_page_share_text(leader_circle)

    get_party_gradients = get_gradients_of_circle(party_circle&.id)

    about_leader =
      {
        header: "లీడర్ గురించి",
        outline_color: get_party_gradients["about_info_outline_color"],
        background_color: get_party_gradients["about_info_bg_color"],
        user: owner_user,
        party_circle: party_circle,
        share_text: share_text,
        details: details
      }

    about_leader[:independent_text] = "Independent Member" if party_circle.blank?
    about_leader
  end

  def get_top_positions_info(owner_user, party_circle)
    top_user_roles = owner_user.user_roles
                               .joins(:role)
                               .where(user_roles: { show_on_about_page: true }, roles: { has_badge: true })
                               .where.not("start_date > ?", Date.current)
                               .order("end_date DESC, roles.grade_level ASC")
                               .limit(5)

    top_user_roles_list = get_user_roles_hash_list(top_user_roles, party_circle&.id)

    show_view_all_button = owner_user.user_roles.count > 5

    get_party_gradients = get_gradients_of_circle(party_circle&.id)

    if top_user_roles_list.present?
      positions_info = {
        header: "నాయకత్వ పదవులు",
        show_view_all_button: show_view_all_button,
        view_all_button_text: "అన్ని పదవులను చూడండి",
        "positions": top_user_roles_list,
        "gradients": {
          "background_gradients": {
            "colors": get_party_gradients["positions_section_background_colors"],
            "stops": get_party_gradients["positions_section_background_color_stops"]
          },
          "gradient_direction": {
            "begin_x": get_party_gradients["positions_section_background_gradient_begin_x"],
            "begin_y": get_party_gradients["positions_section_background_gradient_begin_y"],
            "end_x": get_party_gradients["positions_section_background_gradient_end_x"],
            "end_y": get_party_gradients["positions_section_background_gradient_end_y"]
          }
        }
      }

      positions_info
    end
  end

  def get_user_roles_hash_list(user_roles, party_circle_id)
    owner_roles_list = []

    user_roles.each do |user_role|
      current_date = Date.current
      current_role = current_date.between?(user_role.start_date, user_role.end_date)

      get_party_gradients = get_gradients_of_circle(party_circle_id)
      highlight_color = current_role ? get_party_gradients["positions_highlight_color"] : 0xffCDCDCD

      start_date = user_role.start_date
      end_date = user_role.end_date
      user_role_start_year = start_date.strftime("%Y")
      user_role_end_year = current_role ? "ప్రస్తుతం" : end_date.strftime("%Y")

      position_hash =
        {
          "title": user_role.role.name,
          "start_date": user_role_start_year,
          "end_date": user_role_end_year,
          "highlight_color": highlight_color
        }

      if user_role.parent_circle&.governing_body_circle_type? || user_role.role.parent_circle&.governing_body_circle_type?
        position_hash[:title] = user_role.role.name
        position_hash[:sub_title] = user_role.purview_circle.name
      elsif user_role.parent_circle&.interest_circle_type? || user_role.role.parent_circle&.interest_circle_type?
        position_hash[:title] = user_role.role.name
        position_hash[:title] += " #{user_role.purview_circle.name}" if user_role.purview_circle.present?
        position_hash[:sub_title] = user_role.parent_circle&.name || user_role.role&.parent_circle&.name
      end
      owner_roles_list << position_hash
    end

    owner_roles_list
  end

  def get_projects_info(owner_user, leader_circle, party_circle)
    owner_projects = LeaderProject.where(user_id: owner_user.id, active: true).limit(5)

    get_party_gradients = get_gradients_of_circle(party_circle&.id)

    owner_projects_list = []
    owner_projects.each do |project|
      project.user.badge = project.user_role.get_json

      project_hash =
        {
          title: project.title,
          body: project.body,
          photo: project.photo_url,
          user: project.user
        }

      owner_projects_list << project_hash
    end

    if owner_projects_list.present?
      share_text = get_about_page_share_text(leader_circle)

      projects_info =
        {
          header: "ప్రాజెక్టులు & పథకాలు",
          share_text: share_text,
          buttons_text_color: get_party_gradients["projects_button_text_color"],
          bottom_line_color: get_party_gradients["projects_bottom_line_color"],
          projects: owner_projects_list,
          gradients: {
            "background_gradients": {
              "colors": get_party_gradients["projects_section_background_colors"],
              "stops": get_party_gradients["projects_section_background_color_stops"]
            },
            "gradient_direction": {
              "begin_x": get_party_gradients["projects_section_background_gradient_begin_x"],
              "begin_y": get_party_gradients["projects_section_background_gradient_begin_y"],
              "end_x": get_party_gradients["projects_section_background_gradient_end_x"],
              "end_y": get_party_gradients["projects_section_background_gradient_end_y"]
            }
          }
        }

      projects_info
    end
  end

  def self.get_state_level_leader_ids
    CirclesRelation.where(active: true, relation: :Leader2State)
                   .distinct.pluck(:first_circle_id)
  end

  def leader_photos_for_premium_posters
    header_2_photos = []
    circle_photo_urls = get_circle_photos
    layout = "layout_0_#{circle_photo_urls.count}"
    layout_details = UserPosterLayout::LAYOUTS[layout.to_sym]
    photos_count = 0
    circle_photo_urls.each do |photo_url|
      photo_details = layout_details[:photos][photos_count]
      photo_hash = {
        radius: photo_details[:radius],
        position_x: photo_details[:position_x],
        position_y: photo_details[:position_y],
      }

      photo_hash[:radius] = (photo_hash[:radius] * 4.0) / 5.0
      photo_hash[:position_y] = (photo_hash[:position_y] * 4.0) / 5.0

      photo_hash[:photo_url] = photo_url
      header_2_photos << photo_hash
      photos_count += 1
    end
    header_2_photos
  end

  def get_circle_name_for_description
    if short_name.present?
      self.short_name
    else
      self.name
    end
  end

  def sponsorship_json
    # Note:: here we are considering that icon_url is mandatory for sponsorship so we are not handling the case when
    # icon_url is nil
    profile_photo_url = photo.url
    line_1 = 'స్పాన్సర్'
    line_2 = name
    {
      icon_url: profile_photo_url,
      line_1: line_1,
      line_2: line_2,
      line_2_text_color: get_text_color,
      gradients: badge_banner_gradients_v2
    }
  end

  def self.has_active_layout?(id)
    UserPosterLayout.where(entity_type: :Circle, entity_id: id, active: true).exists?
  end

  def self.has_layout?(id)
    UserPosterLayout.where(entity_type: :Circle, entity_id: id).exists?
  end

  def can_send_post_message
    return false if circle_monthly_usage.blank?
    return true if circle_monthly_usage.channel_message_limit.blank?
    circle_monthly_usage.channel_message_usage < circle_monthly_usage.channel_message_limit
  end

  def can_send_poster_message
    return false if circle_monthly_usage.blank?
    return true if circle_monthly_usage.fan_posters_limit.blank?
    circle_monthly_usage.fan_posters_usage < circle_monthly_usage.fan_posters_limit
  end

  def update_post_message_usage
    return if circle_monthly_usage.blank?
    circle_monthly_usage.increment!(:channel_message_usage, 1)
  end

  def update_fan_poster_message_usage
    return if circle_monthly_usage.blank?
    circle_monthly_usage.increment!(:fan_posters_usage, 1)
  end

  def update_notification_usage
    return if circle_monthly_usage.blank?
    circle_monthly_usage.increment!(:channel_notification_usage, 1)
  end

  def get_layout_and_header_photos_circle(entity:)
    user_poster_layout = entity.get_circle_poster_layout

    if user_poster_layout.blank?
      return [nil, [], []]
    end

    user_leader_photos = UserLeaderPhoto.where(user_poster_layout_id: user_poster_layout.id).order(:header_type, :priority)

    header_1_photos = []
    header_2_photos = []
    layout = "layout_#{user_poster_layout.h1_count}_#{user_poster_layout.h2_count}"
    layout_details = UserPosterLayout::LAYOUTS[layout.to_sym]
    photos_count = 0
    user_leader_photos.each do |user_leader_photo|
      photo_details = layout_details[:photos][photos_count]
      photo_hash = {
        radius: photo_details[:radius] * 4.0 / 5.0,
        position_x: photo_details[:position_x],
        position_y: photo_details[:position_y] * 4.0 / 5.0,
      }

      if user_leader_photo.header_type == 'header_1'
        photo_hash[:photo_url] = user_leader_photo.photo.compressed_url(size: 200)
        header_1_photos << photo_hash
      elsif user_leader_photo.header_type == 'header_2'
        photo_hash[:photo_url] = user_leader_photo.photo.compressed_url(size: 200)
        header_2_photos << photo_hash
      end
      photos_count += 1
    end
    [layout, header_1_photos, header_2_photos]
  end

  def get_common_hash_for_layout_and_layout_variation_circle(entity:)
    layout, header_1_photos, header_2_photos = self.get_layout_and_header_photos_circle(entity: entity)
    [layout, {
      header_1_photos: header_1_photos,
      header_2_photos: header_2_photos
    }]
  end

  def get_creative_metadata(creative_id:)
    app_icon_url = 'https://a-cdn.thecircleapp.in/512x512/filters:quality(80)/production/admin-media/32/42ac9710-4924-4dc4-a262-90d96ac2c9a0.jpg'
    page_title = "ప్రత్యేక పోస్టర్ | #{name} | Praja App"
    page_description = 'లేటెస్ట్ మరియు ప్రత్యేక పోస్టర్లు పొందండి'
    creative = PosterCreative.find(creative_id)
    page_image = Metadatum.where(entity: creative, key: 'creative_link_preview_image_url').first&.value
    # apply quality transform to use a smaller image
    page_image = Capture.apply_img_transform(page_image, width: 600, height: 315) if page_image.present?
    # fallback to app icon url if there is no preview image
    page_image = app_icon_url if page_image.blank?

    {
      page_image: page_image,
      page_title: page_title,
      page_description: page_description
    }
  end

  LEVEL_HIERARCHY = {
    'village' => 'mandal',
    'mandal' => 'district',
    'district' => 'state',
    'corporation' => 'mandal',
    'municipality' => 'mandal',
    'mla_constituency' => 'mp_constituency'
  }

  def check_parent_circle_id_level
    case self.level
    when 'mp_constituency', 'state'
      if parent_circle_id.present?
        errors.add(:parent_circle_id, "should be nil for #{self.level}")
        return
      end
    end

    expected_parent_level = LEVEL_HIERARCHY[level]
    if expected_parent_level && parent_circle
      unless parent_circle.level == expected_parent_level
        errors.add(:parent_circle_id, "must be a #{expected_parent_level} for a #{level} level circle")
      end
    elsif expected_parent_level
      errors.add(:parent_circle_id, "is required to have a parent circle of level #{expected_parent_level}")
    end
  end

  def self.get_fan_poster_request_json(circle_id)
    {
      circle_id: circle_id,
      cta_text: "పోస్టర్ రిక్వెస్ట్ పంపండి",
      requested_cta_text: 'రిక్వెస్ట్ పంపబడింది!',
    }
  end

  def self.has_owner?
    UserCirclePermissionGroup.where(circle_id: circle_id, permission_group_id: Constants.owner_permission_group_id)
                             .exists?
  end

  def self.suggesting_circles_of_user(user_id)
    user = User.find_by(id: user_id)
    return [] if user.blank?

    suggested_circle_ids = []

    add_party_filter = ''

    if user.affiliated_party_circle_id.present?
      add_party_filter = "AND (relation = 'Leader2Party' AND second_circle_id = '#{user.affiliated_party_circle_id}')"
    end

    mla_constituency_ids = Circle.joins("INNER JOIN circles_relations ON circles_relations.first_circle_id = circles.id")
                                 .where(circles: { parent_circle_id: user.district_id },
                                        circles_relations: { relation: 'Mandal2MLA', active: true })
                                 .pluck('circles_relations.second_circle_id')

    mp_constituency_ids = Circle.where(id: mla_constituency_ids).pluck(:parent_circle_id)

    district_leader_ids = CirclesRelation.where(active: true)
                                         .where(relation: [:MLA, :MLA_Contestant, :MP, :MP_Contestant, :MLC, :MLC_Contestant])
                                         .where(first_circle_id: mla_constituency_ids + mp_constituency_ids + [user.district_id])
                                         .pluck(:second_circle_id, :relation)
                                         .sort_by { |id, relation|
                                           case relation
                                           when 'MLA', 'MLA_Contestant' then 1
                                           when 'MP', 'MP_Contestant' then 2
                                           when 'MLC', 'MLC_Contestant' then 3
                                           else 4
                                           end
                                         }.map(&:first)

    other_leader_ids = CirclesRelation.where(active: true)
                                      .where("(relation = :leader2location AND second_circle_id = :mandal_id) OR
                                              (relation = :leader2state AND second_circle_id = :state_id) #{add_party_filter}",
                                             { leader2location: 'Leader2Location', mandal_id: user.mandal_id,
                                               leader2state: 'Leader2State', state_id: user.state_id })
                                      .distinct
                                      .pluck(:first_circle_id)

    suggested_circle_ids += district_leader_ids
    suggested_circle_ids += other_leader_ids

    suggested_circle_ids.uniq!
    circles = Circle.joins(:circle_photos)
                    .where(id: suggested_circle_ids)
                    .where(circle_photos: { photo_type: :poster })
                    .order(Arel.sql("FIELD(circles.id, #{suggested_circle_ids.join(',')})"))
                    .distinct

    circles.map { |circle| { id: circle.id, name: circle.name, name_en: circle.name_en } }
  end

  def get_poster_photos
    circle_photos.where(photo_type: :poster).order(photo_order: :asc)
  end

  def get_circle_poster_layout
    @circle_poster_layout ||= UserPosterLayout.where(entity: self, active: true).last
  end
end

