require 'sidekiq-scheduler'

class CronForSendingProfessionIdsToMixpanel
  include Sidekiq::Worker

  def perform
    start_time = Time.zone.now
    start_id = $redis.get(Constants.user_professions_table_last_processed_id_key).to_i

    records = UserProfession.where('id > ?', start_id)
                            .limit(10000)
                            .pluck(:id, :user_id)

    return if records.empty?

    end_id = records.last[0]
    $redis.set(Constants.user_professions_table_last_processed_id_key, end_id)

    user_ids = records.map { |record| record[1] }

    user_ids.each_slice(500) do |batch|
      batch.each do |user_id|
        SyncMixpanelUser.perform_async(user_id)
      end
      sleep(1)
    end

    elapsed_time = Time.zone.now - start_time
    Rails.logger.warn("Mixpanel sync triggered for users and End ID is: #{end_id} and #{elapsed_time}")
  end
end
