# frozen_string_literal: true

class Constants
  def self.get_admin_host
    return 'https://preprod-api.thecircleapp.in' if Rails.env.production? && ENV['DEPLOYMENT'] == 'preprod'
    return 'https://www.thecircleapp.in' if Rails.env.production?
    return 'http://localhost:3000'
  end

  def self.get_api_host
    return 'https://preprod-api.thecircleapp.in' if Rails.env.production? && ENV['DEPLOYMENT'] == 'preprod'
    return 'https://api.thecircleapp.in' if Rails.env.production?
    return 'http://localhost:3000'
  end

  # owner permission group id as constant for prod & dev and test
  def self.owner_permission_group_id
    if Rails.env.production?
      return 3
    else
      return 1
    end
  end

  def self.get_firebase_url
    "https://www.google-analytics.com/mp/collect"
  end

  def self.admin_permission_group_id
    if Rails.env.production?
      return 2
    else
      return nil
    end
  end

  def self.grace_period_given_string
    'grace_period_given'
  end

  def self.grace_period_given_string_expired
    'grace_period_given_expired'
  end

  def self.grace_period_given_count_string
    'grace_period_given_count'
  end

  def self.disable_suggested_lists_key
    "disable_suggested_lists"
  end

  def self.circle_members_count_redis_key
    'circle_members_count'
  end

  def self.trending_feed_cache_key
    'trending_feed_v4'
  end

  def self.jwt_access_token_header
    'X-Access-Token'
  end

  def self.no_of_days_post_to_be_retrieve_for_feed
    3
  end

  def self.no_of_days_post_to_be_indexed
    4
  end

  def self.default_video_thumbnail_url
    'https://a-cdn.thecircleapp.in/assets/static-video-thumbnail-2.jpg'
  end

  def self.get_transparent_badge_icon_url
    'https://a-cdn.thecircleapp.in/assets/trans.png'
  end

  def self.get_user_post_media_upload_urls
    return {
      photo: 'https://media-service-preprod-api.thecircleapp.in/photos/upload',
      video: 'https://media-service-preprod-api.thecircleapp.in/videos/upload'
    } if Rails.env.production? && ENV['DEPLOYMENT'] == 'preprod'

    {
      photo: 'https://media-service-api.thecircleapp.in/photos/upload',
      video: 'https://media-service-api.thecircleapp.in/videos/upload'
    }
  end

  def self.get_photo_bg_removal_url
    return 'https://media-service-preprod-api.thecircleapp.in/photos/remove-bg-upload' if Rails.env.production? && ENV['DEPLOYMENT'] == 'preprod'
    'https://media-service-api.thecircleapp.in/photos/remove-bg-upload'
  end

  def self.get_photo_bg_removal_url_for_internal_services
    "#{media_service_callbacks_baseurl}/photos/remove-bg-upload"
  end

  def self.get_media_service_capture_upload_url
    # added development url temporarily to avoid 401 errors in prod media service
    if Rails.env.development?
      'https://localhost:3002/capture/upload'
    else
      self.media_service_callbacks_baseurl + '/capture/upload'
    end
  end

  def self.support_request_callback_url
    "#{get_api_host}/request-support-callback"
  end

  def self.extend_plan_url_for_cancel_flow
    Constants.get_api_host + '/extend-subscription?source=cancellation_flow&duration_in_days=' + UserPlanExtension::CANCELLATION_FLOW_EXTENSION_DURATION_IN_DAYS.to_s
  end

  def self.praja_account_phone
    9_999_999_999
  end

  def self.app_store_account_phone
    3_210_987_654
  end

  def self.pg_account_phone
    3_210_987_653
  end

  def self.praja_account_user_id
    if Rails.env.production?
      41
    else
      User.select(:id).find_by_phone(praja_account_phone).id
    end
  end

  def self.get_circle_feed_options
    {
      trending: 'ట్రెండింగ్',
      generic: 'కొత్త పోస్ట్స్'
    }
  end

  def self.should_show_contacts_redis_key(user_id)
    "should_show_contacts_screen_#{user_id}"
  end

  def self.max_contact_screen_seen_limit
    2
  end

  def self.contact_screen_shown_count(user_id)
    "contact_screen_shown_count_#{user_id}"
  end

  def self.last_login_time_key
    'last_login_at'
  end

  def self.last_contacts_uploaded_time_key
    'last_contacts_uploaded_at'
  end

  def self.core_user_ids_with_reduced_weightages
    # Added TV5 News, TV2 News
    Rails.env.production? ? [1046254, 1046668] : []
  end

  def self.users_batch_size_for_dm_batch_api
    100
  end

  def self.village_levels
    [:village, :municipality, :corporation]
  end

  def self.get_is_poster_photo_key_for_post
    'is_poster_photo_post'
  end

  def self.get_test_users_redis_key
    'test_user_ids'
  end

  def self.get_subscription_banner_to_be_enabled_trial_users_key
    "subscription_banner_to_be_enabled_trial_user_ids"
  end

  # Returns the key for the list of user IDs who have blocked the given user.
  #
  # @param user_id [Integer] the ID of the user being blocked
  # @return [String] the key for the list of user IDs who have blocked the given user
  def self.get_user_blocked_by_ids_key(user_id)
    "user_blocked_by_ids_#{user_id}"
  end

  def self.get_user_blocked_ids_key(user_id)
    "user_blocked_ids_#{user_id}"
  end

  def self.get_trend_feedback_eligibility_redis_key(user_id)
    "user_#{user_id}_trend_feedback_eligibility"
  end

  # Returns the number of days after which the blocked users cache will expire.
  #
  # @return [Integer] the number of days after which the blocked users cache will expire
  def self.blocked_users_cache_expiry_in_days
    30
  end

  def self.get_garuda_base_url
    if Rails.env.production?
      'http://garuda-api.garuda.svc.cluster.local:8080'
    else
      'http://garuda-app:8080'
    end
  end

  def self.get_dm_url
    if Rails.env.development?
      'http://praja-messaging-server:3001'
    else
      'http://messaging-service.messaging-service.svc.cluster.local:3000'
    end
  end

  def self.conversation_types
    { none: 'none', channel: 'channel', private_group: 'private_group' }
  end

  def self.get_poster_product_id
    1
  end

  def self.get_play_store_deep_link
    'market://details?id=buzz.praja.app'
  end

  def self.get_app_store_url
    'https://apps.apple.com/app/id1498121369'
  end

  def self.get_redis_key_for_app_update_notification_count(user_id, conversation_id)
    "dm_app_update_notification_#{user_id}_#{conversation_id}"
  end

  def self.dm_recommended_users_redis_cache_key(user_id)
    "dm_recommended_users_of_#{user_id}"
  end

  def self.get_dm_enabled_app_version
    Gem::Version.new('2308.01.0')
  end

  def self.get_dm_private_groups_enabled
    Gem::Version.new('2311.29.00')
  end

  def self.get_dm_channels_enabled_version
    Gem::Version.new('2311.30.00')
  end

  def self.default_dm_private_group_icon
    'https://a-cdn.thecircleapp.in/fit-in/512x512/filters:quality(80)/assets/group_fallback_avatar.jpg'
  end

  def self.default_dm_channel_icon
    'https://a-cdn.thecircleapp.in/fit-in/512x512/filters:quality(80)/assets/channel_fallback_avatar.jpg'
  end

  def self.get_max_following_count_to_fetch_dm_recommended_users
    100
  end

  def self.get_max_followers_count_to_fetch_dm_recommended_users
    100
  end

  def self.get_max_dm_forward_count
    5
  end

  def self.cloud_vision_tags
    {
      'poster' => ['poster'],
      'advertisement' => ['advertisement', 'advertising', 'marketing'],
      'local_event' => ['local event', 'event'],
    }
  end

  def self.premium_package_frames_count
    5
  end

  def self.ap_mla_const_for_suggested_circles
    []
  end

  def self.posters_v3_released_identities
    %w[polygonal_profile_identity party_tag_identity premium_cornered_party_icon_shiny_identity
     semi_circular_identity multi_color_identity shiny_identity shiny_identity_with_low_shadow
     stroked_border_identity party_slogan_identity_with_party_icon linear_name_and_role_identity trapezoidal_identity
     top_trapezoidal_identity bottom_trapezoidal_identity plain_identity basic_no_profile_pic_identity
     basic_transparent_identity premium_cornered_party_icon_gradient_identity]
  end

  def self.channel_enabled_circle_levels
    [:private, :political_party, :political_leader, :sub, :investor, :media]
  end

  def self.truecaller_user_email_key
    'truecaller_user_email'
  end

  def self.web_app_tokens_redis_key
    'users_web_app_active_jwt_tokens'
  end

  def self.customer_care_user_id
    praja_account_user_id
  end

  def self.recommended_premium_frames_count
    30
  end

  def self.recommended_status_frames_count
    20
  end

  def self.post_poster_tag_id
    1
  end

  def self.poster_affiliated_party_key
    'poster_affiliated_party_id'
  end

  def self.floww_contact_id_key
    'floww_contact_id'
  end

  def self.zoho_contact_id_key
    'zoho_contact_id'
  end

  def self.poster_usage_count_threshold_for_premium_pitch
    10
  end

  def self.poster_usage_count_threshold_for_lead
    5
  end

  def self.poster_usage_count_threshold_for_qualified_pitch_lead
    5
  end

  def self.no_of_days_before_to_check_for_qualified_lead
    5
  end

  def self.no_of_days_before_to_check_for_low_usage
    2
  end

  def self.poster_usage_count_threshold_for_trial_used
    1
  end

  # PREMIUM_FRAME_6_MONTH_PRICE
  def self.premium_frame_6_month_price
    150
  end

  # PREMIUM_FRAME_12_MONTH_PRICE
  def self.premium_frame_12_month_price
    300
  end

  # STATUS_FRAME_6_MONTH_PRICE
  def self.status_frame_6_month_price
    600
  end

  # STATUS_FRAME_12_MONTH_PRICE
  def self.status_frame_12_month_price
    1000
  end

  # designations for get-verified flow
  def self.designations
    [
      { name: 'elected_representative', display_name: 'ప్రజా ప్రతినిధి' },
      { name: 'party_official', display_name: 'పార్టీ నాయకులు' },
      { name: 'journalist', display_name: 'జర్నలిస్టు' },
      { name: 'lawyer', display_name: 'న్యాయవాది' },
      { name: 'other', display_name: 'ఇతర' }
    ]
  end

  def self.posters_tab_v3_enabled?
    Gem::Version.new('2311.30.0')
  end

  def self.user_post_id_via_dm_key(user_id)
    "dm_post_ids_of_user_#{user_id}"
  end

  def self.cdn_domain
    'az-ip-cdn.thecircleapp.in'
  end

  def self.biggest_circle_ids
    if Rails.env.development?
      [5]
    else
      [31398, 31401, 31402, 31403, 31405, 31406]
    end
  end

  # number of creatives to be fetched for each category in posters tab
  def self.creatives_count
    5
  end

  def self.get_vip_status_badge_role_id
    if Rails.env.production?
      79
    elsif Rails.env.test?
      1
    else
      1
    end
  end

  def self.relevant_fan_poster_toast_id
    'relevant_fan_poster_toast_id'
  end

  def self.relevant_fan_poster_max_view_count
    2
  end

  def self.unlimited_package_id
    1
  end

  def self.subscription_activated_date_key
    "subscription_activated_date"
  end

  def self.user_poster_trial_start_date_key
    'poster_trial_start_date'
  end

  def self.user_poster_trial_end_date_key
    'poster_trial_end_date'
  end

  def self.user_poster_trial_duration_key
    'poster_trial_duration_in_days'
  end

  def self.premium_pitch_views_key
    'premium_pitch_views_v2'
  end

  def self.dummy_creative_id
    if Rails.env.production?
      PosterCreative.last.id
    else
      PosterCreative.find_by(id: 431)&.id || PosterCreative.last.id
    end
  end

  def self.dummy_plain_creative_id
    if Rails.env.production?
      PosterCreative.find_by(id: 1695)&.id || PosterCreative.last.id
    else
      PosterCreative.find_by(id: 432)&.id || PosterCreative.last.id
    end
  end

  def self.hero_dummy_cutout_id
    if Rails.env.production?
      164403
    else
      AdminMedium.find_by(id: 1762)&.id || AdminMedium.last.id
    end
  end

  def self.hero_frame_dummy_cutout_url
    if Rails.env.production?
      AdminMedium.find_by(id: 164403).url
    else
      AdminMedium.last.url
    end
  end

  def self.family_dummy_cutout_id
    if Rails.env.production?
      164402
    else
      AdminMedium.find_by(id: 1766)&.id || AdminMedium.last.id
    end
  end

  def self.family_frame_dummy_cutout_url
    if Rails.env.production?
      AdminMedium.find_by(id: 164402).url
    else
      AdminMedium.last.url
    end
  end

  def self.user_dummy_cutout_id
    if Rails.env.production?
      164401
    else
      AdminMedium.find_by(id: 1760)&.id || AdminMedium.last.id
    end
  end

  def self.h1_dummy_photo_id
    if Rails.env.production?
      164397
    else
      AdminMedium.find_by(id: 1764)&.id || AdminMedium.last.id
    end
  end

  def self.h2_dummy_photo_id
    if Rails.env.production?
      164400
    else
      AdminMedium.find_by(id: 1765)&.id || AdminMedium.last.id
    end
  end

  def self.get_leader_dummy_cutout_id
    if Rails.env.production?
      87698
    else
      AdminMedium.find_by(id: 1763)&.id || AdminMedium.last.id
    end
  end

  def self.events_count_image_url
    if Rails.env.production?
      AdminMedium.find_by(id: 172456).url
    else
      AdminMedium.last.url
    end
  end

  def self.unlimited_downloads_image_url
    if Rails.env.production?
      AdminMedium.find_by(id: 172457).url
    else
      AdminMedium.last.url
    end
  end

  def self.user_fan_poster_interests_key
    'user_fan_poster_interests'
  end

  def self.no_of_users_to_be_shown
    4
  end

  def self.min_no_of_users_to_be_interested
    10
  end

  def self.owner_premium_interests_key
    'owner_premium_interests'
  end

  def self.max_no_of_times_to_show_fan_poster_prompt
    2
  end

  def self.fan_posters_prompt_seen_count_key(user_id, circle_id)
    "fan_posters_prompt_seen_count_#{user_id}_#{circle_id}"
  end

  def self.poster_creative_jid_key
    'poster_creative_jid'
  end

  def self.fan_user_dummy_photo_id
    94176
  end

  def self.fan_request_feed_item_id
    'fan_request_feed_item'
  end

  def self.add_to_homescreen_enabled_circles
    if Rails.env.development?
      [5]
    else
      [38133]
    end
  end

  def self.fan_poster_view_count_key(user_id, circle_id)
    "fan_poster_view_count_#{user_id}_#{circle_id}"
  end

  def self.party_mla_contestant_key
    :party_mla
  end

  def self.party_mp_contestant_key
    :party_mp
  end

  def self.user_district_mla_contestant_key
    :user_district_mla_contestant
  end

  def self.user_constituency_mla_contestant_key
    :user_constituency_mla_contestant
  end

  def self.user_constituency_mp_contestant_key
    :user_constituency_mp_contestant
  end

  def self.mla_contestant_carousel_feed_item_id
    'user_mla_contestant_carousel'
  end

  def self.mp_contestant_carousel_feed_item_id
    'user_mp_contestant_carousel'
  end

  def self.district_mla_contestant_carousel_feed_item_id
    'user_district_mla_contestant_carousel'
  end

  def self.kyc_carousel_feed_items_for_my_feed
    [Constants.mla_contestant_carousel_feed_item_id, Constants.mp_contestant_carousel_feed_item_id,
     Constants.district_mla_contestant_carousel_feed_item_id]
  end

  def self.kyc_carousel_views_key(user_id)
    "kyc_carousel_views_#{user_id}"
  end

  def self.non_badge_users_for_premium_pitch_key
    'non_badge_users_for_premium_pitch'
  end

  def self.poster_carousel_feed_item_id
    'poster_carousel'
  end

  def self.frame_views_redis_key
    'frame_views'
  end

  def self.frame_views_queue_redis_key
    'frame_views_queue_v1'
  end

  def self.user_date_frame_views_queue_redis_key(user_id)
    "user_date_frame_views_#{user_id}_#{Time.zone.now.strftime("%Y-%m-%d")}"
  end

  def self.poster_creative_views_redis_key
    'poster_creative_views'
  end

  def self.poster_creative_views_queue_redis_key
    'poster_creative_views_queue_v1'
  end

  def self.user_date_poster_creative_views_queue_redis_key(user_id)
    "user_date_poster_creative_views_#{user_id}_#{Time.zone.now.strftime("%Y-%m-%d")}"
  end

  def self.post_views_redis_key
    'post_views'
  end

  def self.post_views_queue_redis_key
    'post_views_queue_v1'
  end

  def self.user_date_post_views_queue_redis_key(user_id, date_key = nil)
    date_key ||= Time.zone.now.strftime('%Y-%m-%d')
    "user_date_post_views_#{user_id}_#{date_key}"
  end

  def self.constituency_feed_item_id_prefix
    'corporation-constituency-selection-'
  end

  def self.user_with_circle_key
    'user_with_circle'
  end

  def self.media_service_callbacks_baseurl
    if ENV['DEPLOYMENT'] == 'preprod'
      'http://media-service-preprod.media-service.svc.cluster.local:3000'
    else
      "http://media-service.media-service.svc.cluster.local:3000"
    end
  end

  def self.max_count_election_ticker_preview_per_week
    2
  end

  def self.max_count_user_constituency_status_carousel_per_week
    2
  end

  def self.max_count_district_constituency_status_carousel_per_week
    2
  end

  def self.max_count_prepoll_carousel_per_week
    2
  end

  def self.passed_milestone_1_creatives_criteria_redis_key
    'passed_milestone_1_creatives_criteria_user_ids'
  end

  def self.daily_premium_leads_limit
    200
  end

  def self.get_send_message_wati_url
    "https://live-mt-server.wati.io/6274/api/v1/sendTemplateMessage"
  end

  def self.probable_premium_user_key
    "probable_premium_user"
  end

  def self.profession_selection_skipped_key
    'profession_selection_skipped'
  end

  def self.leader_profession_ids
    # political leader, independent org leader
    [1, 2]
  end

  def self.non_leader_sub_profession_ids
    # Karyakatha, Political Leader/Others, Independent Org Leader/Others
    [3, 43, 44]
  end

  def self.yet_to_start_trial_key
    "yet_to_start_trial"
  end

  def self.rm_user_id_key
    'rm_user_id'
  end

  def self.default_plan_key
    'default_plan'
  end

  def self.system_default_plan_id
    1
  end

  def self.plan_downgrade_consent_key
    'subscription_plan_downgrade_consent'
  end

  def self.plan_downgraded_date_key
    'subscription_plan_downgraded_date'
  end

  def self.get_premium_frame_product_id
    2
  end

  def self.get_status_frame_product_id
    3
  end

  def self.max_premium_frames_support_count
    10
  end

  def self.max_status_frames_support_count
    10
  end

  def self.poster_trial_default_duration
    15
  end

  def self.default_premium_duration_days
    30.days.from_now
  end

  def self.duration_days_after_premium_end_date
    3
  end

  def self.posters_preview_dashboard_base_url
    if ENV['DEPLOYMENT'] == 'preprod'
      "https://preprod-pp.thecircleapp.in"
    else
      "https://pp.thecircleapp.in"
    end
  end

  def self.praja_customer_care_phone_number
    8861894908
  end

  def self.praja_customer_care_email
    "<EMAIL>"
  end

  def self.default_rm_user_image_url
    if Rails.env.production?
      AdminMedium.find_by(id: 176928)&.placeholder_url
    else
      AdminMedium.last.placeholder_url
    end
  end

  def self.juspay_customer_id_key
    'juspay_customer_id'
  end

  def self.poster_feed_for_you_image_url
    if Rails.env.production?
      AdminMedium.find_by(id: 177495).url
    else
      AdminMedium.last.url
    end
  end

  def self.public_circle_id
    0
  end

  def self.preferred_upi_apps_ios
    [
      {
        'package_name': 'phonepe://pay',
        'display_name': 'PhonePe',
        'name': 'PHONEPE',
        'icon_url':
          'https://play-lh.googleusercontent.com/6iyA2zVz5PyyMjK5SIxdUhrb7oh9cYVXJ93q6DZkmx07Er1o90PXYeo6mzL4VC2Gj9s=w120-h240-rw',
      },
      {
        'package_name': 'paytmmp://upi/pay',
        'display_name': 'Paytm',
        'name': 'PAYTM',
        'icon_url':
          'https://play-lh.googleusercontent.com/2tH3ybpe3Tb5y2vamr4s0IJ-ffW83ouOFl4qDeZ8qvKdil5OjMN5_kiQviniaIBz420=w120-h240-rw',
      },
      {
        'package_name': 'tez://upi/pay',
        'display_name': 'GooglePay',
        'name': 'GOOGLEPAY',
        'icon_url':
          'https://play-lh.googleusercontent.com/Fm5PDRimTL_KsWyIRcTv9h0JLrTkDOMwh18SE819OXjEZhlwMYBHJXxUZ8eOBudxCsHC=w120-h240-rw',
      },
      {
        "package_name": "credpay://upi/pay",
        "display_name": "CRED UPI",
        "name": "CRED_UPI",
        "icon_url":
          "https://play-lh.googleusercontent.com/r2ZbsIr5sQ7Wtl1T6eevyWj4KS7QbezF7JYB9gxQnLWbf0K4kU7qaLNcJLLUh0WG-3pK=w120-h240-rw"
      }
    ]
  end

  # Returns the static value for iOS UPI app based on the iOS package name
  # For PhonePe payment flow, iOS requires static values like PHONEPE/GPAY/PAYTM
  # @param package_name [String] The iOS package name/URL scheme of the UPI app
  # @return [String] The static value for iOS UPI app
  def self.get_upi_app_static_values_for_ios(package_name)
    # Map of iOS package names/URL schemes to their corresponding static values
    package_name_to_static_value = {
      'phonepe://pay' => 'PHONEPE',
      'paytmmp://upi/pay' => 'PAYTM',
      'tez://upi/pay' => 'GPAY',
      'bhim://upi/pay' => 'BHIM',
      'credpay://upi/pay' => 'CRED'
    }

    # Return the static value for the given package name, or a default value if not found
    package_name_to_static_value[package_name] || 'PHONEPE' # Default to PHONEPE if not found
  end

  def self.preferred_upi_apps_android
    [
      {
        'package_name': 'com.phonepe.app',
        'display_name': 'PhonePe',
        'name': 'PHONEPE',
        'icon_url':
          'https://play-lh.googleusercontent.com/6iyA2zVz5PyyMjK5SIxdUhrb7oh9cYVXJ93q6DZkmx07Er1o90PXYeo6mzL4VC2Gj9s=w120-h240-rw',
      },
      {
        'package_name': 'net.one97.paytm',
        'display_name': 'Paytm',
        'name': 'PAYTM',
        'icon_url':
          'https://play-lh.googleusercontent.com/2tH3ybpe3Tb5y2vamr4s0IJ-ffW83ouOFl4qDeZ8qvKdil5OjMN5_kiQviniaIBz420=w120-h240-rw',
      },
      {
        'package_name': 'com.google.android.apps.nbu.paisa.user',
        'display_name': 'GooglePay',
        'name': 'GOOGLEPAY',
        'icon_url':
          'https://play-lh.googleusercontent.com/Fm5PDRimTL_KsWyIRcTv9h0JLrTkDOMwh18SE819OXjEZhlwMYBHJXxUZ8eOBudxCsHC=w120-h240-rw',
      },
      {
        "package_name": "in.org.npci.upiapp",
        "display_name": "Bhim UPI",
        "name": "BHIM_UPI",
        "icon_url":
          "https://play-lh.googleusercontent.com/B5cNBA15IxjCT-8UTXEWgiPcGkJ1C07iHKwm2Hbs8xR3PnJvZ0swTag3abdC_Fj5OfnP=w120-h240-rw"
      },
      {
        "package_name": "org.altruist.BajajExperia",
        "display_name": "Bajaj Finserv",
        "name": "BAJAJ_FINSERV",
        "icon_url":
          "https://play-lh.googleusercontent.com/DbepofsHLK7fTQmiQi9KurqbL1VvVJEAJ0AOX8CejdsgygCTH_0K4kG9JLmcKl3MkN0K=w120-h240-rw"
      },
      {
        "package_name": "in.amazon.mShop.android.shopping",
        "display_name": "Amazon",
        "name": "AMAZON",
        "icon_url":
          "https://play-lh.googleusercontent.com/1Ns1T_qN0pEXMvZeZ5lQNAR8z4blP7ce2J2Nn5doXvt2T1g_W7VMORdWHaApkOooupI=w120-h420-rw"
      },
      {
        "package_name": "com.naviapp",
        "display_name": "Navi UPI",
        "name": "NAVI",
        "icon_url":
          "https://play-lh.googleusercontent.com/5BlEJb1cF_r6dSCvPkeIcBaWbdBgmIId5ZiNmpeT3cPEV8ojj5LAKpg0wIWAg88b6q4=w120-h420-rw"
      },
      {
        "package_name": "com.dreamplug.androidapp",
        "display_name": "CRED UPI",
        "name": "CRED_UPI",
        "icon_url":
          "https://play-lh.googleusercontent.com/r2ZbsIr5sQ7Wtl1T6eevyWj4KS7QbezF7JYB9gxQnLWbf0K4kU7qaLNcJLLUh0WG-3pK=w120-h240-rw"
      }
    ]
  end

  def self.max_no_of_times_to_show_self_trial_nudge_for_user
    3
  end

  def self.self_trial_nudge_seen_count_key(user_id)
    "self_trial_nudge_seen_count_#{user_id}"
  end

  def self.designer_photo_url
    if Rails.env.production?
      AdminMedium.find_by(id: 185942).url
    else
      AdminMedium.last.url
    end
  end

  def self.poster_share_deeplink_redis_key(user_id)
    "poster_share_deeplink_#{user_id}"
  end

  def self.upgrade_deeplink_in_poster_share_redis_key(user_id)
    "upgrade_deeplink_in_poster_share_#{user_id}"
  end

  def self.premium_1_rs_user_redis_key
    'premium_1_rs_user_redis_key'
  end

  def self.premium_29_rs_campaign_redis_key
    'premium_29_rs_users'
  end

  def self.premium_59_rs_campaign_redis_key
    'premium_59_rs_users'
  end

  def self.signed_up_today_key(user_id)
    "signed_up_today_#{user_id}"
  end

  def self.seen_premium_experience_popup_key(user_id)
    "seen_premium_experience_popup_#{user_id}"
  end

  def self.premium_half_price_campaign_redis_key
    'premium_half_price_users'
  end

  def self.update_floww_lead_score_based_on_last_online_key
    'update_floww_lead_score_based_on_last_online'
  end

  def self.update_floww_lead_score_based_on_last_trial_attempt_key
    'update_floww_lead_score_based_on_last_trial_attempt'
  end

  def self.annual_paywall_deeplink
    "/annual-pay-wall"
  end

  def self.paywall_deeplink
    "/pay-wall"
  end

  def self.last_rm_submitted_data
    "last_rm_submitted_data"
  end

  def self.send_wati_msg_key
    "send_wati_msg"
  end

  def self.upgrade_package_nudge_count_key(user_id)
    "upgrade_package_nudge_count_#{user_id}"
  end

  def self.upgrade_package_sheet_key
    "upgrade_package_sheet"
  end

  def self.campaign_offer_reveal_status_key(campaign_id)
    "campaign_#{campaign_id}_offer_reveal_status"
  end

  def self.openai_url
    "https://api.openai.com/v1/chat/completions"
  end

  def self.charge_ivr_experiment_redis_key
    "charge_ivr_experiment_user"
  end

  def self.versions_table_last_deleted_id_redis_key
    "versions_table_last_deleted_id"
  end

  def self.badge_free_text_key
    "badge_free_text"
  end

  def self.poster_photo_with_background_original_key
    "poster_photo_with_background_original"
  end

  def self.poster_photo_without_background_original_key
    "poster_photo_without_background_original"
  end

  def self.hero_frame_photo_original_key
    "hero_frame_photo_original"
  end

  def self.family_frame_photo_original_key
    "family_frame_photo_original"
  end

  def self.poster_photo_without_background_key
    "poster_photo_without_background"
  end

  def self.hero_frame_photo_key
    "hero_frame_photo"
  end

  def self.family_frame_photo_key
    "family_frame_photo"
  end

  def self.azure_buckets_source
    "prodprajarup"
  end

  def self.aws_buckets_source
    "circle-app-photos"
  end

  def self.gcp_buckets_source
    "praja-raw-user-photos"
  end

  def self.photos_table_last_processed_id_key
    "photos_table_last_processed_id"
  end

  def self.videos_table_last_processed_id_key
    "videos_table_last_processed_id"
  end

  def self.truecaller_invalid_images_last_processed_id_key
    "truecaller_invalid_images_last_processed_id"
  end

  def self.premium_reactivation_wati_campaign_count
    "premium_reactivation_wati_campaign_count"
  end

  def self.poster_image_url(campaign_name)
    "poster_image_url_#{campaign_name}"
  end

  def self.get_send_message_wati_csv_url
    "https://live-mt-server.wati.io/6274/api/v1/sendTemplateMessageCSV"
  end

  def self.aws_s3_bucket_name_for_csvs
    "praja-csvs"
  end

  def self.monthly_campaign_for_yearly_users_key
    "monthly_campaign_for_yearly_users_zset"
  end

  def self.bg_remove_redis_key(key, user_id)
    "#{key}:#{user_id}"
  end

  def self.trial_activation_wati_campaign_count
    "trial_activation_wati_campaign_count"
  end

  def self.get_send_messages_using_bulk_api
    "https://live-mt-server.wati.io/6274/api/v2/sendTemplateMessages"
  end

  def self.wati_campaigns_redis_key
    "wati_campaigns_redis_key"
  end

  def self.layout_rejection_reason
    'layout_rejection_reason'
  end

  def self.oe_review_completed
    'oe_review_completed'
  end

  def self.selected_no_affiliated_party_key
    'selected_no_affiliated_party'
  end

  def self.user_professions_table_last_processed_id_key
    'user_professions_table_last_processed_key'
  end
end
