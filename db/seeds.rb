# This file should contain all the record creation needed to seed the database with its default values.
# The data can then be loaded with the rails db:seed command (or created alongside the database with db:setup).
#
# Examples:
#
#   movies = Movie.create([{ name: 'Star Wars' }, { name: 'Lord of the Rings' }])
#   Character.create(name: '<PERSON>', movie: movies.first)
begin
  if Rails.env.development?
    admin_user = AdminUser.where(email: '<EMAIL>').first_or_create(id: 1, password: 'password', password_confirmation: 'password',phone: '1000000000', name: 'Admin User')

    # Professions
    if Profession.count.zero?
      # Admin Media for professions
      AdminMedium.skip_callback(:save, :before, :set_media_type)
      AdminMedium.skip_callback(:save, :before, :upload)

      political_leader_photo = AdminMedium.new(url: "https://cdn.thecircleapp.in/production/admin-media/32/2c7180d4-5e26-4f35-bb0c-e5064c04c1a3.jpg", admin_user_id: admin_user.id, media_type: :photo)
      political_leader_photo.save(validate: false)

      independent_org_leader_photo = AdminMedium.new(url: "https://cdn.thecircleapp.in/production/admin-media/32/e7df247d-979c-4514-86e1-e11437338c9f.jpg", admin_user_id: admin_user.id, media_type: :photo)
      independent_org_leader_photo.save(validate: false)

      AdminMedium.set_callback(:save, :before, :set_media_type)
      AdminMedium.set_callback(:save, :before, :upload)

      political_leader = Profession.create!(name: 'Political Leader', name_en: 'Political Leader EN', admin_medium: political_leader_photo)
      independent_org_leader = Profession.create!(name: 'Independent Organization Leader', name_en: 'Independent Organization Leader EN', admin_medium: independent_org_leader_photo)
      SubProfession.create!(name: 'Elected Representative', name_en: 'Elected Representative EN', profession: political_leader)
      SubProfession.create!(name: 'Party Leader', name_en: 'Party Leader EN', profession: political_leader)
      SubProfession.create!(name: 'Party Worker', name_en: 'Party Worker EN', profession: political_leader)
    end

    Circle.create!(id: 0, name: 'Public', name_en: 'Public', level: :private, circle_type: :my_circle)

    # Owner permission group
    permission_group = PermissionGroup.new(id: 1, name: 'owner')
    permission_group.permission_group_permissions.build(permission_identifier: :add_tag)
    permission_group.permission_group_permissions.build(permission_identifier: :remove_tag)
    permission_group.save!

    # Circle with short_info
    # Circle.create!(name: 'Topic', name_en: 'Topic EN', level: :topic, circle_type: :interest, short_info: 'Topic
    # Short Info', parent_circle: nil)

    state = Circle.create!(name: 'State', name_en: 'State EN', level: :state, circle_type: :location)
    district = Circle.create!(name: 'District', name_en: 'District EN', level: :district, circle_type: :location,
                              parent_circle: state)
    mandal = Circle.create!(name: 'Mandal', name_en: 'Mandal EN', level: :mandal, circle_type: :location, parent_circle: district)
    village = Circle.create!(name: 'Village', name_en: 'Village EN', level: :village, circle_type: :location, parent_circle: mandal)

    # Interest circle
    Circle.create!(name: 'TRS', name_en: 'TRS EN', level: :political_party, circle_type: :interest, parent_circle: nil)
    mla = Circle.create!(name: 'Leader', name_en: 'Leader EN', level: :political_leader, circle_type: :interest, parent_circle: nil)
    mp = Circle.create!(name: 'Leader 2', name_en: 'Leader 2 EN', level: :political_leader, circle_type: :interest, parent_circle: nil)

    # Constituencies
    mp_const = Circle.create!(name: 'MP Const.', name_en: 'MP Const. EN', level: :mp_constituency, circle_type: :location, parent_circle: nil)
    mla_const = Circle.create!(name: 'MLA Const.', name_en: 'MLA Const. EN', level: :mla_constituency, circle_type:
      :location, parent_circle: mp_const)

    # Circle relations
    CirclesRelation.create!(first_circle: mandal, second_circle: mla_const, relation: 'Mandal2MLA')
    CirclesRelation.create!(first_circle: mla_const, second_circle: mla, relation: 'MLA')
    CirclesRelation.create!(first_circle: mp_const, second_circle: mp, relation: 'MP')

    user = User.create(name: 'Fantom User 1', phone: 2_000_000_000, village_id: village.id)
    user.activate
    user.update_location
    user.circles << village

    user1 = User.create(name: 'Fantom User 2', phone: 2_000_000_001, village_id: village.id)
    user1.activate
    user1.update_location
    user1.circles << village

    user2 = User.create(name: 'Praja User', phone: 9_999_999_999, village_id: village.id)
    user2.activate
    user2.update_location
    user2.circles << village

    ug = UserGroup.create(user: user, name: 'Fantom Group 1')
    UserGroupMember.create(user_group: ug, user: user1)

    SingularDatum.create(user_id: user1.id, invited_by: user.id, utm_medium: "Referrals", utm_source: "Shares", utm_campaign: "Profile Share")

    font = Font.find_or_create_by(name_font: "Noto Sans Telugu", badge_font: "Noto Sans Telugu")

    Product.find_or_create_by(id: 1, name: 'Posters', description: 'Posters description')
    ItemPrice.create!(item_type: "Product", item_id: 1, price: 2000, maintenance_price: 1000, duration_in_months: 12)
    ItemPrice.create!(item_type: "Product", item_id: 1, price: 1200, maintenance_price: 800, duration_in_months: 6)

    # Frames and Item Price data for posters tab
    frame1 = Frame.find_or_create_by(identifier: :party_curved_identity, frame_type: :premium, gold_border: false,
                                     has_shadow_color: false, is_neutral_frame: false, has_footer_party_icon: false,
                                     badge_strip: true, user_position_back: false, font_id: font.id, identity_type: :curved)
    ItemPrice.create!(item_type: "Frame", item_id: frame1.id, price: 500, maintenance_price: 0, duration_in_months: 12)
    ItemPrice.create!(item_type: "Frame", item_id: frame1.id, price: 300, maintenance_price: 0, duration_in_months: 6)

    frame2 = Frame.find_or_create_by(identifier: :party_flat_circle_identity, frame_type: :premium, gold_border: false, has_shadow_color: false,
                                     is_neutral_frame: false, has_footer_party_icon: false, badge_strip: true,
                                     user_position_back: false, font_id: font.id, identity_type: :flat_user_badge_circle)
    ItemPrice.create!(item_type: "Frame", item_id: frame2.id, price: 500, maintenance_price: 0, duration_in_months: 12)
    ItemPrice.create!(item_type: "Frame", item_id: frame2.id, price: 300, maintenance_price: 0, duration_in_months: 6)

    frame3 = Frame.find_or_create_by(identifier: :party_flat_identity, frame_type: :premium, gold_border: false, has_shadow_color: false,
                                     is_neutral_frame: false, has_footer_party_icon: true, badge_strip: true,
                                     user_position_back: false, font_id: font.id, identity_type: :flat_user)
    ItemPrice.create!(item_type: "Frame", item_id: frame3.id, price: 500, maintenance_price: 0, duration_in_months: 12)
    ItemPrice.create!(item_type: "Frame", item_id: frame3.id, price: 300, maintenance_price: 0, duration_in_months: 6)

    frame4 = Frame.find_or_create_by(identifier: :party_white_flat_identity, frame_type: :premium, gold_border: false, has_shadow_color: false,
                                     is_neutral_frame: false, has_footer_party_icon: true, badge_strip: true,
                                     user_position_back: false, font_id: font.id, identity_type: :gold_lettered_user)
    ItemPrice.create!(item_type: "Frame", item_id: frame4.id, price: 500, maintenance_price: 0, duration_in_months: 12)
    ItemPrice.create!(item_type: "Frame", item_id: frame4.id, price: 300, maintenance_price: 0, duration_in_months: 6)

    frame5 = Frame.find_or_create_by(identifier: :neutral_flat_identity, frame_type: :premium, gold_border: false, has_shadow_color: false,
                                     is_neutral_frame: true, has_footer_party_icon: false, badge_strip: true,
                                     user_position_back: false, font_id: font.id, identity_type: :flat_user)
    ItemPrice.create!(item_type: "Frame", item_id: frame5.id, price: 500, maintenance_price: 0, duration_in_months: 12)
    ItemPrice.create!(item_type: "Frame", item_id: frame5.id, price: 300, maintenance_price: 0, duration_in_months: 6)

    frame6 = Frame.find_or_create_by(identifier: :gold_white_flat_identity, frame_type: :status, gold_border: true, has_shadow_color: false,
                                     is_neutral_frame: false, has_footer_party_icon: true, badge_strip: true,
                                     user_position_back: false, font_id: font.id, identity_type: :gold_lettered_user)
    ItemPrice.create!(item_type: "Frame", item_id: frame6.id, price: 1000, maintenance_price: 0, duration_in_months: 12)
    ItemPrice.create!(item_type: "Frame", item_id: frame6.id, price: 600, maintenance_price: 0, duration_in_months: 6)

    frame7 = Frame.find_or_create_by(identifier: :gold_curved_identity, frame_type: :status, gold_border: true, has_shadow_color: false,
                                     is_neutral_frame: false, has_footer_party_icon: false, badge_strip: true,
                                     user_position_back: false, font_id: font.id, identity_type: :curved_with_depth)
    ItemPrice.create!(item_type: "Frame", item_id: frame7.id, price: 1000, maintenance_price: 0, duration_in_months: 12)
    ItemPrice.create!(item_type: "Frame", item_id: frame7.id, price: 600, maintenance_price: 0, duration_in_months: 6)

    frame8 = Frame.find_or_create_by(identifier: :party_neon_curved_identity, frame_type: :status, gold_border: false, has_shadow_color: true,
                                     is_neutral_frame: false, has_footer_party_icon: false, badge_strip: true,
                                     user_position_back: false, font_id: font.id, identity_type: :curved_with_depth)
    ItemPrice.create!(item_type: "Frame", item_id: frame8.id, price: 1000, maintenance_price: 0, duration_in_months: 12)
    ItemPrice.create!(item_type: "Frame", item_id: frame8.id, price: 600, maintenance_price: 0, duration_in_months: 6)

    CirclePackage.find_or_create_by(
      id: 1,
      name: "Unlimited Package",
      enable_channel: true,
      enable_fan_posters: true,
      eligibile_for_post_push_notifications: true,
      eligibile_for_wati: true,
      fan_poster_creatives_limit: nil,
      channel_post_msg_limit: nil,
      channel_post_msg_notification_limit: nil
    )

  elsif Rails.env.test?
    User.find_or_create_by(name: 'Praja User', phone: 9_999_999_999)
    AdminUser.where(email: '<EMAIL>').first_or_create(id: 1, phone: 9_999_999_999, password: 'password', password_confirmation: 'password')

    Circle.create!(id: 0, name: 'Public', name_en: 'Public', level: :private, circle_type: :my_circle)

    # Owner permission group
    permission_group = PermissionGroup.new(id: 1, name: 'owner')
    permission_group.permission_group_permissions.build(permission_identifier: :add_tag)
    permission_group.permission_group_permissions.build(permission_identifier: :remove_tag)
    permission_group.save!
    Product.create(id: 1, name: 'Posters', description: 'Posters description')
    Product.create(id: 2, name: 'premium frames', description: 'premium frames description')
    Product.create(id: 3, name: 'status frames', description: 'status frames description')

    # basic frames data
    font = Font.find_or_create_by(name_font: "Noto Sans Telugu", badge_font: "Noto Sans Telugu")
    Frame.create(frame_type: :basic, identity_type: :flat_user, badge_strip: true, user_position_back: false,
                 outer_frame: true, font_id: font.id)
    Frame.create(frame_type: :basic, identity_type: :basic_transparent_identity, badge_strip: false,
                 user_position_back: false, outer_frame: true, font_id: font.id)
    Frame.create(frame_type: :basic, identity_type: :basic_no_profile_pic_identity, badge_strip: false,
                 user_position_back: false, outer_frame: true, font_id: font.id)

    CirclePackage.find_or_create_by(
      id: 1,
      name: "Unlimited Package",
      enable_channel: true,
      enable_fan_posters: true,
      eligibile_for_post_push_notifications: true,
      eligibile_for_wati: true,
      fan_poster_creatives_limit: nil,
      channel_post_msg_limit: nil,
      channel_post_msg_notification_limit: nil
    )
    Plan.create!(id: 1, name: "Plan 1 monthly test", duration_in_months: 1, total_amount: 400, discount_amount: 1,
                 amount: 399)
    Plan.create!(id: 2, name: "Plan 2 yearly test", duration_in_months: 12, total_amount: 400, discount_amount: 1,
                 amount: 2399)

    PlanProduct.create!(plan_id: 1, product_id: 1, quantity: 1)
    PlanProduct.create!(plan_id: 1, product_id: 2, quantity: 10)
    PlanProduct.create!(plan_id: 1, product_id: 3, quantity: 0)
    PlanProduct.create!(plan_id: 2, product_id: 1, quantity: 1)
    PlanProduct.create!(plan_id: 2, product_id: 2, quantity: 10)
    PlanProduct.create!(plan_id: 2, product_id: 3, quantity: 0)
  end
rescue
  nil
end
