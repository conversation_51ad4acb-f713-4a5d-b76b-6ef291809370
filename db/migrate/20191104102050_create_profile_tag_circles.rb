class CreateProfileTagCircles < ActiveRecord::Migration[6.0]
  def change
    create_table :profile_tag_circles do |t|
      t.references :profile_tag, type: :bigint, null: false, foreign_key: true
      t.references :circle, type: :bigint, type: :bigint, null: false, foreign_key: true
      t.boolean :multiple, default: false
      t.boolean :active, default: true

      t.timestamps
    end
  end
end
