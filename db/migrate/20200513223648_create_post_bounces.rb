class CreatePostBounces < ActiveRecord::Migration[6.0]
  def change
    drop_table :post_bounces, if_exists: true

    create_table :post_bounces do |t|
      t.references :post, type: :bigint, null: false, foreign_key: true
      t.references :circle, type: :bigint, null: false, foreign_key: true
      t.integer :score, default: nil
      t.boolean :active, default: true

      t.timestamps
    end
  end
end
