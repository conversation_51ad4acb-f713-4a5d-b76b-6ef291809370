class CreatePermissionGroupPermissions < ActiveRecord::Migration[6.1]
  def change
    create_table :permission_group_permissions do |t|
      t.references :permission_group, null: false
      t.integer :permission_identifier, null: false
      t.timestamps
    end
    add_index :permission_group_permissions, [:permission_group_id, :permission_identifier], unique: true,
              name: 'index_on_permission_groups_id_and_permission_identifier'
  end
end


