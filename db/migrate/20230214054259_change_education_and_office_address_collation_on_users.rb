class ChangeEducationAndOfficeAddressCollationOnUsers < ActiveRecord::Migration[6.1]
  def up
    change_column :users, :education, :string, collation: 'utf8mb4_unicode_ci'
    change_column :users, :office_address, :text, collation: 'utf8mb4_unicode_ci'
  end

  def down
    change_column :users, :education, :string, collation: 'utf8mb4_0900_ai_ci'
    change_column :users, :office_address, :text, collation: 'utf8mb4_0900_ai_ci'
  end
end
