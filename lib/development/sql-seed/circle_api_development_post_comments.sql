INSERT IGNORE INTO circle_api_development.post_comments (id, comment, post_id, user_id, active, created_at, updated_at)
VALUES (1, '2nd comment.', 1, 24, 1, '2022-09-06 13:09:11', '2022-09-06 13:09:11');
INSERT IGNORE INTO circle_api_development.post_comments (id, comment, post_id, user_id, active, created_at, updated_at)
VALUES (2, '2nd cokjmment.', 1, 24, 1, '2022-09-06 13:09:16', '2022-09-06 13:09:16');
INSERT IGNORE INTO circle_api_development.post_comments (id, comment, post_id, user_id, active, created_at, updated_at)
VALUES (3, '2nd cokjmment is here.', 1, 24, 1, '2022-09-06 13:11:22', '2022-09-06 13:11:22');
INSERT IGNORE INTO circle_api_development.post_comments (id, comment, post_id, user_id, active, created_at, updated_at)
VALUES (4, '----------checking comments', 1, 1, 1, '2022-09-22 06:41:34', '2022-09-22 06:41:34');
INSERT IGNORE INTO circle_api_development.post_comments (id, comment, post_id, user_id, active, created_at, updated_at)
VALUES (5, '----------checking comments', 108, 1, 1, '2022-09-22 06:46:05', '2022-09-22 06:46:05');
INSERT IGNORE INTO circle_api_development.post_comments (id, comment, post_id, user_id, active, created_at, updated_at)
VALUES (6, 'Shit', 235, 1, 1, '2022-10-14 12:20:37', '2022-10-14 12:20:37');
INSERT IGNORE INTO circle_api_development.post_comments (id, comment, post_id, user_id, active, created_at, updated_at)
VALUES (7, 'Shit', 235, 1, 1, '2022-10-14 12:21:38', '2022-10-14 12:21:38');
INSERT IGNORE INTO circle_api_development.post_comments (id, comment, post_id, user_id, active, created_at, updated_at)
VALUES (8, 'Shit', 235, 1, 1, '2022-10-14 12:21:44', '2022-10-14 12:21:44');
INSERT IGNORE INTO circle_api_development.post_comments (id, comment, post_id, user_id, active, created_at, updated_at)
VALUES (9, 'Shit', 235, 1, 1, '2022-10-14 12:23:24', '2022-10-14 12:23:24');
INSERT IGNORE INTO circle_api_development.post_comments (id, comment, post_id, user_id, active, created_at, updated_at)
VALUES (10, 'Shit 2', 235, 1, 1, '2022-10-14 12:26:04', '2022-10-14 12:26:04');
INSERT IGNORE INTO circle_api_development.post_comments (id, comment, post_id, user_id, active, created_at, updated_at)
VALUES (11, 'deleting a post check 1', 394, 25, 1, '2022-11-08 06:25:56', '2022-11-08 06:25:56');
INSERT IGNORE INTO circle_api_development.post_comments (id, comment, post_id, user_id, active, created_at, updated_at)
VALUES (12, 'deleting a post check 2', 394, 25, 1, '2022-11-08 06:26:39', '2022-11-08 06:26:39');