# INSERT IGNORE INTO circle_api_development.active_storage_attachments (id, name, record_type, record_id, blob_id, created_at) VALUES (1, 'image', 'LeaderProject', 1, 1, '2023-01-20 05:13:49');
# INSERT IGNORE INTO circle_api_development.active_storage_attachments (id, name, record_type, record_id, blob_id, created_at) VALUES (2, 'image', 'LeaderProject', 2, 2, '2023-01-20 05:24:35');
# INSERT IGNORE INTO circle_api_development.active_storage_attachments (id, name, record_type, record_id, blob_id, created_at) VALUES (3, 'photo', 'LeaderProject', 3, 3, '2023-01-20 06:20:10');
# INSERT IGNORE INTO circle_api_development.active_storage_attachments (id, name, record_type, record_id, blob_id, created_at) VALUES (4, 'photo', 'LeaderProject', 4, 4, '2023-01-24 07:16:47');
# INSERT IGNORE INTO circle_api_development.active_storage_attachments (id, name, record_type, record_id, blob_id, created_at) VALUES (5, 'photo', 'LeaderProject', 5, 5, '2023-01-24 07:56:23');
# INSERT IGNORE INTO circle_api_development.active_storage_attachments (id, name, record_type, record_id, blob_id, created_at) VALUES (6, 'photo', 'LeaderProject', 6, 6, '2023-01-24 08:36:39');