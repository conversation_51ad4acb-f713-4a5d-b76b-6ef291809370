#/bin/bash
curl -XPUT --insecure -u admin:admin 'http://praja-opensearch:9200/development_new_posts_v2' -H 'Content-Type: application/json' -d '{
   "mappings" : {
     "properties" : {
       "active" : {
         "type" : "boolean"
       },
       "badge_user_affiliated_circle_id" : {
         "type" : "keyword"
       },
       "circle_id" : {
         "type" : "keyword"
       },
       "circle_level" : {
         "type" : "keyword"
       },
       "comments_count" : {
         "type" : "long"
       },
       "created_at" : {
         "type" : "date"
       },
       "district_id" : {
         "type" : "keyword"
       },
       "hashtag_ids" : {
         "type" : "keyword"
       },
       "id" : {
         "type" : "keyword"
       },
       "is_badge_user" : {
         "type" : "boolean"
       },
       "is_political_party_tagged" : {
         "type" : "boolean"
       },
       "liked_user_ids" : {
         "type" : "keyword"
       },
       "likes_count" : {
         "type" : "long"
       },
       "opinions_count" : {
         "type" : "long"
       },
       "parent_post_id" : {
         "type" : "keyword"
       },
       "post_user_followers" : {
         "type" : "keyword"
       },
       "tagged_circles_ids" : {
         "type" : "keyword"
       },
       "tagged_district_circle_ids" : {
         "type" : "keyword"
       },
       "tagged_interest_circle_ids" : {
         "type" : "keyword"
       },
       "tagged_mandal_circle_ids" : {
         "type" : "keyword"
       },
       "tagged_mla_constituency_circle_ids" : {
         "type" : "keyword"
       },
       "tagged_mp_constituency_circle_ids" : {
         "type" : "keyword"
       },
       "tagged_state_circle_ids" : {
         "type" : "keyword"
       },
       "tagged_village_circle_ids" : {
         "type" : "keyword"
       },
       "trends_timestamps" : {
         "type" : "long"
       },
       "user_district_id" : {
         "type" : "keyword"
       },
       "user_id" : {
         "type" : "keyword"
       },
       "user_mandal_id" : {
         "type" : "keyword"
       },
       "user_mla_constituency_id" : {
         "type" : "keyword"
       },
       "user_mp_constituency_id" : {
         "type" : "keyword"
       },
       "user_state_id" : {
         "type" : "keyword"
       },
       "user_village_id" : {
         "type" : "keyword"
       },
       "whatsapp_count" : {
         "type" : "long"
       }
     }
   }
 }'

curl -XPUT --insecure -u admin:admin 'http://praja-opensearch:9200/development_search_entities_v2' -H 'Content-Type: application/json' -d '{
  "mappings" : {
    "properties" : {
      "active" : {
        "type" : "keyword"
      },
      "affiliated_political_party_id" : {
        "type" : "keyword"
      },
      "badge" : {
        "type" : "keyword"
      },
      "badge_grade_level" : {
        "type" : "integer"
      },
      "badge_location_circle_id" : {
        "type" : "keyword"
      },
      "circle_ids" : {
        "type" : "keyword"
      },
      "circle_type" : {
        "type" : "keyword"
      },
      "district_id" : {
        "type" : "keyword"
      },
      "followers_count" : {
        "type" : "long"
      },
      "id" : {
        "type" : "keyword"
      },
      "level" : {
        "type" : "keyword"
      },
      "mandal_id" : {
        "type" : "keyword"
      },
      "members_count" : {
        "type" : "long"
      },
      "mla_constituency_id" : {
        "type" : "keyword"
      },
      "mp_constituency_id" : {
        "type" : "keyword"
      },
      "name" : {
        "type" : "search_as_you_type",
        "doc_values" : false,
        "max_shingle_size" : 3
      },
      "name_en" : {
        "type" : "search_as_you_type",
        "doc_values" : false,
        "max_shingle_size" : 3
      },
      "phone" : {
        "type" : "search_as_you_type",
        "doc_values" : false,
        "max_shingle_size" : 3
      },
      "photo_url" : {
        "type" : "text"
      },
      "political_party" : {
        "type" : "keyword"
      },
      "posts_count_in_week" : {
        "type" : "integer"
      },
      "posts_count_with_10_trends" : {
        "type" : "integer"
      },
      "type" : {
        "type" : "keyword"
      },
      "village_id" : {
        "type" : "keyword"
      }
    }
  }
}'
