Subscription
  .joins("INNER JOIN (SELECT user_id, MAX(id) AS last_id
                                                   FROM subscriptions
                                                   WHERE status in ('active', 'cancelled', 'paused', 'on_hold')
                                                   GROUP BY user_id) latest ON subscriptions.user_id = latest.user_id
                                                                                 and subscriptions.id = latest.last_id")
  .where(status: [:cancelled, :paused])
  .select('subscriptions.user_id')
  .find_in_batches(batch_size: 1000) do |cancelled_users|

  user_ids = cancelled_users.map(&:user_id).uniq
  wati_campaign_key = []

  wati_trigger_key_users = UserMetadatum.where(user_id: user_ids, key: Constants.premium_reactivation_wati_campaign_count)
                                  .pluck(:user_id)

  final_users = user_ids - wati_trigger_key_users

  final_users.each do |user|
    wati_campaign_key << UserMetadatum.new(user_id: user,
                                           key: Constants.premium_reactivation_wati_campaign_count,
                                           value: 0)
  end

  UserMetadatum.import(wati_campaign_key, batch_size: 100)
end
