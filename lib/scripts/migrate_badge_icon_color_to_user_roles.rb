def test(last_id)
  failed_cases = []

  begin
    UserBadge.where(active: 1).where("id >= ?", last_id).each do |ub|
      puts "USER BADGE ID - #{ub.id}"

      role = Role.where(name: ub.badge_role.text).first

      user_role = UserRole.where(user_id: ub.user_id, primary_role: true).first

      if ub.badge_icon.present? && role.badge_icon_color != ub.get_badge_icon_color
        badge_icon_color = ub.get_badge_icon_color
      else
        next
      end

      begin
        user_role.update(badge_icon_color: badge_icon_color)
      rescue => e
        failed_cases << "#{ub.id}, #{ub.badge_role.text}, #{e}"
      end
    end
  rescue
    failed_cases.each do |c|
      puts c
    end
  end
end
