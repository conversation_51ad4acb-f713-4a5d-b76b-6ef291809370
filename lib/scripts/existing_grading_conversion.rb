old_grade_users = UserBadge.where('grade_level_old is not null')
grade_1 = []
grade_2 = []
grade_3 = []
grade_4 = []
grade_5 = []
old_grade_users.each do |ogu|
  case true
  when ogu.grade_level_old == "Grade-1"
    grade_1 << ogu.id
  when ogu.grade_level_old == "Grade-2"
    grade_2 << ogu.id
  when ogu.grade_level_old == "Grade-3"
    grade_3 << ogu.id
  when ogu.grade_level_old == "Grade-4"
    grade_4 << ogu.id
  when ogu.grade_level_old == "Grade-5"
    grade_5 << ogu.id
  end
end
UserBadge.where(id: grade_1).update(grade_level_new: 1)
UserBadge.where(id: grade_2).update(grade_level_new: 2)
UserBadge.where(id: grade_3).update(grade_level_new: 3)
UserBadge.where(id: grade_4).update(grade_level_new: 4)
UserBadge.where(id: grade_5).update(grade_level_new: 5)
