User.active.where(mla_constituency_id: nil).find_in_batches(batch_size: 500) do |users|
  users.each do |user|
    mandal_id = user.mandal_id
    mla_constituencies = CirclesRelation.where(first_circle_id: mandal_id, relation: "Mandal2MLA")
    if mla_constituencies.present? && mla_constituencies.count == 1
      mla_consitituency = mla_constituencies.first.second_circle
      user.mla_constituency_id = mla_consitituency.id
      user.mp_constituency_id = mla_consitituency.parent_circle_id
      user.save!
    end
  end
end

# Make mla constituency id nil for corporation users whose mandal has multiple mla mappings
mandal_ids = Circle.where(level: :corporation).pluck(:parent_circle_id).uniq!
mandals_with_multiple_relations = CirclesRelation.where(first_circle_id: mandal_ids, relation: 'Mandal2MLA')
                                                 .group(:first_circle_id)
                                                 .having('COUNT(*) > 1')
                                                 .count
                                                 .keys
User.active.where(mandal_id: mandals_with_multiple_relations).where.not(mla_constituency_id: nil).update(mla_constituency_id: nil, mp_constituency_id: nil)

