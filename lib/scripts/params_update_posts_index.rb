posts = ES_CLIENT.search index: EsUtil.get_new_posts_index, body: {
  "query": {
    "bool": {
      "filter": [
        {
          "bool": {
            "must_not": {
              "exists": {
                "field": "is_political_party_tagged"
              }
            }
          }
        }
      ]
    }
  },
  "size": 300
}

count = 0
posts['hits']['hits'].map do |hit|
  post_id = hit['_source']['id']
  count += 1
  IndexPostNew.perform_async(post_id)
end
puts count
