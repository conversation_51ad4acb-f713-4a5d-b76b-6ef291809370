# date = (Time.zone.today - 3.days).to_datetime
#
# Post.where(active: true).where('created_at > ?', date).find_in_batches(batch_size: 250) do |group|
#   group.each do |p|
#     IndexPostNew.perform_async(p.id)
#   end
#   sleep(1)
# end

#re index failed posts
Post.where(active: true).where('created_at > ?', 1.hour.ago).find_in_batches(batch_size: 250) do |group|
  group.each do |p|
    IndexPostNewV2.perform_async(p.id)
  end
  sleep(1)
end
