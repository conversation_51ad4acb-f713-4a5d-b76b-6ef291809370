upl_users = UserPosterLayout.where(status: :active, entity_type: 'user').pluck(:entity_id)
party_users = Metadatum.where(key: Constants.poster_affiliated_party_key, entity_type: 'user').pluck(:entity_id)
no_party_users = upl_users - party_users

# update the affiliated party to each badge user
badge_users_ids = []
no_party_users.each_slice(10) do |no_party_users_batch|
  badge_users = UserRole.where(active: true, user_id: no_party_users_batch)
  badge_users_ids.concat(badge_users.pluck(:user_id))

  badge_users.in_batches(of: 5) do |badge_users_batch|
    valid_affiliated_party_ids = badge_users_batch.map do |user_role|
      circle_id = user_role.get_badge_user_affiliated_party_circle_id
      circle_id unless circle_id == 0
    end.compact

    badge_users_batch.each_with_index do |user_role, index|
      affiliated_party_id = valid_affiliated_party_ids[index]
      next unless affiliated_party_id
      metadatum = Metadatum.find_or_initialize_by(
        entity_id: user_role.user_id,
        entity_type: 'User',
        key: Constants.poster_affiliated_party_key
      )
      metadatum.value = affiliated_party_id
      metadatum.save!
    end
  end
end

# update the affiliated party to each non badge user from the last poster creative share of info creative kind
no_badge_users = no_party_users - badge_users_ids

poster_share_user_ids = []
no_badge_users.each_slice(10) do |no_badge_users_batch|
  poster_share_circle_id = PosterShare.joins(poster_creative: :poster_creative_circles)
                                      .where(poster_creatives: { creative_kind: :info })
                                      .where(user_id: no_badge_users_batch)
                                      .select('poster_shares.user_id, poster_creative_circles.circle_id, MAX(poster_shares.created_at)')
                                      .group('poster_shares.user_id')
                                      .map { |record| [record.user_id, record.circle_id] }

  poster_share_circle_id.each do |user_id, circle_id|
    next unless circle_id

    metadatum = Metadatum.find_or_initialize_by(
      entity_id: user_id,
      entity_type: 'User',
      key: Constants.poster_affiliated_party_key
    )
    metadatum.value = circle_id
    metadatum.save!
  end

  poster_share_user_ids.concat(poster_share_circle_id.map { |user_id, _| user_id })
end

# update the affiliated party to each non badge user exclusive party joined
exclusive_users = no_badge_users - poster_share_user_ids

exclusive_users.each_slice(10) do |exclusive_users_batch|
  valid_exclusive_party_ids = exclusive_users_batch.map do |user_id|
    user = User.find(user_id)
    exclusive_party_id = user.user_exclusive_political_party
    exclusive_party_id unless exclusive_party_id.nil?
  end.compact

  exclusive_users_batch.each_with_index do |user_id, index|
    exclusive_party_id = valid_exclusive_party_ids[index]
    next unless exclusive_party_id

    metadatum = Metadatum.find_or_initialize_by(
      entity_id: user_id,
      entity_type: 'User',
      key: Constants.poster_affiliated_party_key
    )
    metadatum.value = exclusive_party_id
    metadatum.save!
  end
end
