# before running delete all user recommended frames
max_count = 10
UserPosterLayout.where(active: true).each do |user_poster_layout|
  user = user_poster_layout.entity if user_poster_layout.entity.is_a?(User)
  if user.present?
    user.create_frame_recommendations

    user_orders = user.orders.where(status: [:opened, :pending, :successful, :last_transaction_failed])
    if user_orders.present?
      # pick only premium frames from user recommended frames and shuffle them
      recommended_frames = UserRecommendedFrame.joins(:frame).where(user_id: user.id)
                                               .where(frames: { frame_type: :premium })
                                               .sample(20)
      recommended_frame_ids = recommended_frames.pluck(:frame_id)

      user_orders.each do |order|
        # pick only premium frames from order items
        order_items = order.order_items
        # check if order has poster product
        if order_items.present? && order_items.where(item_type: :Product, item_id: Constants.get_poster_product_id).exists?
          product_order_item = order_items.where(item_type: :Product, item_id: Constants.get_poster_product_id).first
          if product_order_item.duration_in_months > 3
            # pick frame ids from order_items and don't add those frame ids again to order items
            frame_ids = order_items.where(item_type: :Frame).pluck(:item_id)
            premium_frame_ids = Frame.where(frame_type: :premium, id: frame_ids).where.not(id: [5, 15]).pluck(:id)
            selected_frame_ids = recommended_frame_ids - premium_frame_ids
            selected_frame_ids = selected_frame_ids.first(max_count - premium_frame_ids.count)
            # add recommended frames to order items
            selected_frame_ids.each do |frame_id|
              OrderItem.create(item_type: :Frame, item_id: frame_id,
                               order_id: order.id,
                               parent_order_item_id: product_order_item.id,
                               duration_in_months: product_order_item.duration_in_months,
                               total_item_price: 0)
            end
          end
          # add user product subscription
          user_product_subscription = UserProductSubscription.where(user_id: user.id, order_id: order.id,
                                                                    item_type: :Product,
                                                                    item_id: Constants.get_poster_product_id,
                                                                    source: :orders).first
          if user_product_subscription.present?
            ordered_frame_ids = order.order_items.where(item_type: :Frame).pluck(:item_id)
            premium_frame_ids = Frame.where(frame_type: :premium, id: ordered_frame_ids).pluck(:id)
            user_subscribed_frame_ids = UserProductSubscription.where(user_id: user.id, order_id: order.id,
                                                                      item_type: :Frame,
                                                                      source: :orders).pluck(:item_id)
            not_subscribed_frame_ids = premium_frame_ids - user_subscribed_frame_ids
            not_subscribed_frame_ids.each do |frame_id|
              UserProductSubscription.create(user_id: user.id, order_id: order.id,
                                             item_type: :Frame, item_id: frame_id,
                                             start_date: user_product_subscription.start_date,
                                             end_date: user_product_subscription.end_date,
                                             active: true,
                                             source: :orders)
            end
          end
        end
      end
    end
  end
end; 0

UserProductSubscription.where(active: true, source: :referral).group_by(&:order_id).each do |order_id, user_product_subscriptions|
  user_id = user_product_subscriptions.first.user_id
  start_date = user_product_subscriptions.first.start_date
  end_date = user_product_subscriptions.first.end_date
  frame_ids = []
  user_product_subscriptions.each do |user_product_subscription|
    if user_product_subscription.item_type == 'Frame'
      frame_ids << user_product_subscription.item_id
    end
  end
  last_subscription = SubscriptionUtils.get_last_ordered_poster_package(user_id)
  if last_subscription.present?
    last_subscription.order.order_items.where.not(parent_order_item_id: nil).each do |order_item|
      if frame_ids.exclude?(order_item.item_id)
        UserProductSubscription.create(user_id: user_id, order_id: order_id,
                                       item_type: :Frame, item_id: order_item.item_id,
                                       start_date: start_date,
                                       end_date: end_date,
                                       active: true,
                                       source: :referral)
      end
    end
  end
end
