class AddPoliticianCircleImage
  include Sidekiq::Worker
  sidekiq_options retry: 0

  def perform
    user = User.find(15092)
    resource = Aws::S3::Resource.new(
      region: 'ap-south-1',
      credentials: Aws::Credentials.new(
          Rails.application.credentials[:aws_access_key_id],
          Rails.application.credentials[:aws_secret_access_key]
      ),
    )
    bucket = resource.bucket(Rails.application.credentials[:aws_s3_bucket_name])

    Dir.chdir("app/workers/TG_AP") do
      Dir.glob('*').select do |f|
        if File.directory? f
          type = :mp_constituency
          if f.to_s == "MLA"
            type = :mla_constituency
          end

          Dir.entries(f).each do |file|
            if file == "." || file == ".." || file == ".DS_Store"
              next
            end
            constituency_name = file.split(".").shift

            circle = Circle.where(name_en: constituency_name, circle_type: :location, level: type).first
            if circle.nil?
              logger.warn("FAILED: " + f.to_s + " - " + constituency_name)
            else
              relation = CirclesRelation.where(relation: f.to_s, first_circle: circle).first
              if relation.nil?
                logger.warn("FAILED R: " + f.to_s + " - " + constituency_name)
              else
                person = relation.second_circle

                unless person.photo.nil?
                  logger.warn("PHOTO EXISTS: " + person.name_en)
                  next
                end

                file_extension = File.extname(file)
                hashed_file_name = f.to_s.downcase + "-" + constituency_name.downcase + '-'+ person.name_en.strip.downcase.gsub(".", "").gsub(" ", "-") + file_extension.downcase

                s3_object_path = Rails.env + '/politician-photos/' + hashed_file_name

                begin
                  obj = bucket.object(s3_object_path)
                  obj.upload_file(f.to_s + "/" + file)

                  photo = Photo.create!(
                    url: 'https://cdn.thecircleapp.in/' + s3_object_path,
                    user: user,
                    service: :aws
                  )

                  person.photo = photo
                  person.save
                rescue => exception
                  logger.warn("FAILED HERE " + exception.message)
                end
              end
            end
          end
        end
      end
    end
  end
end