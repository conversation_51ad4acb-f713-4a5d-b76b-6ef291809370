class CreateSubAccounts
  include Sidekiq::Worker
  sidekiq_options retry: 0

  ACCOUNTS_TO_CREATE = 4

  def perform(*args)
    if args.count <= 0
      return
    end

    data = args[0]
    user_group_id = args[1].to_i

    phone = data["NO"].to_i

    (1..ACCOUNTS_TO_CREATE).each do |i|
      sub_data = data
      sub_data['NO'] = phone + i
      sub_data['NAME'] = User.get_random_name

      sub_user = create_sub_user(sub_data)

      if sub_user.nil?
        logger.warn("++++ SUB-USER CREATE FAILED ++++")
        logger.warn("PHONE = #{sub_data['NO']}; NAME = #{sub_data['NAME']}")
        next
      end

      begin
        UserGroupMember.create(user_group_id: user_group_id, user: sub_user)
      rescue => exception
        logger.error(exception.message.to_s)
        logger.warn("++++ GROUP CREATE FAILED ++++")
        logger.warn("PHONE = #{sub_data['NO']}; NAME = #{sub_data['NAME']}; GROUP ID = #{user_group_id}")
        next
      end
    end
  end

  def create_sub_user(data)
    phone = data["NO"].to_i
    user = User.find_by_phone(phone)

    if user.nil?
      user = User.create(name: data['NAME'], phone: phone, status: :active)
    end

    if user.get_location_circles.count > 0
      return user
    end

    if !data['HC DIST'].nil? && data['HC DIST'] != ""
      district = Circle.where(name_en: data['HC DIST'], circle_type: :location, level: :district).first
      if district.nil?
        logger.warn("++++ HC DIST FAILED ++++")
        logger.warn("PHONE = #{phone}; NAME = #{data['NAME']}; HC DIST = #{data['HC DIST']}")
        return nil
      end

      mandals = district.child_circles
      mandal = mandals.sample

      circle = Circle.where(name_en: mandal.name_en, circle_type: :location, level: :village).first
      if circle.nil?
        logger.warn("++++ VILLAGE FIND FAILED ++++")
        logger.warn("PHONE = #{phone}; NAME = #{data['NAME']}; HC DIST = #{data['HC DIST']}; MANDAL = #{mandal.name_en}")

        villages = district.get_all_village_circles
        circle = villages.sample
      end

      begin
        user.circles << circle
      rescue => e
        logger.warn("++++ CIRCLE ADD FAILED ++++")
        logger.warn("PHONE = #{user.phone}; CIRCLE NAME EN = #{circle.name_en}")
        # byebug

      end
    else
      logger.warn("++++ NO HC ++++")
      logger.warn("PHONE = #{phone}; NAME = #{data['NAME']}")
      return nil
    end

    user.village_id = circle.id
    user.populate_location
    user.save!

    return user
  end
end
