class AccountsScript
  include Sidekiq::Worker
  sidekiq_options retry: 0

  def perform
    guntur_district_id = 13_286
    mandal_ids = Circle.where(parent_circle_id: guntur_district_id).pluck(:id)

    starting_number = **********
    i = 0
    while i < 10
      mandal_id = mandal_ids.delete_at(rand(mandal_ids.length))
      mandal = Circle.find mandal_id

      village_ids = mandal.get_all_child_circle_ids
      village_id = village_ids.delete_at(rand(village_ids.length))

      u = User.create(name: User.get_random_name, phone: starting_number, status: :active, internal: true)
      u.circles << Circle.find(village_id)

      ug = UserGroup.create(user: u, name: "Guntur #{i + 1}")

      sub_account_starting_number = starting_number + 1
      j = 0
      while j < 10
        u_sub = User.create(name: User.get_random_name, phone: sub_account_starting_number, status: :active, internal: true)
        UserGroupMember.create(user_group: ug, user: u_sub)
        sub_account_starting_number += 1
        j += 1
      end

      starting_number += 100
      i += 1
    end
  end
end
