require 'net/http'

class AndhraGetMandalVillagesWorker
  include Sidekiq::Worker
  sidekiq_options retry: 0

  def perform(*args)
    if args.length <= 0
      return
    end

    district_id = args[0].to_i
    mandal_id = args[1].to_i
    id = args[2].to_i

    requested_url = "https://indikosh.com/my-ind/search/28/#{district_id}/#{mandal_id}/places.csv"

    uri = URI.parse(requested_url)
    res = Net::HTTP.post_form(uri, {})

    body = res.body.to_s

    if res.code == "200"
      parsed_body = Nokogiri::HTML.parse(body)

      opt_groups = parsed_body.children.last.children.last.children
      villages_html = opt_groups[opt_groups.count - 2].children

      villages = []
      villages_html.map do |village|
        if village == "\n"
          next
        end

        string = village.children.first.to_s
        if string != "" && !string.nil? && !string.empty?
          village_obj = {}
          village_obj['name_en'] = string
          village_obj

          villages << village_obj
        end
      end

      villages = villages.reject { |v| v.nil? || v.empty? }

      names = []
      villages.each do |village|
        village["name_en"] = village["name_en"].gsub("_", " ")
        name_dirty = village["name_en"]
        name_splits = name_dirty.split("#")
        name = name_splits[0]
        names << name

        village['name'] = name
        village['parent_circle_id'] = id
        village['level'] = :village
      end

      requested_url = 'http://www.google.com/inputtools/request?ime=transliteration_en_te&num=2&cp=0&cs=0&ie=utf-8&oe=utf-8&text='

      group_number = 0
      names.in_groups_of(30) do |name_group|
        uri = URI.parse(requested_url + name_group.join(","))
        g_res = Net::HTTP.get_response(uri)

        if g_res.code == "200"
          resp = JSON.parse(g_res.body)

          if resp[0] == "SUCCESS"
            resp[1].each_with_index do |e, i|
              index = group_number * 30 + i

              if !e[1].nil? && e[1].count > 1
                villages[index]['name'] = e[1][0]
              end

              # village_id = villages[index]['id']
              villages[index]['id'] = nil
              Circle.create(villages[index])
            end

            ## OTHERS
          end
        end

        group_number = group_number + 1
      end
    end
  end
end
