class AddCirclesRelation
  include Sidekiq::Worker
  sidekiq_options retry: 0

  def perform(*args)
    if args.length <= 0
      return
    end

    mapping = args[0]

    circle_1 = mapping['circle_1']
    circle_2 = mapping['circle_2']
    relation = mapping['relation']

    if circle_1.nil? || circle_2.nil? || relation.empty?
      return
    end

    level1 = circle_level(circle_1)
    type1 = circle_type(circle_1)
    level2 = circle_level(circle_2)
    type2 = circle_type(circle_2)

    c1 = Circle.where(name_en: circle_1['name_en'], circle_type: type1, level: level1, active: true).first
    c2 = Circle.where(name_en: circle_2['name_en'], circle_type: type2, level: level2, active: true).first
    if c1.nil? || c2.nil?
      return
    end

    circleRelation=CirclesRelation.where(first_circle_id: c1.id, second_circle_id: c2.id, relation: relation).first

    if circleRelation.nil?
      CirclesRelation.create(first_circle: c1, second_circle: c2, relation: relation)
    end
  end

  def circle_level(ci)
    level = nil
    if ci['type'] == 'district'
      level = :district
    elsif ci['type'] == 'mandal'
      level = :mandal
    elsif ci['type'] == 'village'
      level = :village
    elsif ci['type'] == 'mp_constituency'
      level = :mp_constituency
    elsif ci['type'] == 'mla_constituency'
      level = :mla_constituency
    elsif ci['type'] == 'political_leader'
      level = :political_leader
    elsif ci['type'] == 'political_party'
      level = :political_party
    end

    level
  end

  def circle_type(ci)
    type = nil
    if ci['type'] == 'district' || ci['type'] == 'mandal' || ci['type'] == 'village' || ci['type'] == 'mp_constituency' || ci['type'] == 'mla_constituency'
      type = :location
    elsif ci['type'] == 'political_leader' || ci['type'] == 'political_party'
      type = :interest
    end

    type
  end
end