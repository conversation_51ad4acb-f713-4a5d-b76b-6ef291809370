class ConstituencyData
  include Sidekiq::Worker
  sidekiq_options retry: 0

  def perform
    constituencies = []
    mps = []
    mlas = []
    mp_count = 0
    mp_constituency_count = 0
    mla_count = 0
    mla_constituency_count = 0
    dup_count = 0
    m_dup_count = 0

    mappings = []

    csv_row_data = CSV.read(
        "app/workers/tg_constituencies.csv",
        {headers: :first_row, skip_blanks: true}
    )

    csv_row_data.each do |row|
      mp_constituency_index = constituencies.index(constituencies.find { |l| l[:name_en] == row["MP CONSTITUENCY"] })

      mp_constituency = nil
      if mp_constituency_index.nil? || mp_constituency_index == -1
        mp_constituency = {
            name: row["MP CONSTITUENCY TE"],
            name_en: row["MP CONSTITUENCY"],
            type: 'mp_constituency',
            children: []
        }

        mp_constituency_index = constituencies.count

        constituencies << mp_constituency
        mp_constituency_count = mp_constituency_count + 1
      else
        mp_constituency = constituencies[mp_constituency_index]
      end

      mp = nil
      mp_index = mps.index(mps.find { |l| l[:name_en] == row["MP NAME"] })
      if mp_index.nil? || mp_index == -1
        mp = {
            name: row["MP NAME TE"],
            name_en: row["MP NAME"],
            type: 'political_leader',
            children: []
        }

        mp_index = mps.count

        mps << mp
        mp_count = mp_count + 1
      else
        mp = mps[mp_index]
      end

      if !mp_constituency.nil? && !mp.nil?
        mappings << {circle_1: mp_constituency, circle_2: mp, relation: "MP"}
      end

      mla_constituency = nil
      mla_constituency_index = constituencies[mp_constituency_index][:children].index(constituencies[mp_constituency_index][:children].find { |l| l[:name_en] == row["MLA CONSTITUENCY"] })
      if mla_constituency_index.nil? || mla_constituency_index == -1
        mla_constituency = {
            name: row["MLA CONSTITUENCY TE"],
            name_en: row["MLA CONSTITUENCY"],
            type: 'mla_constituency'
        }

        mla_constituency_index = constituencies[mp_constituency_index][:children].count

        constituencies[mp_constituency_index][:children] << mla_constituency
        mla_constituency_count = mla_constituency_count + 1
      else
        mla_constituency = constituencies[mp_constituency_index][:children][mla_constituency_index]
      end

      mla = nil
      mla_index = mlas.index(mlas.find { |l| l[:name_en] == row["MLA NAME"] })
      if mla_index.nil? || mla_index == -1
        mla = {
            name: row["MLA NAME TE"],
            name_en: row["MLA NAME"],
            type: 'political_leader'
        }

        mla_index = mlas.count

        mlas << mla
        mla_count = mla_count + 1
      else
        mla = mlas[mla_index]
      end

      if !mla_constituency.nil? && !mla.nil?
        mappings << {circle_1: mla_constituency, circle_2: mla, relation: "MLA"}
      end
    end

    count = 0
    constituencies.each do |d|
      count = count + 1
      count = count + d[:children].count
    end

    send = true

    if send
      constituencies.each do |d|
        AddCirclesData.perform_async(d)
      end
      mps.each do |d|
        AddCirclesData.perform_async(d)
      end
      mlas.each do |d|
        AddCirclesData.perform_async(d)
      end
      mappings.each do |mapping|
        AddCirclesRelation.perform_in(1.minutes, mapping)
      end
    end
  end
end