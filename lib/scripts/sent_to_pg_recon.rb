# frozen_string_literal: true


sc_not_found = []
errors = []

user_ids.each do |user_id|
  s = Subscription.where(user_id: user_id, status: [:active, :on_hold, :cancelled, :paused]).last
  if s.paused?
    errors << {user_id: user_id, order_id: nil, payment_gateway: s.payment_gateway, error_code: "Subscription Paused"}
    next
  end

  if s.cancelled?
    errors << {user_id: user_id, order_id: nil, payment_gateway: s.payment_gateway, error_code: "Subscription Cancelled"}
    next
  end

  sc = SubscriptionCharge.where(user_id: user_id, status: [:failed, :success], subscription: s).last

  if sc.present?
    if sc.failed?
      if sc.subscription.juspay?
        errors << {user_id: user_id, order_id: sc.pg_id, payment_gateway: sc.subscription.payment_gateway, error_code: sc.pg_json.dig("juspay", "content", "order", "txn_detail", "response_code")}
      else
        errors << {user_id: user_id, order_id: sc.pg_id, payment_gateway: sc.subscription.payment_gateway, error_code: sc.pg_json.dig("cf_reasons")}
      end
    elsif sc.success?
      errors << {user_id: user_id, order_id: sc.pg_id, payment_gateway: sc.subscription.payment_gateway, error_code: "Payment success later"}
    end
  else
    sc_not_found << user_id
  end
end;0
