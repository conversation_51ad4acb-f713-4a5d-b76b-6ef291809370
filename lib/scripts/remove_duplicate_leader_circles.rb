# duplicate leader circles clean up
require 'csv'

leader_circles = CSV.parse(File.read('./lib/scripts/duplicate_leader_circles.csv'), headers: true)

# take leader circles hash key as duplicate circle id and value as original circle id
leader_circles_hash = {}
leader_circles.each do |list|
  duplicate_circle_id = list["circle_id"].to_i
  original_circle_id = list["original_circle_id"].to_i
  leader_circles_hash.store(duplicate_circle_id, original_circle_id)
end

# post circles clean up
leader_circles_hash.each do |duplicate, original|
  # all posts tagged to duplicate circle
  duplicate_circle_tagged_post_ids = PostCircle.where(circle_id: duplicate).pluck(:post_id)

  # posts tagged to both duplicate and original circle
  both_circles_tagged_post_ids = PostCircle.where(post_id: duplicate_circle_tagged_post_ids, circle_id: original).pluck(:post_id)

  # delete duplicate post circles for the posts with both circles tagged
  PostCircle.where(post_id: both_circles_tagged_post_ids, circle_id: duplicate).destroy_all

  # update post circles with duplicate circle tagged to original circle tagged
  only_duplicate_circle_tagged_post_ids = duplicate_circle_tagged_post_ids - both_circles_tagged_post_ids
  PostCircle.where(post_id: only_duplicate_circle_tagged_post_ids, circle_id: duplicate).update(circle_id: original)

  updated_post_ids = both_circles_tagged_post_ids + only_duplicate_circle_tagged_post_ids
  updated_post_ids.each do |post_id|
    Rails.cache.delete([Post::CACHE_KEY, post_id])
  end
end

# members clean up
leader_circles_hash.each do |duplicate, original|
  # all users joined in duplicate circle
  duplicate_circle_joined_user_ids = UserCircle.where(circle_id: duplicate).pluck(:user_id)

  # same users already joined in original circle
  both_circles_joined_user_ids = UserCircle.where(user_id: duplicate_circle_joined_user_ids, circle_id: original).pluck(:user_id)

  # delete duplicate user circles for users who already joined in original circle
  UserCircle.where(user_id: both_circles_joined_user_ids, circle_id: duplicate).destroy_all

  # update duplicate circle to original circle for users who only joined in duplicate circle
  only_joined_duplicate_circle_user_ids = duplicate_circle_joined_user_ids - both_circles_joined_user_ids
  UserCircle.where(user_id: only_joined_duplicate_circle_user_ids, circle_id: duplicate).update(circle_id: original)

end

leader_circles_hash.each do |duplicate, original|
  # delete poster creative circles
  PosterCreativeCircle.where(circle_id: duplicate).find_each do |pcc|
    if PosterCreativeCircle.where(poster_creative_id: pcc.poster_creative_id, circle_id: original).exists?
      pcc.destroy
    else
      pcc.update(circle_id: original)
    end
  end
end

# post bounces clean up
PostBounce.where(circle_id: leader_circles_hash.keys).destroy_all

# circles relations clean up #optional
CirclesRelation.where(first_circle_id: leader_circles_hash.keys).destroy_all
CirclesRelation.where(second_circle_id: leader_circles_hash.keys).destroy_all

# delete circle photos
CirclePhoto.where(circle_id: leader_circles_hash.keys).destroy_all

CircleMonthlyUsage.where(circle_id: leader_circles_hash.keys).destroy_all

CirclePackageMapping.where(circle_id: leader_circles_hash.keys).delete_all

CirclePremiumInterest.where(circle_id: leader_circles_hash.keys).destroy_all

# delete duplicate circles
Circle.where(id: leader_circles_hash.keys).destroy_all
