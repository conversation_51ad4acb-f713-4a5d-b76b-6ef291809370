UserCircle.joins(:circle).where(circles: {level: :village}).where(user_id: user_ids).each do |uc|
  current_village = uc.circle
  current_mandal = current_village.parent_circle
  other_village_in_mandal = Circle.where(name_en: "Other", level: :village, parent_circle: current_mandal).first
  uc.circle_id = other_village_in_mandal.id
  uc.save!
end;0

UserCircle.joins(:circle).where(circles: {level: :village}).where(user_id: user_ids).each do |uc|
  puts "#{uc.user_id}, #{uc.circle_id}"
end;0

