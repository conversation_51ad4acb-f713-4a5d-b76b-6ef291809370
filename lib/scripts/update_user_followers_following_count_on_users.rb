uid = $redis.get('indexed_last_user_id_for_followers_and_following_count_update').to_i
user_id = nil
count = 0
User.where(id: uid..uid + 10000).each do |user|
  user_id = user.id
  total_following_count = UserFollower.where(follower_id: user_id, active: true).count
  total_followers_count = UserFollower.where(user_id: user_id, active: true).count
  user.update_columns(total_followers_count: total_followers_count, total_following_count: total_following_count)
  count += 1
  sleep 1 if (count % 500).zero?
end

$redis.set('indexed_last_user_id_for_followers_and_following_count_update', user_id.to_s) if count > 0
