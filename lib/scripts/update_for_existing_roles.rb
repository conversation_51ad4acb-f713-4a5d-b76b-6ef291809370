Role.where(active: 1).each do |role|
  badge_role = BadgeRole.where(text: role.name).first
  if badge_role.political_party_role?
    if role.interest_primary_circle_type?
      role.update(badge_icon: :primary_circle_id)
    else
      role.update(primary_circle_type: :interest, primary_circle_level: :political_party, badge_icon: :primary_circle_id,
                  secondary_circle_type: role.primary_circle_type, secondary_circle_level: role.primary_circle_level,
                  primary_circle_to_badge_text: false, secondary_circle_to_badge_text: true)
    end
  end
end